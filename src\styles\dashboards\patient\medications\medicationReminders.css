.container {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.listContent {
  padding-bottom: 16px;
}

.reminderItem {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.reminderHeader {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 8px;
}

.statusIndicator {
  width: 12px;
  height: 12px;
  border-radius: 6px;
  margin-right: 8px;
}

.medicationName {
  font-size: 16px;
  font-weight: bold;
  color: #212121;
  flex: 1;
}

.dosage {
  font-size: 14px;
  color: #616161;
  margin-left: 8px;
}

.reminderDetails {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.timeContainer {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.timeText {
  font-size: 14px;
  color: #757575;
  margin-left: 4px;
}

.statusText {
  font-size: 14px;
  font-weight: 500;
}

.instructions {
  font-size: 14px;
  color: #757575;
  margin-bottom: 12px;
}

.actionButtons {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

.actionButton {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 8px 12px;
  border-radius: 4px;
  flex: 1;
  margin: 0 4px;
  cursor: pointer;
}

.takenButton {
  background-color: #4CAF50;
}

.skipButton {
  background-color: #F44336;
}

.actionButtonText {
  color: #fff;
  font-size: 14px;
  font-weight: 500;
  margin-left: 4px;
}

.loadingContainer {
  padding: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.loadingText {
  font-size: 14px;
  color: #757575;
  margin-top: 8px;
}

.emptyContainer {
  padding: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.emptyText {
  font-size: 16px;
  color: #757575;
  margin-top: 8px;
}

.errorContainer {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 16px;
  background-color: #FFEBEE;
  border-radius: 8px;
  margin: 16px;
}

.errorText {
  font-size: 14px;
  color: #D32F2F;
  margin-left: 8px;
  flex: 1;
}

.sectionHeader {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 12px 8px;
  background-color: #E3F2FD;
  border-radius: 8px;
  margin-bottom: 12px;
}

.timeHeaderContainer {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.timeHeader {
  font-size: 16px;
  font-weight: bold;
  color: #4285F4;
  margin-left: 8px;
}

.medicationCount {
  font-size: 14px;
  color: #757575;
  font-style: italic;
}
