.container {
  flex: 1;
  background-color: #f5f7fa;
  min-height: 100vh;
  padding: 20px;
}

.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

.card {
  margin: 12px;
  border-radius: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  background-color: transparent;
  overflow: hidden;
}

.cardGradient {
  border-radius: 16px;
  background: linear-gradient(to bottom right, #f5f7fa, #ffffff);
  padding: 16px;
}

.cardHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.dateContainer {
  flex: 1;
}

.dateTitle {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.statusContainer {
  display: flex;
  align-items: center;
  margin-top: 4px;
}

.statusDot {
  width: 8px;
  height: 8px;
  border-radius: 4px;
  margin-right: 6px;
}

.statusText {
  font-size: 12px;
  font-weight: bold;
}

.avatarIcon {
  margin-left: 8px;
  width: 40px;
  height: 40px;
  border-radius: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.cardDivider {
  height: 1px;
  margin: 12px 0;
  background-color: #e0e0e0;
}

.cardDetails {
  margin-top: 8px;
}

.detailRow {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.detailIcon {
  margin-right: 8px;
}

.detailText {
  font-size: 14px;
  color: #555;
  flex: 1;
}

.cardFooter {
  margin-top: 12px;
  display: flex;
  justify-content: flex-end;
}

.viewDetailsButton {
  display: flex;
  align-items: center;
  padding: 6px 2px;
  cursor: pointer;
}

.viewDetailsText {
  color: #4CAF50;
  font-weight: bold;
  margin-right: 4px;
  font-size: 14px;
}

.modal {
  background-color: white;
  padding: 20px;
  margin: 20px auto;
  border-radius: 16px;
  max-height: 80vh;
  max-width: 600px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  overflow-y: auto;
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.modalTitleContainer {
  flex: 1;
}

.modalTitle {
  font-size: 20px;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
}

.modalStatusBadge {
  padding: 4px 12px;
  border-radius: 16px;
  align-self: flex-start;
  margin-top: 4px;
  display: inline-block;
}

.modalStatusText {
  font-size: 12px;
  font-weight: bold;
}

.closeButton {
  padding: 4px;
  cursor: pointer;
}

.divider {
  margin: 16px 0;
  height: 1px;
  background-color: #e0e0e0;
}

.modalDateTimeContainer {
  display: flex;
  justify-content: space-between;
  margin: 16px 0;
}

.modalDateContainer, .modalTimeContainer {
  flex: 1;
  display: flex;
  align-items: center;
}

.modalDateContainer {
  padding-right: 8px;
}

.modalTimeContainer {
  padding-left: 8px;
}

.modalIcon {
  margin-right: 12px;
}

.modalLabel {
  font-size: 12px;
  color: #757575;
  margin-bottom: 2px;
}

.modalDateValue, .modalTimeValue {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.modalDetailsSection {
  margin: 16px 0;
}

.modalDetailRow {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16px;
}

.modalDetailIcon {
  margin-right: 12px;
  margin-top: 2px;
}

.modalDetailTextContainer {
  flex: 1;
}

.modalDetailLabel {
  font-size: 12px;
  color: #757575;
  margin-bottom: 2px;
}

.modalDetailValue {
  font-size: 16px;
  color: #333;
}

.actionButtons {
  display: flex;
  justify-content: space-around;
  margin-top: 16px;
}

.actionButton {
  flex: 1;
  margin: 0 4px;
  border-radius: 8px;
  padding: 8px;
  cursor: pointer;
  border: none;
  color: white;
  font-weight: bold;
}

.confirmButton {
  background-color: #4CAF50;
}

.cancelButton {
  background-color: #F44336;
}

.completeButton {
  background-color: #2196F3;
}

.rescheduleButton {
  background-color: #9C27B0;
}

.noAppointments {
  text-align: center;
  margin: 40px 0;
  font-size: 16px;
  color: #757575;
}

.pagination {
  margin: 16px 0;
}

.paginationControls {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10px 0;
}

.paginationButton {
  padding: 8px 15px;
  background-color: #4CAF50;
  border-radius: 8px;
  margin: 0 5px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  border: none;
}

.paginationButtonDisabled {
  background-color: #CCCCCC;
  cursor: not-allowed;
}

.paginationButtonText {
  color: white;
  font-weight: bold;
}

.paginationText {
  margin: 0 10px;
  font-size: 14px;
  color: #555;
}
