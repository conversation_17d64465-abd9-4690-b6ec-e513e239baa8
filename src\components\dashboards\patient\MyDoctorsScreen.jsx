import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Ionicons } from '@expo/vector-icons';
import { usersAPI } from '../../../config/api';
import { PATIENT_COLORS, ROLE_COLORS } from '../../../config/theme';
import { db } from '../../../config/firebase';
import { doc, getDoc } from 'firebase/firestore';
import '../../../styles/dashboards/patient/myDoctorsScreen.css';

const DoctorCard = ({ doctor, onRequestAppointment, onStartVideoCall }) => {
  const [showDetails, setShowDetails] = useState(false);

  // Extract first and last name from displayName
  const nameParts = doctor.displayName ? doctor.displayName.split(' ') : ['', ''];
  const firstName = nameParts[0] || '';
  const lastName = nameParts.slice(1).join(' ') || '';

  // Create initials for avatar
  const initials = `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();

  const toggleDetails = () => {
    setShowDetails(!showDetails);
  };

  // Format address if available
  const formatAddress = () => {
    if (!doctor.address) return 'Address not available';

    // Handle both string and object address formats
    if (typeof doctor.address === 'string') {
      return doctor.address;
    }

    const { street, city, state, zipCode, country } = doctor.address;
    const parts = [];

    if (street) parts.push(street);
    if (city) parts.push(city);
    if (state) parts.push(state);
    if (zipCode && zipCode !== '...') parts.push(zipCode);
    else if (zipCode === '...') parts.push('Zip: ...');
    if (country) parts.push(country);

    return parts.join(', ') || 'Address not available';
  };

  return (
    <div className="doctorCard">
      <div
        className="cardContent"
        onClick={toggleDetails}
      >
        <div className="cardHeader">
          <div className="avatarContainer">
            {doctor.profileImage ? (
              <img src={doctor.profileImage} className="avatar" alt={`Dr. ${doctor.displayName}`} />
            ) : (
              <div className="avatarPlaceholder" style={{ backgroundColor: ROLE_COLORS.doctor.primary }}>
                <span className="avatarText">{initials}</span>
              </div>
            )}
          </div>

          <div className="doctorInfo">
            <h3 className="doctorName">{doctor.displayName ? `Dr. ${doctor.displayName}` : 'Doctor'}</h3>
            <p className="doctorSpecialty">
              {doctor.specialty === '...' ? 'Specialty: ...' : doctor.specialty}
            </p>

            {doctor.email && (
              <div className="contactInfo">
                <div className="contactItem">
                  <Ionicons name="mail" size={16} color={ROLE_COLORS.doctor.primary} />
                  <span className="contactText">{doctor.email}</span>
                </div>
              </div>
            )}
          </div>

          <Ionicons
            name={showDetails ? "chevron-up" : "chevron-down"}
            size={24}
            color={ROLE_COLORS.doctor.primary}
            className="expandIcon"
          />
        </div>

        {showDetails && (
          <div className="detailsContainer">
            <div className="detailSection">
              <div className="detailTitleContainer">
                <Ionicons name="location" size={16} color={ROLE_COLORS.doctor.primary} />
                <span className="detailTitle">Address</span>
              </div>
              <p className="detailText">{formatAddress()}</p>
            </div>

            {doctor.phoneNumber && (
              <div className="detailSection">
                <div className="detailTitleContainer">
                  <Ionicons name="call" size={16} color={ROLE_COLORS.doctor.primary} />
                  <span className="detailTitle">Phone</span>
                </div>
                <p className="detailText">{doctor.phoneNumber}</p>
              </div>
            )}

            {doctor.hospital && (
              <div className="detailSection">
                <div className="detailTitleContainer">
                  <Ionicons name="business" size={16} color={ROLE_COLORS.doctor.primary} />
                  <span className="detailTitle">Hospital</span>
                </div>
                <p className="detailText">{doctor.hospital}</p>
              </div>
            )}

            {doctor.education && (
              <div className="detailSection">
                <div className="detailTitleContainer">
                  <Ionicons name="school" size={16} color={ROLE_COLORS.doctor.primary} />
                  <span className="detailTitle">Education</span>
                </div>
                <p className="detailText">{doctor.education}</p>
              </div>
            )}

            {doctor.experience && (
              <div className="detailSection">
                <div className="detailTitleContainer">
                  <Ionicons name="briefcase" size={16} color={ROLE_COLORS.doctor.primary} />
                  <span className="detailTitle">Experience</span>
                </div>
                <p className="detailText">{doctor.experience} years</p>
              </div>
            )}

            {doctor.languages && doctor.languages.length > 0 && (
              <div className="detailSection">
                <div className="detailTitleContainer">
                  <Ionicons name="globe" size={16} color={ROLE_COLORS.doctor.primary} />
                  <span className="detailTitle">Languages</span>
                </div>
                <p className="detailText">{doctor.languages.join(', ')}</p>
              </div>
            )}

            {!doctor.hospital && !doctor.education &&
             !doctor.experience && (!doctor.languages || doctor.languages.length === 0) && (
              <div className="noDetailsContainer">
                <p className="noDetailsText">No additional details available</p>
              </div>
            )}
          </div>
        )}
      </div>

      <div className="actionsContainer">
        <button
          className="actionButton primaryActionButton"
          onClick={() => onRequestAppointment(doctor)}
        >
          <Ionicons name="calendar" size={22} color="#fff" />
          <span className="actionText primaryActionText">Request Appointment</span>
        </button>

        <div className="secondaryActionsRow">
          <button
            className="actionButton secondaryActionButton"
            onClick={() => onStartVideoCall(doctor)}
          >
            <Ionicons name="videocam" size={22} color={PATIENT_COLORS.primary} />
            <span className="actionText">Video Call</span>
          </button>

          <button
            className="actionButton secondaryActionButton"
          >
            <Ionicons name="chatbubble" size={22} color={PATIENT_COLORS.primary} />
            <span className="actionText">Message</span>
          </button>
        </div>
      </div>
    </div>
  );
};

const MyDoctorsScreen = () => {
  const navigate = useNavigate();
  const [doctors, setDoctors] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    fetchDoctors();
  }, []);

  const fetchDoctorsFromFirebase = async (doctorIds) => {
    try {
      const detailedDoctors = [];

      for (const doctorId of doctorIds) {
        // Get the doctor document from Firestore
        const doctorDoc = doc(db, 'users', doctorId);
        const doctorSnapshot = await getDoc(doctorDoc);

        if (doctorSnapshot.exists()) {
          const doctorData = doctorSnapshot.data();
          console.log('Raw doctor data:', doctorData);

          // Format the doctor data with only real data from Firebase
          const doctorInfo = {
            uid: doctorId
          };

          // Name fields
          if (doctorData.displayName) doctorInfo.displayName = doctorData.displayName;
          else if (doctorData.firstName || doctorData.lastName) {
            doctorInfo.displayName = `${doctorData.firstName || ''} ${doctorData.lastName || ''}`.trim();
          }

          if (doctorData.firstName) doctorInfo.firstName = doctorData.firstName;
          if (doctorData.lastName) doctorInfo.lastName = doctorData.lastName;

          // Contact information
          if (doctorData.email) doctorInfo.email = doctorData.email;
          // Check for phone number in different possible field names
          if (doctorData.phoneNumber) doctorInfo.phoneNumber = doctorData.phoneNumber;
          else if (doctorData.phone) doctorInfo.phoneNumber = doctorData.phone;
          else if (doctorData.tel) doctorInfo.phoneNumber = doctorData.tel;
          else if (doctorData.telephone) doctorInfo.phoneNumber = doctorData.telephone;
          else if (doctorData.mobile) doctorInfo.phoneNumber = doctorData.mobile;
          else if (doctorData.mobileNumber) doctorInfo.phoneNumber = doctorData.mobileNumber;

          // Specialty - add '...' if not available
          doctorInfo.specialty = doctorData.specialty || doctorData.speciality || '...';

          // Profile image
          if (doctorData.profileImage) doctorInfo.profileImage = doctorData.profileImage;

          // Address information - handle both string and object formats
          if (typeof doctorData.address === 'string') {
            doctorInfo.address = {
              street: doctorData.address
            };
          } else if (doctorData.address && typeof doctorData.address === 'object') {
            doctorInfo.address = doctorData.address;
          }

          // Add country, city if they exist directly in the doctor data
          if (doctorData.country) {
            if (!doctorInfo.address) doctorInfo.address = {};
            doctorInfo.address.country = doctorData.country;
          }

          if (doctorData.city) {
            if (!doctorInfo.address) doctorInfo.address = {};
            doctorInfo.address.city = doctorData.city;
          }

          // Add zipCode - set to '...' if not available
          if (doctorData.zipCode) {
            if (!doctorInfo.address) doctorInfo.address = {};
            doctorInfo.address.zipCode = doctorData.zipCode;
          } else {
            if (!doctorInfo.address) doctorInfo.address = {};
            doctorInfo.address.zipCode = '...';
          }

          // Other professional information
          if (doctorData.hospital) doctorInfo.hospital = doctorData.hospital;
          if (doctorData.education) doctorInfo.education = doctorData.education;
          if (doctorData.experience) doctorInfo.experience = doctorData.experience;
          if (doctorData.languages && doctorData.languages.length > 0) doctorInfo.languages = doctorData.languages;
          if (doctorData.bio) doctorInfo.bio = doctorData.bio;

          detailedDoctors.push(doctorInfo);
        }
      }

      return detailedDoctors;
    } catch (error) {
      console.error('Error fetching detailed doctor information:', error);
      throw error;
    }
  };

  const fetchDoctors = async () => {
    try {
      setLoading(true);

      // First, get the linked doctor IDs from the API
      const linkedDoctors = await usersAPI.getLinkedUsers('doctors');

      if (!linkedDoctors || linkedDoctors.length === 0) {
        console.log('No linked doctors found');
        setDoctors([]);
        return;
      }

      // Extract doctor IDs
      const doctorIds = linkedDoctors.map(doctor => doctor.uid);

      // Log the doctor IDs for debugging
      console.log('Doctor IDs to fetch:', doctorIds);

      // Fetch detailed information for each doctor from Firebase
      const detailedDoctors = await fetchDoctorsFromFirebase(doctorIds);

      console.log(`Fetched ${detailedDoctors.length} doctors with detailed information`);
      console.log('Doctor details:', JSON.stringify(detailedDoctors, null, 2));

      setDoctors(detailedDoctors);
    } catch (error) {
      console.error('Error fetching doctors:', error);
      // Use a web-based notification system here instead of react-native-flash-message
      alert('Failed to load your doctors. Please try again.');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleRefresh = () => {
    setRefreshing(true);
    fetchDoctors();
  };

  const renderEmptyList = () => (
    <div className="emptyContainer">
      <Ionicons name="medical" size={60} color="#ccc" className="emptyIcon" />
      <h3 className="emptyText">No doctors following you yet</h3>
      <p className="emptySubtext">
        When doctors add you as their patient, they will appear here
      </p>
    </div>
  );

  // Handle request appointment button press
  const handleRequestAppointment = (doctor) => {
    // Navigate to Appointments screen with parameter to open request modal
    navigate('/appointments', {
      state: {
        openRequestModal: true,
        selectedDoctorId: doctor.uid
      }
    });
  };

  // Handle start video call button press
  const handleStartVideoCall = (doctor) => {
    // Generate a unique room name
    const roomName = `neurocare_${doctor.uid}_${Date.now()}`;

    // Navigate to PatientVideoCall screen
    navigate('/patient-video-call', {
      state: {
        roomName: roomName,
        doctorInfo: doctor
      }
    });
  };

  return (
    <div className="container">
      {loading && !refreshing ? (
        <div className="loadingContainer">
          <div className="spinner"></div>
          <p className="loadingText">Loading your doctors...</p>
        </div>
      ) : (
        <div className={doctors.length === 0 ? "emptyListContent" : "listContent"}>
          {doctors.length === 0 ? (
            renderEmptyList()
          ) : (
            <>
              {refreshing && <div className="refreshIndicator">Refreshing...</div>}
              <button onClick={handleRefresh} className="refreshButton">
                <Ionicons name="refresh" size={20} color={PATIENT_COLORS.primary} />
                Refresh
              </button>
              
              {doctors.map((doctor) => (
                <DoctorCard
                  key={doctor.uid}
                  doctor={doctor}
                  onRequestAppointment={handleRequestAppointment}
                  onStartVideoCall={handleStartVideoCall}
                />
              ))}
            </>
          )}
        </div>
      )}
    </div>
  );
};

export default MyDoctorsScreen;
