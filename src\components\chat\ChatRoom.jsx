import React, { useState, useEffect, useRef } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { useNavigate, useLocation } from 'react-router-dom';
import { IoArrowBack, IoSend, IoChatbubble, IoPeople, IoDocumentText, IoVideocam, IoMic, IoMicOff, IoVideocamOff, IoPersonAdd, IoSave, IoMedical } from 'react-icons/io5';
import { ROLE_COLORS } from '../../config/theme';
import axios from 'axios';
import VideoCall from '../video/VideoCall';
import '../../styles/chat/chatRoom.css';

const ChatRoom = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const scrollViewRef = useRef();

  // State for UI and controls
  const [message, setMessage] = useState('');
  const [messages, setMessages] = useState([]);
  const [showChat, setShowChat] = useState(true);
  const [showParticipants, setShowParticipants] = useState(false);
  const [showMedicalNotes, setShowMedicalNotes] = useState(false);
  const [medicalNote, setMedicalNote] = useState('');
  const [consultationTime, setConsultationTime] = useState(0);
  const [callId, setCallId] = useState(null);
  const [showVideoCall, setShowVideoCall] = useState(false);
  const [isMicOn, setIsMicOn] = useState(true);
  const [isCameraOn, setIsCameraOn] = useState(true);
  const [roomName, setRoomName] = useState('');
  const [slideAnim, setSlideAnim] = useState(false);
  const [fadeAnim, setFadeAnim] = useState(false);

  // Get patient info and call parameters from location state
  const patientInfo = location.state?.patientInfo || {};
  const startVideoCall = location.state?.startVideoCall || false;
  const initialRoomName = location.state?.roomName;

  // Initialize chat session
  useEffect(() => {
    const initializeChat = async () => {
      try {
        // Check if user data is available
        if (!user || !user.uid) {
          console.error('User data is not available or incomplete');
          throw new Error('User data is not available');
        }

        // Create a unique chat ID using user.uid instead of user.id
        const chatId = `neurocare_chat_${user.uid}_${patientInfo.id}_${Date.now()}`;

        // Create a new chat record in the backend
        const chatResponse = await axios.post('/api/communication/create-chat', {
          doctorId: user.uid, // Use uid instead of id
          patientId: patientInfo.id,
          chatId: chatId
        });

        if (chatResponse.data && chatResponse.data.chatId) {
          setCallId(chatResponse.data.chatId);
        }

      } catch (error) {
        console.error('Error initializing chat:', error);

        // Provide more detailed error logging
        if (error.message === 'Network Error') {
          console.error('Network Error Details:');
          console.error('- API Endpoint: /api/communication/create-chat');

          // Use user.uid instead of user.id and check if it exists
          const doctorId = user?.uid || 'unknown';
          console.error('- Request Data:', { doctorId, patientId: patientInfo.id });

          // Use a fallback local ID for development/testing when backend is unavailable
          const fallbackId = `local_chat_${doctorId}_${patientInfo.id}_${Date.now()}`;
          console.log('Using fallback chat ID:', fallbackId);
          setCallId(fallbackId);

          // Show a less disruptive warning instead of forcing navigation back
          alert('Connection Warning: Unable to connect to the server. Some features may be limited.');
          return;
        }

        // For other errors, show the standard error message
        alert('Chat Error: Failed to initialize the chat. Please try again.');
        navigate(-1);
      }
    };

    initializeChat();
  }, []);

  // Animation
  useEffect(() => {
    setFadeAnim(true);
    setSlideAnim(true);
  }, []);

  // Auto-start video call if requested
  useEffect(() => {
    if (startVideoCall && initialRoomName) {
      // Set a short delay to ensure the component is fully mounted
      const timer = setTimeout(() => {
        setShowVideoCall(true);
        setRoomName(initialRoomName);
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [startVideoCall, initialRoomName]);

  // Timer for consultation duration
  useEffect(() => {
    const timer = setInterval(() => {
      setConsultationTime(prev => prev + 1);
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Format time as MM:SS
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Send a new message
  const sendMessage = async () => {
    if (message.trim() === '') return;

    // Create the message object first so we can add it to UI immediately
    const newMessage = {
      id: Date.now(),
      sender: 'doctor',
      senderName: `Dr. ${user?.lastName || 'Unknown'}`,
      text: message,
      timestamp: new Date(),
      status: 'sending' // Add a status to track message state
    };

    // Add message to UI immediately for better UX
    const updatedMessages = [...messages, newMessage];
    setMessages(updatedMessages);
    setMessage('');

    // Scroll to bottom
    setTimeout(() => {
      if (scrollViewRef.current) {
        scrollViewRef.current.scrollTop = scrollViewRef.current.scrollHeight;
      }
    }, 100);

    try {
      // Send message to the backend
      await axios.post(`/api/communication/chat/${callId}/message`, {
        content: message,
        type: 'text'
      });

      // Update message status to sent
      const sentMessages = updatedMessages.map(msg =>
        msg.id === newMessage.id ? { ...msg, status: 'sent' } : msg
      );
      setMessages(sentMessages);
    } catch (error) {
      console.error('Error sending message:', error);

      if (error.message === 'Network Error') {
        console.error('Network Error Details:');
        console.error('- API Endpoint:', `/api/communication/chat/${callId}/message`);
        console.error('- Message content:', message);

        // Update message status to indicate error
        const errorMessages = updatedMessages.map(msg =>
          msg.id === newMessage.id ? { ...msg, status: 'error' } : msg
        );
        setMessages(errorMessages);

        // Show a toast or non-blocking notification instead of an alert
        alert('Message Not Sent: Unable to send message due to network issues. The message is saved locally.');
      } else {
        // For other errors
        alert('Error: Failed to send message. Please try again.');
      }
    }
  };

  // Fetch messages from the server
  useEffect(() => {
    if (!callId) return;

    const fetchMessages = async () => {
      try {
        const response = await axios.get(`/api/communication/chat/${callId}/messages?limit=50`);
        if (response.data) {
          const formattedMessages = response.data.map(msg => ({
            id: msg.id,
            sender: msg.senderId === user?.uid ? 'doctor' : 'patient',
            senderName: msg.senderId === user?.uid ?
              `Dr. ${user?.lastName || 'Unknown'}` :
              `${patientInfo.firstName || 'Patient'} ${patientInfo.lastName || ''}`,
            text: msg.content,
            timestamp: new Date(msg.timestamp),
            status: 'sent'
          }));

          // Preserve locally created messages that haven't been sent yet
          const localMessages = messages.filter(msg =>
            msg.status === 'sending' || msg.status === 'error'
          );

          // Combine server messages with local messages
          const combinedMessages = [...formattedMessages, ...localMessages];

          // Sort by timestamp
          combinedMessages.sort((a, b) => a.timestamp - b.timestamp);

          setMessages(combinedMessages);

          // Scroll to bottom
          setTimeout(() => {
            if (scrollViewRef.current) {
              scrollViewRef.current.scrollTop = scrollViewRef.current.scrollHeight;
            }
          }, 100);
        }
      } catch (error) {
        console.error('Error fetching messages:', error);

        if (error.message === 'Network Error') {
          console.error('Network Error Details:');
          console.error('- API Endpoint:', `/api/communication/chat/${callId}/messages`);

          // Don't show an alert for network errors during background fetching
          // as it would be disruptive to the user experience
          console.log('Using cached messages due to network error');
        }
      }
    };

    // Initial fetch
    fetchMessages();

    // Set up polling for new messages
    const messageInterval = setInterval(fetchMessages, 10000);

    return () => clearInterval(messageInterval);
  }, [callId]);

  // Toggle sidebar panels
  const togglePanel = (panel) => {
    if (panel === 'chat') {
      setShowChat(true);
      setShowParticipants(false);
      setShowMedicalNotes(false);
    } else if (panel === 'participants') {
      setShowChat(false);
      setShowParticipants(true);
      setShowMedicalNotes(false);
    } else if (panel === 'notes') {
      setShowChat(false);
      setShowParticipants(false);
      setShowMedicalNotes(true);
    }
  };

  // Format timestamp
  const formatTimestamp = (timestamp) => {
    return timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  // End consultation
  const endConsultation = async () => {
    try {
      if (callId) {
        // End the call on the server
        await axios.post(`/api/video/${callId}/end`);
      }

      // Navigate back
      navigate(-1);
    } catch (error) {
      console.error('Error ending consultation:', error);
      navigate(-1);
    }
  };

  // Save medical notes
  const saveMedicalNotes = async () => {
    if (!medicalNote.trim()) {
      alert('Error: Please enter consultation notes before saving.');
      return;
    }

    try {
      await axios.post(`/api/medical/consultations/${callId}/notes`, {
        notes: medicalNote
      });

      alert('Success: Consultation notes saved successfully.');
    } catch (error) {
      console.error('Error saving notes:', error);
      alert('Error: Failed to save notes. Please try again.');
    }
  };

  // Create prescription
  const createPrescription = () => {
    navigate('/new-prescription', {
      state: {
        patientInfo: patientInfo,
        consultationId: callId,
        notes: medicalNote
      }
    });
  };

  // Doctor colors
  const doctorColors = ROLE_COLORS.doctor;

  return (
    <div className={`animated-container ${fadeAnim ? 'fade-in' : ''}`}>
      <div className="container">
        <div className="header" style={{ backgroundColor: doctorColors.primary }}>
          <div className="header-left">
            <button className="back-button" onClick={endConsultation}>
              <IoArrowBack size={24} color="#fff" />
            </button>
            <div className="header-title">
              Consultation with {patientInfo.firstName} {patientInfo.lastName}
            </div>
          </div>
          <div className="header-right">
            <div className="timer">{formatTime(consultationTime)}</div>
          </div>
        </div>

        <div className="content">
          {/* Video call container */}
          <div className="video-container">
            {showVideoCall ? (
              <VideoCall
                patientInfo={patientInfo}
                onCallEnd={() => setShowVideoCall(false)}
                callId={callId}
                setCallId={setCallId}
                initialRoomName={initialRoomName}
              />
            ) : (
              <div className="start-call-container">
                <button className="start-call-button" onClick={() => setShowVideoCall(true)}>
                  <IoVideocam size={40} color="#fff" />
                  <span className="start-call-text">Start Video Call</span>
                </button>
              </div>
            )}
          </div>

          {/* Chat panel */}
          <div className="side-panel">
            {/* Panel tabs */}
            <div className="panel-tabs">
              <div 
                className={`panel-tab ${showChat ? 'active-tab' : ''}`}
                onClick={() => togglePanel('chat')}
              >
                <IoChatbubble
                  size={20}
                  color={showChat ? doctorColors.primary : '#757575'}
                />
                <span className={`tab-text ${showChat ? 'active-tab-text' : ''}`}>Chat</span>
              </div>

              <div
                className={`panel-tab ${showParticipants ? 'active-tab' : ''}`}
                onClick={() => togglePanel('participants')}
              >
                <IoPeople
                  size={20}
                  color={showParticipants ? doctorColors.primary : '#757575'}
                />
                <span className={`tab-text ${showParticipants ? 'active-tab-text' : ''}`}>Participants</span>
              </div>

              <div
                className={`panel-tab ${showMedicalNotes ? 'active-tab' : ''}`}
                onClick={() => togglePanel('notes')}
              >
                <IoDocumentText
                  size={20}
                  color={showMedicalNotes ? doctorColors.primary : '#757575'}
                />
                <span className={`tab-text ${showMedicalNotes ? 'active-tab-text' : ''}`}>Notes</span>
              </div>
            </div>

            {/* Panel content */}
            <div className="panel-content">
              {showChat && (
                <div className="chat-container">
                  <div 
                    className="messages-container"
                    ref={scrollViewRef}
                  >
                    {messages.length === 0 && (
                      <div className="no-messages-text">
                        No messages yet. Start the conversation!
                      </div>
                    )}
                    {messages.map((msg) => (
                      <div
                        key={msg.id}
                        className={`message-item ${msg.sender === 'doctor' ? 'doctor-message' : 'patient-message'}`}
                      >
                        <div className="message-header">
                          <span className="message-sender">{msg.senderName}</span>
                          <span className="message-time">{formatTimestamp(msg.timestamp)}</span>
                        </div>
                        <div className="message-text">{msg.text}</div>
                        {msg.status && msg.sender === 'doctor' && (
                          <div className="message-status">
                            {msg.status === 'sending' && (
                              <span className="status-text">Sending...</span>
                            )}
                            {msg.status === 'error' && (
                              <div className="error-status">
                                <span className="error-status-text">Not sent</span>
                                <span
                                  className="retry-text"
                                  onClick={() => {
                                    // Remove the message with error status
                                    const filteredMessages = messages.filter(m => m.id !== msg.id);
                                    setMessages(filteredMessages);
                                    // Set the message text back to input for retry
                                    setMessage(msg.text);
                                  }}
                                >
                                  Retry
                                </span>
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>

                  <div className="input-container">
                    <textarea
                      className="input"
                      value={message}
                      onChange={(e) => setMessage(e.target.value)}
                      placeholder="Type a message..."
                      onKeyPress={(e) => {
                        if (e.key === 'Enter' && !e.shiftKey) {
                          e.preventDefault();
                          sendMessage();
                        }
                      }}
                    />
                    <button
                      className="send-button"
                      onClick={sendMessage}
                    >
                      <IoSend size={24} color={doctorColors.primary} />
                    </button>
                  </div>
                </div>
              )}

              {showParticipants && (
                <div className="participants-container">
                  <div className="participant">
                    <div className="participant-avatar">
                      <span className="avatar-text">
                        {user.firstName?.charAt(0) || 'D'}
                      </span>
                    </div>
                    <div className="participant-info">
                      <div className="participant-name">
                        Dr. {user.lastName} (You)
                      </div>
                      <div className="participant-role">Doctor</div>
                    </div>
                    <div className="participant-status">
                      {isMicOn ? 
                        <IoMic size={16} color="#4CAF50" /> : 
                        <IoMicOff size={16} color="#F44336" />
                      }
                      {isCameraOn ? 
                        <IoVideocam size={16} color="#4CAF50" /> : 
                        <IoVideocamOff size={16} color="#F44336" />
                      }
                    </div>
                  </div>

                  <div className="participant">
                    <div className="participant-avatar patient-avatar">
                      <span className="avatar-text">
                        {patientInfo.firstName?.charAt(0)}
                      </span>
                    </div>
                    <div className="participant-info">
                      <div className="participant-name">
                        {patientInfo.firstName} {patientInfo.lastName}
                      </div>
                      <div className="participant-role">Patient</div>
                    </div>
                  </div>

                  <div className="invite-container">
                    <button
                      className="invite-button"
                      onClick={() => {
                        // This functionality requires the Jitsi Meet API
                        alert('Info: Invite functionality is not available in this version.');
                      }}
                    >
                      <IoPersonAdd size={20} color="#fff" />
                      <span className="invite-text">Invite Specialist</span>
                    </button>
                  </div>
                </div>
              )}

              {showMedicalNotes && (
                <div className="notes-container">
                  <div className="notes-title">Consultation Notes</div>
                  <div className="notes-subtitle">
                    Patient: {patientInfo.firstName} {patientInfo.lastName}, {patientInfo.age} years
                  </div>

                  <textarea
                    className="notes-input"
                    value={medicalNote}
                    onChange={(e) => setMedicalNote(e.target.value)}
                    placeholder="Enter consultation notes here..."
                    rows={10}
                  />

                  <div className="notes-actions">
                    <button
                      className="notes-button"
                      onClick={saveMedicalNotes}
                    >
                      <IoSave size={20} color="#fff" />
                      <span className="notes-button-text">Save Notes</span>
                    </button>

                    <button
                      className="notes-button prescription-button"
                      onClick={createPrescription}
                    >
                      <IoMedical size={20} color="#fff" />
                      <span className="notes-button-text">Create Prescription</span>
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChatRoom;
