import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../../../contexts/AuthContext';
import { useMedications } from '../../../../contexts/MedicationContext';
import MedicationReminders from './MedicationReminders';
import { IoMdArrowBack, IoMdAdd, IoMdTrash, IoMdMedkit, IoMdAlert, IoMdInformation, IoMdClose, IoMdMic, IoMdTime, IoMdCalendar } from 'react-icons/io';
import { clearAllMedicationsForUser } from '../../../../services/firebaseMedicationsService';
import { localMedicationsService } from '../../../../services/localStorageService';
import '../../../../styles/dashboards/patient/medications/medications.css';

const Medications = () => {
  const { user } = useAuth();
  const {
    medications,
    reminders,
    loading,
    error,
    useLocalStorage,
    addMedication,
    addReminder,
    createRemindersForMedication,
    migrateLocalDataToFirebase,
    fetchMedications
  } = useMedications();
  const navigate = useNavigate();
  const [modalVisible, setModalVisible] = useState(false);
  const [reminderModalVisible, setReminderModalVisible] = useState(false);
  const [selectedMedication, setSelectedMedication] = useState(null);
  const [localLoading, setLocalLoading] = useState(false);

  // Form states for adding new medication
  const [medicationName, setMedicationName] = useState('');
  const [dosage, setDosage] = useState('');
  const [frequency, setFrequency] = useState('daily');
  const [selectedDays, setSelectedDays] = useState({
    monday: true,
    tuesday: true,
    wednesday: true,
    thursday: true,
    friday: true,
    saturday: true,
    sunday: true
  });
  const [selectedTimes, setSelectedTimes] = useState([{ hour: 9, minute: 0 }]);
  const [showTimePickerIndex, setShowTimePickerIndex] = useState(-1);
  const [instructions, setInstructions] = useState('');

  // Voice recognition states
  const [voiceMode, setVoiceMode] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [processingVoice, setProcessingVoice] = useState(false);
  const [recognizedText, setRecognizedText] = useState('');

  // Form states for adding reminder
  const [reminderDate, setReminderDate] = useState(new Date());
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showTimePicker, setShowTimePicker] = useState(false);
  const [reminderInstructions, setReminderInstructions] = useState('');

  useEffect(() => {
    // Check if we need to migrate local data to Firebase
    const checkMigration = async () => {
      try {
        // This is a one-time migration for existing users
        await migrateLocalDataToFirebase();
      } catch (err) {
        console.error('Error during migration check:', err);
      }
    };

    if (user) {
      checkMigration();
    }
  }, [user, migrateLocalDataToFirebase]);

  // Handle adding a time slot
  const handleAddTimeSlot = () => {
    setSelectedTimes([...selectedTimes, { hour: 9, minute: 0 }]);
  };

  // Voice recognition functions
  const startRecording = async () => {
    try {
      // In a web app, we would use the Web Speech API
      if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
        setIsRecording(true);
        // Web implementation would go here
        alert("Voice recording started. This is a demo feature in the web version.");
      } else {
        alert('Speech recognition is not supported in this browser.');
      }
    } catch (error) {
      console.error('Failed to start recording:', error);
      alert('Failed to start recording. Please try again.');
    }
  };

  const stopRecording = async () => {
    try {
      setIsRecording(false);
      setProcessingVoice(true);

      // Simulate processing with a timeout
      setTimeout(() => {
        const userMessage = "I heard you speaking. In a real app, this would use speech recognition.";
        setRecognizedText(userMessage);
        setProcessingVoice(false);
        
        alert("This is a demo. In a real app, your voice would be processed. Please enter medication details manually.");
      }, 1000);
    } catch (error) {
      console.error('Failed to stop recording:', error);
      setIsRecording(false);
      setProcessingVoice(false);
      alert('Failed to process recording. Please try again.');
    }
  };

  const toggleVoiceMode = () => {
    setVoiceMode(!voiceMode);
    setRecognizedText('');
  };

  // Handle removing a time slot
  const handleRemoveTimeSlot = (index) => {
    const newTimes = [...selectedTimes];
    newTimes.splice(index, 1);
    setSelectedTimes(newTimes);
  };

  // Handle time change
  const handleTimeChange = (e, index) => {
    const timeValue = e.target.value;
    if (timeValue) {
      const [hours, minutes] = timeValue.split(':').map(Number);
      const newTimes = [...selectedTimes];
      newTimes[index] = {
        hour: hours,
        minute: minutes
      };
      setSelectedTimes(newTimes);
    }
  };

  // Toggle day selection
  const toggleDay = (day) => {
    setSelectedDays(prev => ({
      ...prev,
      [day]: !prev[day]
    }));
  };

  // Format time for display
  const formatTime = (hour, minute) => {
    const date = new Date();
    date.setHours(hour, minute, 0);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const handleAddMedication = async () => {
    if (!medicationName.trim()) {
      alert('Please enter a medication name');
      return;
    }

    setLocalLoading(true);
    try {
      // Create frequency string based on selection
      let frequencyStr = frequency;
      if (frequency === 'specific-days') {
        const days = Object.entries(selectedDays)
          .filter(([_, selected]) => selected)
          .map(([day]) => day.charAt(0).toUpperCase() + day.slice(1))
          .join(', ');
        frequencyStr = `On ${days}`;
      }

      // Format times
      const times = selectedTimes.map(time =>
        formatTime(time.hour, time.minute)
      ).join(', ');

      const newMedication = {
        patientId: user?.uid,
        name: medicationName,
        dosage: dosage,
        frequency: frequencyStr,
        frequencyType: frequency,
        frequencyDays: selectedDays,
        frequencyTimes: selectedTimes,
        instructions: instructions,
      };

      // Use the context method to save to Firebase
      const savedMedication = await addMedication(newMedication);

      // Create reminders for the medication based on frequency
      await createRemindersForMedication(savedMedication);

      // Reset form
      setMedicationName('');
      setDosage('');
      setFrequency('daily');
      setSelectedDays({
        monday: true,
        tuesday: true,
        wednesday: true,
        thursday: true,
        friday: true,
        saturday: true,
        sunday: true
      });
      setSelectedTimes([{ hour: 9, minute: 0 }]);
      setInstructions('');

      // Close modal automatically after saving
      setModalVisible(false);

      // Reset voice mode if active
      if (voiceMode) {
        setVoiceMode(false);
        setRecognizedText('');
      }
    } catch (error) {
      console.error('Error adding medication:', error);
      alert('Failed to add medication');
    } finally {
      setLocalLoading(false);
    }
  };

  const handleAddReminder = async () => {
    if (!selectedMedication) {
      alert('Please select a medication');
      return;
    }

    setLocalLoading(true);
    try {
      const newReminder = {
        patientId: user?.uid,
        medicationId: selectedMedication.id,
        medicationName: selectedMedication.name,
        dosage: selectedMedication.dosage,
        instructions: reminderInstructions || selectedMedication.instructions,
        scheduledTime: reminderDate.toISOString(),
        status: 'scheduled',
      };

      // Use the context method to save to Firebase
      await addReminder(newReminder);

      // Reset form and close modal
      setReminderInstructions('');
      setReminderDate(new Date());
      setReminderModalVisible(false);
      setSelectedMedication(null);
    } catch (error) {
      console.error('Error adding reminder:', error);
      alert('Failed to add reminder');
    } finally {
      setLocalLoading(false);
    }
  };

  const handleMedicationPress = (medication) => {
    setSelectedMedication(medication);
    setReminderInstructions(medication.instructions);
    setReminderModalVisible(true);
  };

  // Function to clear all medications
  const handleClearAllMedications = () => {
    if (window.confirm("Are you sure you want to delete all your medications and reminders? This action cannot be undone.")) {
      const deleteFirebaseData = async () => {
        setLocalLoading(true);
        try {
          await clearAllMedicationsForUser();
          // Refresh the medications list
          await fetchMedications();
          alert("All medications and reminders have been deleted from Firebase.");
        } catch (error) {
          console.error('Error clearing medications:', error);
          alert("Failed to clear medications. Please try again.");
        } finally {
          setLocalLoading(false);
        }
      };

      const deleteAllData = async () => {
        setLocalLoading(true);
        try {
          // Clear Firebase data
          await clearAllMedicationsForUser();

          // Clear local storage data
          await localMedicationsService.clearAllMedicationData();

          // Refresh the medications list
          await fetchMedications();

          alert("All medications and reminders have been deleted from both Firebase and local storage.");
        } catch (error) {
          console.error('Error clearing all medications data:', error);
          alert("Failed to clear all medications data. Please try again.");
        } finally {
          setLocalLoading(false);
        }
      };

      const choice = window.confirm("Delete from Firebase only or all data (including local storage)?");
      if (choice) {
        deleteAllData();
      } else {
        deleteFirebaseData();
      }
    }
  };

  const onDateChange = (e) => {
    const newDate = new Date(e.target.value);
    if (!isNaN(newDate.getTime())) {
      // Keep the time from the current reminderDate
      newDate.setHours(reminderDate.getHours());
      newDate.setMinutes(reminderDate.getMinutes());
      setReminderDate(newDate);
    }
  };

  const onTimeChange = (e) => {
    const timeValue = e.target.value;
    if (timeValue) {
      const [hours, minutes] = timeValue.split(':').map(Number);
      const newDate = new Date(reminderDate);
      newDate.setHours(hours);
      newDate.setMinutes(minutes);
      setReminderDate(newDate);
    }
  };

  return (
    <div className="container">
      <div className="header">
        <button
          className="backButton"
          onClick={() => navigate(-1)}
        >
          <IoMdArrowBack size={24} color="#333" />
        </button>
        <h1 className="headerTitle">Medications</h1>
        <button
          className="addButton"
          onClick={() => setModalVisible(true)}
        >
          <IoMdAdd size={24} color="#4285F4" />
        </button>
      </div>

      <div className="content">
        <div className="section">
          <div className="sectionHeader">
            <h2 className="sectionTitle">Your Medications</h2>
            {medications.length > 0 && (
              <button
                className="clearButton"
                onClick={handleClearAllMedications}
              >
                <IoMdTrash size={18} color="#F44336" />
                <span className="clearButtonText">Clear All</span>
              </button>
            )}
          </div>

          {loading || localLoading ? (
            <div className="loadingContainer">
              <div className="spinner"></div>
              <p className="loadingText">Loading medications...</p>
            </div>
          ) : medications.length === 0 ? (
            <div className="emptyContainer">
              <IoMdMedkit size={48} color="#BDBDBD" />
              <p className="emptyText">No medications added yet</p>
              <button
                className="emptyButton"
                onClick={() => setModalVisible(true)}
              >
                <span className="emptyButtonText">Add Medication</span>
              </button>
            </div>
          ) : (
            <div>
              {medications.map((item) => (
                <div 
                  key={item.id} 
                  className="medicationItem"
                  onClick={() => handleMedicationPress(item)}
                >
                  <div className="medicationHeader">
                    <IoMdMedkit size={24} color="#4285F4" />
                    <div className="medicationInfo">
                      <h3 className="medicationName">{item.name}</h3>
                      <p className="medicationDosage">{item.dosage}</p>
                    </div>
                    <IoMdAdd size={24} color="#4CAF50" />
                  </div>

                  <div className="medicationDetails">
                    <p className="medicationFrequency">
                      <span className="detailLabel">Frequency: </span>
                      {item.frequency}
                    </p>

                    {item.instructions && (
                      <p className="medicationInstructions">
                        <span className="detailLabel">Instructions: </span>
                        {item.instructions}
                      </p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}

          {error && (
            <div className="errorContainer">
              <IoMdAlert size={24} color="#F44336" />
              <p className="errorText">{error}</p>
            </div>
          )}

          {useLocalStorage && (
            <div className="infoContainer">
              <IoMdInformation size={24} color="#2196F3" />
              <p className="infoText">
                Your medications are currently stored on this device only.
                They will not be available on other devices.
              </p>
            </div>
          )}
        </div>

        <div className="section">
          <h2 className="sectionTitle">Medication Reminders</h2>
          <MedicationReminders />
        </div>
      </div>

      {/* Add Medication Modal */}
      {modalVisible && (
        <div className="modalOverlay">
          <div className="modalContent">
            <div className="modalHeader">
              <h2 className="modalTitle">Add New Medication</h2>
              <div className="modalHeaderActions">
                <button
                  className="voiceModeButton"
                  onClick={toggleVoiceMode}
                >
                  <IoMdMic size={24} color="#4285F4" />
                </button>
                <button onClick={() => setModalVisible(false)}>
                  <IoMdClose size={24} color="#333" />
                </button>
              </div>
            </div>

            {voiceMode ? (
              <div className="voiceContainer">
                {!recognizedText ? (
                  <>
                    <p className="voiceInstructions">
                      {isRecording
                        ? "I'm listening... Speak clearly and include medication name, dosage, frequency, and instructions."
                        : "Tap the microphone and say something like:"}
                    </p>

                    {!isRecording && (
                      <p className="voiceExample">
                        "Add Amoxicillin 500mg three times daily with food"
                      </p>
                    )}

                    <button
                      className={`recordButton ${isRecording ? 'recordingButton' : ''}`}
                      onClick={isRecording ? stopRecording : startRecording}
                    >
                      <IoMdMic
                        size={32}
                        color={isRecording ? "#fff" : "#4285F4"}
                      />
                    </button>

                    {isRecording && (
                      <p className="recordingText">Recording... Tap to stop</p>
                    )}
                  </>
                ) : processingVoice ? (
                  <div className="processingContainer">
                    <div className="spinner"></div>
                    <p className="processingText">Processing your request...</p>
                  </div>
                ) : (
                  <div className="resultContainer">
                    <h3 className="recognizedTextTitle">I heard:</h3>
                    <p className="recognizedText">{recognizedText}</p>

                    <div className="medicationSummary">
                      <h3 className="summaryTitle">Medication Details:</h3>
                      <div className="summaryItem">
                        <span className="summaryLabel">Name:</span>
                        <span className="summaryValue">{medicationName || 'Not detected'}</span>
                      </div>
                      <div className="summaryItem">
                        <span className="summaryLabel">Dosage:</span>
                        <span className="summaryValue">{dosage || 'Not detected'}</span>
                      </div>
                      <div className="summaryItem">
                        <span className="summaryLabel">Frequency:</span>
                        <span className="summaryValue">
                          {frequency === 'daily' ? 'Daily' : 'Specific days'}
                        </span>
                      </div>
                      <div className="summaryItem">
                        <span className="summaryLabel">Times:</span>
                        <span className="summaryValue">
                          {selectedTimes.map(time => formatTime(time.hour, time.minute)).join(', ')}
                        </span>
                      </div>
                      <div className="summaryItem">
                        <span className="summaryLabel">Instructions:</span>
                        <span className="summaryValue">{instructions || 'None'}</span>
                      </div>
                    </div>

                    <div className="voiceActionButtons">
                      <button
                        className="voiceActionButton secondaryVoiceButton"
                        onClick={() => {
                          setRecognizedText('');
                          setVoiceMode(false);
                        }}
                      >
                        <span className="secondaryVoiceButtonText">Edit Manually</span>
                      </button>

                      <button
                        className="voiceActionButton primaryVoiceButton"
                        onClick={handleAddMedication}
                        disabled={localLoading}
                      >
                        {localLoading ? (
                          <div className="loadingButtonContent">
                            <div className="spinner"></div>
                            <span className="primaryVoiceButtonText">Saving...</span>
                          </div>
                        ) : (
                          <span className="primaryVoiceButtonText">Save Medication</span>
                        )}
                      </button>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="modalBody">
                <div className="formGroup">
                  <label className="formLabel">Medication Name *</label>
                  <input
                    className="formInput"
                    value={medicationName}
                    onChange={(e) => setMedicationName(e.target.value)}
                    placeholder="Enter medication name"
                  />
                </div>

                <div className="formGroup">
                  <label className="formLabel">Dosage</label>
                  <input
                    className="formInput"
                    value={dosage}
                    onChange={(e) => setDosage(e.target.value)}
                    placeholder="e.g., 500mg, 2 tablets"
                  />
                </div>

                <div className="formGroup">
                  <label className="formLabel">Frequency</label>
                  <div className="frequencyOptions">
                    <button
                      className={`frequencyOption ${frequency === 'daily' ? 'frequencyOptionSelected' : ''}`}
                      onClick={() => setFrequency('daily')}
                    >
                      <span className={`frequencyOptionText ${frequency === 'daily' ? 'frequencyOptionTextSelected' : ''}`}>Daily</span>
                    </button>

                    <button
                      className={`frequencyOption ${frequency === 'specific-days' ? 'frequencyOptionSelected' : ''}`}
                      onClick={() => setFrequency('specific-days')}
                    >
                      <span className={`frequencyOptionText ${frequency === 'specific-days' ? 'frequencyOptionTextSelected' : ''}`}>Specific Days</span>
                    </button>
                  </div>
                </div>

                {frequency === 'specific-days' && (
                  <div className="formGroup">
                    <label className="formLabel">Select Days</label>
                    <div className="daysContainer">
                      {['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'].map(day => (
                        <button
                          key={day}
                          className={`dayButton ${selectedDays[day] ? 'dayButtonSelected' : ''}`}
                          onClick={() => toggleDay(day)}
                        >
                          <span className={`dayButtonText ${selectedDays[day] ? 'dayButtonTextSelected' : ''}`}>
                            {day.charAt(0).toUpperCase()}
                          </span>
                        </button>
                      ))}
                    </div>
                  </div>
                )}

                <div className="formGroup">
                  <div className="timeHeaderContainer">
                    <label className="formLabel">Time(s) to Take</label>
                    <button
                      className="addTimeButton"
                      onClick={handleAddTimeSlot}
                    >
                      <IoMdAdd size={24} color="#4285F4" />
                      <span className="addTimeText">Add Time</span>
                    </button>
                  </div>

                  {selectedTimes.map((time, index) => (
                    <div key={index} className="timeSlotContainer">
                      <div className="timePickerButton">
                        <IoMdTime size={20} color="#4285F4" />
                        <input
                          type="time"
                          value={`${time.hour.toString().padStart(2, '0')}:${time.minute.toString().padStart(2, '0')}`}
                          onChange={(e) => handleTimeChange(e, index)}
                          className="timePickerInput"
                        />
                      </div>

                      {selectedTimes.length > 1 && (
                        <button
                          className="removeTimeButton"
                          onClick={() => handleRemoveTimeSlot(index)}
                        >
                          <IoMdClose size={24} color="#F44336" />
                        </button>
                      )}
                    </div>
                  ))}
                </div>

                <div className="formGroup">
                  <label className="formLabel">Instructions</label>
                  <textarea
                    className="formInput textArea"
                    value={instructions}
                    onChange={(e) => setInstructions(e.target.value)}
                    placeholder="e.g., Take with food, Take before bed"
                    rows={3}
                  />
                </div>
              </div>
            )}

            <div className="modalFooter">
              <button
                className="modalButton cancelButton"
                onClick={() => setModalVisible(false)}
              >
                <span className="cancelButtonText">Cancel</span>
              </button>
              <button
                className="modalButton saveButton"
                onClick={handleAddMedication}
                disabled={localLoading}
              >
                {localLoading ? (
                  <div className="loadingButtonContent">
                    <div className="spinner"></div>
                    <span className="saveButtonText">Saving...</span>
                  </div>
                ) : (
                  <span className="saveButtonText">Save Medication</span>
                )}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Add Reminder Modal */}
      {reminderModalVisible && (
        <div className="modalOverlay">
          <div className="modalContent">
            <div className="modalHeader">
              <h2 className="modalTitle">Add Medication Reminder</h2>
              <button onClick={() => setReminderModalVisible(false)}>
                <IoMdClose size={24} color="#333" />
              </button>
            </div>

            {selectedMedication && (
              <div className="modalBody">
                <div className="selectedMedication">
                  <h3 className="selectedMedicationTitle">{selectedMedication.name}</h3>
                  <p className="selectedMedicationDosage">{selectedMedication.dosage}</p>
                </div>

                <div className="formGroup">
                  <label className="formLabel">Date</label>
                  <div className="datePickerButton">
                    <input
                      type="date"
                      value={reminderDate.toISOString().split('T')[0]}
                      onChange={onDateChange}
                      className="datePickerInput"
                    />
                    <IoMdCalendar size={20} color="#4285F4" />
                  </div>
                </div>

                <div className="formGroup">
                  <label className="formLabel">Time</label>
                  <div className="datePickerButton">
                    <input
                      type="time"
                      value={`${reminderDate.getHours().toString().padStart(2, '0')}:${reminderDate.getMinutes().toString().padStart(2, '0')}`}
                      onChange={onTimeChange}
                      className="datePickerInput"
                    />
                    <IoMdTime size={20} color="#4285F4" />
                  </div>
                </div>

                <div className="formGroup">
                  <label className="formLabel">Special Instructions</label>
                  <textarea
                    className="formInput textArea"
                    value={reminderInstructions}
                    onChange={(e) => setReminderInstructions(e.target.value)}
                    placeholder="e.g., Take with food, Take before bed"
                    rows={3}
                  />
                </div>
              </div>
            )}

            <div className="modalFooter">
              <button
                className="modalButton cancelButton"
                onClick={() => setReminderModalVisible(false)}
              >
                <span className="cancelButtonText">Cancel</span>
              </button>
              <button
                className="modalButton saveButton"
                onClick={handleAddReminder}
                disabled={localLoading}
              >
                {localLoading ? (
                  <div className="loadingButtonContent">
                    <div className="spinner"></div>
                    <span className="saveButtonText">Setting...</span>
                  </div>
                ) : (
                  <span className="saveButtonText">Set Reminder</span>
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Medications;
