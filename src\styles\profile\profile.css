.container {
  flex: 1;
}

.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
}

.scrollView {
  flex: 1;
  overflow-y: auto;
}

.content {
  flex: 1;
  padding: 20px;
  margin-bottom: 20px;
}

.appHeader {
  background-color: var(--primary-color);
  display: flex;
  align-items: center;
  padding: 10px 20px;
  color: white;
}

.backButton {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  margin-right: 10px;
  display: flex;
  align-items: center;
}

.headerTitle {
  flex: 1;
  color: white;
  font-size: 1.2rem;
  margin: 0;
}
