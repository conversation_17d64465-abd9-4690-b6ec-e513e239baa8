import React, { useState } from 'react';
import '../styles/screens/changePasswordScreen.css';

// Mock hooks and utilities for web version
const useNavigation = () => ({
  goBack: () => window.history.back()
});

const useAuth = () => ({
  user: { role: 'default' },
  changePassword: async (currentPassword, newPassword) => {
    // Mock implementation - replace with actual web authentication logic
    console.log('Changing password...');
    return Promise.resolve();
  }
});

const getThemeForRole = (role) => ({
  colors: {
    primary: role === 'patient' ? '#ff952b' : 
             role === 'doctor' ? '#aa56ff' :
             role === 'supervisor' ? '#ff00f2' :
             role === 'caregiver' ? '#00a9ff' :
             '#106b00' // admin/default
  }
});

const ChangePasswordScreen = () => {
  const navigation = useNavigation();
  const { user, changePassword } = useAuth();
  const theme = getThemeForRole(user?.role || 'default');
  
  const [formData, setFormData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });
  const [loading, setLoading] = useState(false);
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  const handleChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    setError('');
  };

  const validateForm = () => {
    if (!formData.currentPassword || !formData.newPassword || !formData.confirmPassword) {
      setError('All fields are required');
      return false;
    }

    if (formData.newPassword.length < 6) {
      setError('New password must be at least 6 characters long');
      return false;
    }

    if (formData.newPassword !== formData.confirmPassword) {
      setError('New password and confirmation do not match');
      return false;
    }

    if (formData.currentPassword === formData.newPassword) {
      setError('New password must be different from current password');
      return false;
    }

    return true;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validateForm()) return;

    try {
      setLoading(true);
      setError('');
      
      await changePassword(formData.currentPassword, formData.newPassword);
      
      setSuccess('Password changed successfully!');
      setTimeout(() => {
        navigation.goBack();
      }, 2000);
      
    } catch (error) {
      setError(error.message || 'Failed to change password');
    } finally {
      setLoading(false);
    }
  };

  const headerStyle = {
    backgroundColor: theme.colors.primary
  };

  const primaryColorStyle = {
    color: theme.colors.primary
  };

  const buttonStyle = {
    backgroundColor: theme.colors.primary
  };

  return (
    <div className="container">
      <header className="header" style={headerStyle}>
        <button 
          className="back-button" 
          onClick={() => navigation.goBack()}
          type="button"
        >
          ←
        </button>
        <h1 className="header-title">Change Password</h1>
      </header>
      
      <div className="content">
        <div className="form-container">
          <div className="header-container">
            <div className="header-icon">🔒</div>
            <h2 className="header-text" style={primaryColorStyle}>Change Password</h2>
            <p className="sub-header-text">
              Update your password to keep your account secure
            </p>
          </div>

          <form onSubmit={handleSubmit}>
            {/* Current Password */}
            <div className="input-container">
              <label className="input-label">Current Password</label>
              <div className="input-box">
                <span className="input-icon">🔒</span>
                <input
                  className="input"
                  type={showCurrentPassword ? "text" : "password"}
                  placeholder="Enter current password"
                  value={formData.currentPassword}
                  onChange={(e) => handleChange('currentPassword', e.target.value)}
                  required
                />
                <button
                  type="button"
                  className="password-toggle"
                  onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                >
                  {showCurrentPassword ? "👁️" : "👁️‍🗨️"}
                </button>
              </div>
            </div>

            {/* New Password */}
            <div className="input-container">
              <label className="input-label">New Password</label>
              <div className="input-box">
                <span className="input-icon">🔒</span>
                <input
                  className="input"
                  type={showNewPassword ? "text" : "password"}
                  placeholder="Enter new password"
                  value={formData.newPassword}
                  onChange={(e) => handleChange('newPassword', e.target.value)}
                  required
                />
                <button
                  type="button"
                  className="password-toggle"
                  onClick={() => setShowNewPassword(!showNewPassword)}
                >
                  {showNewPassword ? "👁️" : "👁️‍🗨️"}
                </button>
              </div>
            </div>

            {/* Confirm Password */}
            <div className="input-container">
              <label className="input-label">Confirm New Password</label>
              <div className="input-box">
                <span className="input-icon">🔒</span>
                <input
                  className="input"
                  type={showConfirmPassword ? "text" : "password"}
                  placeholder="Confirm new password"
                  value={formData.confirmPassword}
                  onChange={(e) => handleChange('confirmPassword', e.target.value)}
                  required
                />
                <button
                  type="button"
                  className="password-toggle"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                >
                  {showConfirmPassword ? "👁️" : "👁️‍🗨️"}
                </button>
              </div>
            </div>

            {error && <div className="error-message">{error}</div>}
            {success && <div className="success-message">{success}</div>}

            {/* Submit Button */}
            <button
              type="submit"
              className="button"
              style={buttonStyle}
              disabled={loading}
            >
              {loading ? (
                <span className="loading-spinner"></span>
              ) : (
                'Update Password'
              )}
            </button>

            {/* Cancel Button */}
            <button
              type="button"
              className="cancel-button"
              onClick={() => navigation.goBack()}
              disabled={loading}
              style={{ color: theme.colors.primary, borderColor: theme.colors.primary }}
            >
              Cancel
            </button>
          </form>
        </div>
      </div>
    </div>
  );
};

export default ChangePasswordScreen;
