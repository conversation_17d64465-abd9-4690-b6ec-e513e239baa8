/* Doctor Dashboard Styles */

.loading-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f7fa;
  padding: 20px;
}

.loading-text {
  margin-top: 10px;
  font-size: 16px;
  color: #424242;
  text-align: center;
}

.scroll-view {
  flex: 1;
  background-color: #f5f7fa;
  overflow-y: auto;
  height: 100%;
}

.header-section {
  width: 100%;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.header-gradient {
  padding: 30px 20px;
  border-bottom-left-radius: 25px;
  border-bottom-right-radius: 25px;
  background: linear-gradient(to right bottom, var(--doctor-primary), var(--doctor-primary-light), #f5f7fa);
}

.greeting {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.greeting-text {
  font-size: 26px;
  font-weight: bold;
  color: white;
  margin-bottom: 5px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
}

.date-text {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
}

.header-actions {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.content-section {
  padding: 10px 16px 80px 16px;
}

.section-header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 14px 16px;
  background-color: white;
  border-radius: 16px;
  margin-bottom: 16px;
  margin-top: 10px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.section-header-content {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.section-icon {
  margin-right: 10px;
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  color: #212121;
}

.section-content {
  margin-bottom: 30px;
  padding-top: 5px;
}

.cards-container {
  margin-bottom: 30px;
  padding-top: 10px;
}

.card-row {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin-bottom: 24px;
  height: 170px;
}

.section-container {
  background-color: #ffffff;
  border-radius: 16px;
  padding: 18px;
  margin-bottom: 30px;
  margin-top: 5px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.quick-actions {
  margin-bottom: 30px;
}

.action-buttons {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-top: 10px;
}

.action-button {
  width: 31%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 16px;
  background-color: white;
  padding: 16px;
  border-radius: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
  cursor: pointer;
}

.action-icon {
  width: 50px;
  height: 50px;
  border-radius: 25px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 8px;
}

.action-text {
  font-size: 12px;
  color: #424242;
  text-align: center;
  font-weight: 500;
}

.vitals-charts-container {
  background-color: white;
  border-radius: 16px;
  overflow: hidden;
  margin-bottom: 16px;
  height: 500px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.section {
  margin-top: 20px;
  margin-bottom: 20px;
}

.fab {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 56px;
  height: 56px;
  border-radius: 28px;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 3px 5px rgba(0, 0, 0, 0.27);
  z-index: 999;
  cursor: pointer;
  color: white;
}

/* Animation classes */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.slide-down {
  animation: slideDown 0.4s ease-in-out;
}

.slide-up {
  animation: slideUp 0.4s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideDown {
  from { transform: translateY(-50px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}
