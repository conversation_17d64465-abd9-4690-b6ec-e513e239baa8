/* Styles for ForgotPassword component */
.background {
  min-height: 100vh;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-size: cover;
  background-position: center;
}

.container {
  padding: 25px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 15px;
  margin: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  max-width: 450px;
  width: 100%;
}

.title {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 10px;
  color: #333;
}

.subtitle {
  font-size: 16px;
  color: #666;
  text-align: center;
  margin-bottom: 30px;
}

.input-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  width: 100%;
  background-color: #f5f5f5;
  border-radius: 8px;
  margin-bottom: 20px;
  padding: 0 10px;
}

.icon {
  margin-right: 10px;
}

.input {
  flex: 1;
  height: 50px;
  font-size: 16px;
  color: #333;
  border: none;
  background-color: transparent;
  outline: none;
  width: 100%;
}

.button {
  background-color: #007AFF;
  width: 100%;
  padding: 15px;
  border-radius: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20px;
  border: none;
  cursor: pointer;
}

.button-text {
  color: #fff;
  font-size: 16px;
  font-weight: 600;
}

.button-disabled {
  background-color: #A0CEF7;
  cursor: not-allowed;
}

.back-link {
  margin-top: 10px;
  cursor: pointer;
  text-decoration: none;
}

.back-link-text {
  color: #007AFF;
  font-size: 16px;
}

.spinner {
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top: 3px solid white;
  width: 20px;
  height: 20px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
