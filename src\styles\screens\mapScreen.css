/* MapScreen Styles */
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

.loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 10px;
  font-size: 16px;
  color: #666;
}

.map-container {
  height: 60%;
  width: 100%;
  background-color: #e0e0e0;
  border-bottom: 1px solid #ccc;
  position: relative;
}

.map {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.map-controls {
  position: absolute;
  right: 16px;
  top: 16px;
  background-color: transparent;
  z-index: 5;
}

.map-control-button {
  background-color: #fff;
  border: none;
  border-radius: 30px;
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  cursor: pointer;
  transition: background-color 0.2s;
}

.map-control-button:hover {
  background-color: #f0f0f0;
}

.map-control-button .icon {
  font-size: 24px;
  color: #333;
}

.navigation-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(255, 255, 255, 0.9);
  padding: 16px;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  max-height: 40%;
}

.navigation-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 16px;
  text-align: center;
}

.route-info-container {
  display: flex;
  justify-content: space-around;
  background-color: #fff;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 12px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.route-info-item {
  display: flex;
  align-items: center;
}

.route-info-text {
  margin-left: 8px;
  font-size: 14px;
  font-weight: bold;
  color: #333;
}

.instructions-container {
  width: 100%;
  background-color: #f5f5f5;
  padding: 16px;
  border-radius: 8px;
  margin-top: 16px;
}

.instructions-title,
.instructions-label {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
}

.instructions-text {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

.map-error-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  background-color: #f5f5f5;
}

.map-error-text {
  font-size: 16px;
  color: #666;
  margin-bottom: 16px;
}

.map-error-button {
  background-color: #4285f4;
  color: #fff;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
}

.map-error-button:hover {
  background-color: #3367d6;
}

.action-buttons-container {
  display: flex;
  justify-content: space-around;
  padding: 16px;
  background-color: #fff;
  border-top: 1px solid #e0e0e0;
}

.action-button {
  width: 50px;
  height: 50px;
  border-radius: 25px;
  border: none;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.3);
  cursor: pointer;
  transition: transform 0.1s;
}

.action-button:hover {
  transform: scale(1.05);
}

.action-button:active {
  transform: scale(0.95);
}

.action-button.primary {
  background-color: #3498db;
}

.action-button.secondary {
  background-color: #2ecc71;
}

.action-button.tertiary {
  background-color: #f39c12;
}

.action-button.purple {
  background-color: #9c27b0;
}

.action-button.green {
  background-color: #4caf50;
}

.action-button.red {
  background-color: #f44336;
}

.action-button .icon {
  font-size: 24px;
  color: #fff;
}

.saved-items-panel {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
  max-height: 40%;
  box-shadow: 0 -3px 5px rgba(0, 0, 0, 0.2);
  z-index: 10;
}

.saved-items-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #eee;
}

.saved-items-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.panel-close-button {
  background: none;
  border: none;
  font-size: 24px;
  color: #333;
  cursor: pointer;
}

.saved-items-list {
  padding: 16px;
  max-height: 300px;
  overflow-y: auto;
}

.empty-list-text {
  text-align: center;
  color: #999;
  font-size: 16px;
  padding: 20px;
}

.saved-item-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background-color: #f9f9f9;
  border-radius: 8px;
  margin-bottom: 8px;
  border: 1px solid #eee;
  cursor: pointer;
  transition: background-color 0.2s;
}

.saved-item-card:hover {
  background-color: #f0f0f0;
}

.saved-item-info {
  flex: 1;
}

.saved-item-name {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.saved-item-address {
  font-size: 14px;
  color: #666;
  margin-top: 2px;
}

.saved-item-notes {
  font-size: 14px;
  color: #888;
  margin-top: 4px;
  font-style: italic;
}

.delete-button {
  background: none;
  border: none;
  padding: 8px;
  cursor: pointer;
  font-size: 20px;
}

.delete-button:hover {
  background-color: rgba(244, 67, 54, 0.1);
  border-radius: 4px;
}

.modal-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  width: 80%;
  max-width: 500px;
  background-color: #fff;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.modal-title {
  font-size: 20px;
  font-weight: bold;
  color: #333;
  text-align: center;
  flex: 1;
}

.modal-close-button {
  background: none;
  border: none;
  font-size: 24px;
  color: #333;
  cursor: pointer;
}

.modal-body {
  padding: 16px 0;
  max-height: 400px;
  overflow-y: auto;
}

.navigation-details {
  margin-bottom: 16px;
}

.navigation-detail-row {
  display: flex;
  margin-bottom: 12px;
}

.navigation-detail-label {
  width: 100px;
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.navigation-detail-value {
  flex: 1;
  font-size: 16px;
  color: #333;
}

.modal-footer {
  display: flex;
  justify-content: space-between;
  padding-top: 16px;
  border-top: 1px solid #e0e0e0;
}

.input {
  width: 100%;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 16px;
  font-size: 16px;
  box-sizing: border-box;
}

.text-area {
  height: 80px;
  resize: vertical;
}

.modal-buttons {
  display: flex;
  justify-content: space-between;
  gap: 10px;
}

.modal-button {
  flex: 1;
  padding: 12px;
  border: none;
  border-radius: 8px;
  text-align: center;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  transition: background-color 0.2s;
}

.cancel-button {
  background-color: #f1f2f6;
  color: #666;
}

.cancel-button:hover {
  background-color: #e1e2e6;
}

.save-button {
  background-color: #3498db;
  color: #fff;
}

.save-button:hover {
  background-color: #2980b9;
}

.complete-button {
  background-color: #4caf50;
  color: #fff;
}

.complete-button:hover {
  background-color: #45a049;
}

/* Responsive design */
@media (max-width: 768px) {
  .modal-content {
    width: 95%;
    margin: 10px;
  }
  
  .action-buttons-container {
    padding: 12px;
  }
  
  .action-button {
    width: 45px;
    height: 45px;
  }
  
  .action-button .icon {
    font-size: 20px;
  }
}
