.container {
  flex: 1;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.loadingContainer {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.8);
}

.loadingText {
  margin-top: 10px;
  color: #555;
  font-size: 16px;
}

.patientItem {
  display: flex;
  flex-direction: row;
  align-items: center;
  background-color: #fff;
  padding: 16px;
  margin-bottom: 1px;
  border-bottom: 1px solid #eee;
  cursor: pointer;
}

.avatarContainer {
  width: 50px;
  height: 50px;
  border-radius: 25px;
  background-color: rgba(16, 107, 0, 0.15);
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 16px;
}

.avatarText {
  font-size: 20px;
  font-weight: bold;
  color: rgba(16, 107, 0, 1);
}

.patientInfo {
  flex: 1;
}

.patientName {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.patientRole {
  font-size: 14px;
  color: #666;
}

.emptyContainer {
  display: flex;
  flex-direction: column;
  flex: 1;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.emptyIcon {
  margin-bottom: 20px;
}

.emptyText {
  font-size: 18px;
  font-weight: bold;
  color: #555;
  margin-bottom: 8px;
}

.emptySubtext {
  font-size: 14px;
  color: #777;
  text-align: center;
  margin-bottom: 30px;
}

.addButtonContainer {
  margin-top: 20px;
}

.floatingButtonContainer {
  position: fixed;
  right: 20px;
  bottom: 20px;
}

.patientList {
  width: 100%;
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.refreshButton {
  background-color: rgba(16, 107, 0, 1);
  color: white;
  border: none;
  padding: 10px 15px;
  border-radius: 4px;
  cursor: pointer;
  margin-bottom: 10px;
}

.refreshButton:hover {
  background-color: rgba(16, 107, 0, 0.8);
}
