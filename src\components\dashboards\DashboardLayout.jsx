import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { IoMenu, IoLogOut, IoNotifications, IoMic } from 'react-icons/io5';
import { useAuth } from '../../contexts/AuthContext';
import { getThemeForRole, COLORS } from '../../config/theme';
import NotificationBadge from '../notifications/NotificationBadge';
import VoiceCommandModal from '../voice/VoiceCommandModal';
import '../../styles/dashboards/dashboardLayout.css';

const DashboardLayout = ({
  children,
  title,
  roleName,
  menuItems = [],
  userRole = 'user',
  notifications = [],
  enableVoiceCommands = userRole === 'patient' // Only enable for patient by default
}) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { logout, user } = useAuth();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [voiceModalVisible, setVoiceModalVisible] = useState(false);

  // Get theme based on user role
  const theme = getThemeForRole(userRole);
  const primaryColor = theme.colors.primary;

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  const handleNavigation = (screen) => {
    toggleSidebar();
    if (screen === 'logout') {
      logout();
    } else {
      navigate(`/${screen.toLowerCase()}`);
    }
  };

  const getCurrentRouteName = () => {
    const path = location.pathname;
    return path.split('/')[1] || '';
  };

  const currentRoute = getCurrentRouteName();

  const handleNotificationPress = () => {
    navigate('/notifications');
  };

  // Close sidebar when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      const sidebar = document.getElementById('sidebar');
      const menuButton = document.getElementById('menu-button');
      
      if (sidebarOpen && 
          sidebar && 
          !sidebar.contains(event.target) && 
          menuButton && 
          !menuButton.contains(event.target)) {
        setSidebarOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [sidebarOpen]);

  return (
    <div className="dashboard-container">
      {/* Header with specific role color */}
      <div className="dashboard-header" style={{ backgroundColor: primaryColor }}>
        <button 
          id="menu-button"
          className="menu-button" 
          onClick={toggleSidebar}
        >
          <IoMenu size={28} color="white" />
        </button>
        <h1 className="header-title">{title}</h1>
        <div className="header-right">
          {enableVoiceCommands && (
            <button
              className="voice-button"
              onClick={() => setVoiceModalVisible(true)}
            >
              <IoMic size={24} color="white" />
            </button>
          )}
          <NotificationBadge
            count={notifications.length}
            onPress={handleNotificationPress}
          />
        </div>
      </div>

      {/* Sidebar */}
      <div 
        id="sidebar"
        className={`sidebar ${sidebarOpen ? 'open' : ''}`}
      >
        <div className="sidebar-header">
          <div className="profile-section">
            {/* Avatar with role-specific color */}
            <div className="profile-icon" style={{ backgroundColor: primaryColor }}>
              <span className="profile-icon-text">
                {user?.firstName?.charAt(0) || ''}{user?.lastName?.charAt(0) || ''}
              </span>
              {!user?.profileCompleted && (
                <div className="incomplete-profile-badge">
                  <span>!</span>
                </div>
              )}
            </div>
            <div className="profile-info">
              <div className="profile-name">
                {user?.firstName} {user?.lastName}
              </div>
              <div className="role-name">
                {roleName}
                {!user?.profileCompleted && <span className="incomplete-text"> • Incomplete Profile</span>}
              </div>
            </div>
          </div>
        </div>

        <div className="menu-container">
          {menuItems.map((item, index) => (
            <div
              key={index}
              className={`menu-item ${currentRoute === item.screen.toLowerCase() ? 'active-menu-item' : ''}`}
              onClick={() => handleNavigation(item.screen)}
            >
              <span className="menu-icon">
                {/* Use dynamic icon component based on item.icon */}
                {getIconComponent(item.icon)}
              </span>
              <span
                className={`menu-item-text ${currentRoute === item.screen.toLowerCase() ? 'active-menu-item-text' : ''}`}
              >
                {item.label}
              </span>
            </div>
          ))}

          <div className="divider"></div>

          <div
            className="menu-item"
            onClick={() => handleNavigation('logout')}
          >
            <IoLogOut size={24} color={COLORS.error} />
            <span className="menu-item-text" style={{ color: COLORS.error }}>Logout</span>
          </div>
        </div>
      </div>

      {/* Overlay when sidebar is open */}
      {sidebarOpen && (
        <div 
          className="sidebar-overlay" 
          onClick={toggleSidebar}
        ></div>
      )}

      {/* Main content */}
      <div className="dashboard-content">
        {children}
      </div>

      {/* Voice command modal */}
      {voiceModalVisible && (
        <VoiceCommandModal
          isVisible={voiceModalVisible}
          onClose={() => setVoiceModalVisible(false)}
        />
      )}
    </div>
  );
};

// Helper function to get icon component based on icon name
const getIconComponent = (iconName) => {
  // This is a simplified version - in a real app, you would import and use all the necessary icons
  // For now, we'll just return a placeholder
  return <span>{iconName}</span>;
};

export default DashboardLayout;
