:root {
  --doctor-primary-color: #0077B6;
}

.container {
  display: flex;
  flex-direction: column;
  background-color: #f8f9fa;
  min-height: 100%;
}

.header {
  background-color: #fff;
  padding: 16px;
  border-bottom: 1px solid #eaeaea;
}

.headerTitle {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.headerSubtitle {
  font-size: 14px;
  color: #666;
  margin-top: 4px;
}

.selectorContainer {
  background-color: #fff;
  margin: 16px;
  border-radius: 8px;
  padding: 10px;
  border: 1px solid #eaeaea;
  overflow-x: auto;
  display: flex;
}

.selectorButton {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 10px 14px;
  margin: 0 4px;
  border-radius: 8px;
  background-color: #f8f9fa;
  border: 1px solid #eaeaea;
  cursor: pointer;
  white-space: nowrap;
}

.selectedButton {
  background-color: #f0f7ff;
  border-color: #666;
}

.selectorText {
  margin-left: 6px;
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.selectedText {
  font-weight: 600;
}

.timeRangeContainer {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin: 0 16px 16px;
}

.timeRangeLabel {
  font-size: 14px;
  color: #666;
  margin-right: 10px;
}

.timeRangeButtons {
  display: flex;
  flex-direction: row;
}

.timeRangeButton {
  padding: 8px 14px;
  margin: 0 4px;
  border-radius: 8px;
  background-color: #f8f9fa;
  border: 1px solid #eaeaea;
  cursor: pointer;
}

.selectedTimeRange {
  background-color: #f0f7ff;
  border-color: var(--doctor-primary-color);
}

.timeRangeText {
  font-size: 13px;
  color: #666;
  font-weight: 500;
}

.selectedTimeRangeText {
  color: var(--doctor-primary-color);
  font-weight: 600;
}

.content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.section {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  border: 1px solid #eaeaea;
}

.sectionTitle {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 12px;
  color: #333;
}

.chartContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 8px 0;
}

.chart {
  border-radius: 8px;
  margin: 8px 0;
}

.legendContainer {
  display: flex;
  flex-direction: row;
  justify-content: center;
  margin-top: 8px;
}

.legendItem {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin: 0 8px;
}

.legendDot {
  width: 10px;
  height: 10px;
  border-radius: 5px;
  margin-right: 4px;
}

.legendText {
  font-size: 12px;
  color: #666;
}

.vitalIconContainer {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 12px;
}

.vitalCard {
  display: flex;
  flex-direction: row;
  align-items: center;
  background-color: #fff;
  border-radius: 8px;
  padding: 14px;
  margin-bottom: 10px;
  border: 1px solid #eaeaea;
  border-left-width: 4px;
  cursor: pointer;
}

.vitalInfo {
  flex: 1;
}

.vitalType {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.vitalValue {
  font-size: 14px;
  color: #666;
  margin-top: 2px;
}

.vitalDate {
  font-size: 12px;
  color: #999;
  margin-top: 2px;
}

.emptyContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px;
}

.emptyText {
  font-size: 14px;
  color: #666;
  text-align: center;
  margin-top: 8px;
}

.emptyChartContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.emptyChartText {
  font-size: 14px;
  color: #666;
  text-align: center;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 24px;
  flex: 1;
}

.loadingText {
  font-size: 14px;
  color: #666;
  margin-top: 8px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top: 4px solid var(--doctor-primary-color);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.4);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modalContainer {
  width: 90%;
  max-width: 500px;
  max-height: 80vh;
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
}

.modalHeader {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #eaeaea;
  background-color: #fff;
}

.modalTitle {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.closeButton {
  padding: 6px;
  cursor: pointer;
}

.modalContent {
  padding: 16px;
  max-height: calc(80vh - 60px);
  overflow-y: auto;
}

.modalVitalCard {
  display: flex;
  flex-direction: row;
  align-items: center;
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  border-left-width: 4px;
}

.notesContainer {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
}

.notesLabel {
  font-size: 14px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.notesText {
  font-size: 14px;
  color: #666;
}

.metadataContainer {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 16px;
}

.metadataLabel {
  font-size: 14px;
  font-weight: bold;
  color: #333;
  margin-bottom: 2px;
}

.metadataValue {
  font-size: 14px;
  color: #666;
  margin-bottom: 12px;
}
