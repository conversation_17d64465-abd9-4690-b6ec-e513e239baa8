import React, { useState, useEffect } from 'react';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import '../../../styles/dashboards/doctor/doctor_patient_detail/patientVitalsCharts.css';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

const PatientVitalsCharts = ({ patientId }) => {
  const [selectedVital, setSelectedVital] = useState('heartRate');
  const [timeRange, setTimeRange] = useState('week');
  const [loading, setLoading] = useState(true);
  const [vitalsData, setVitalsData] = useState({
    labels: [],
    datasets: []
  });

  // Mock data for demonstration
  useEffect(() => {
    // In a real app, this would fetch data from an API
    const mockData = generateMockData();
    setVitalsData(mockData);
    setLoading(false);
  }, [selectedVital, timeRange]);

  // Generate mock data for demonstration
  const generateMockData = () => {
    const labels = [];
    const data = [];
    
    // Generate dates for the selected time range
    const today = new Date();
    let days = 7;
    
    if (timeRange === 'month') {
      days = 30;
    } else if (timeRange === 'year') {
      days = 365;
    }
    
    // Generate data points
    for (let i = days; i >= 0; i--) {
      const date = new Date();
      date.setDate(today.getDate() - i);
      labels.push(date.toLocaleDateString());
      
      // Generate random values based on vital type
      let value;
      switch (selectedVital) {
        case 'heartRate':
          value = Math.floor(Math.random() * 30) + 60; // 60-90 bpm
          break;
        case 'bloodPressure':
          value = Math.floor(Math.random() * 40) + 100; // 100-140 mmHg (systolic)
          break;
        case 'temperature':
          value = (Math.random() * 1.5) + 36; // 36-37.5 °C
          break;
        case 'oxygenSaturation':
          value = Math.floor(Math.random() * 5) + 95; // 95-100%
          break;
        default:
          value = Math.floor(Math.random() * 100);
      }
      
      data.push(value);
    }
    
    return {
      labels,
      datasets: [
        {
          label: getVitalLabel(),
          data,
          borderColor: getVitalColor(),
          backgroundColor: getVitalColor(0.2),
          tension: 0.4,
        }
      ]
    };
  };

  // Get label for the selected vital
  const getVitalLabel = () => {
    switch (selectedVital) {
      case 'heartRate':
        return 'Heart Rate (BPM)';
      case 'bloodPressure':
        return 'Blood Pressure (mmHg)';
      case 'temperature':
        return 'Temperature (°C)';
      case 'oxygenSaturation':
        return 'Oxygen Saturation (%)';
      default:
        return 'Value';
    }
  };

  // Get color for the selected vital
  const getVitalColor = (opacity = 1) => {
    switch (selectedVital) {
      case 'heartRate':
        return `rgba(255, 99, 132, ${opacity})`;
      case 'bloodPressure':
        return `rgba(54, 162, 235, ${opacity})`;
      case 'temperature':
        return `rgba(255, 159, 64, ${opacity})`;
      case 'oxygenSaturation':
        return `rgba(75, 192, 192, ${opacity})`;
      default:
        return `rgba(153, 102, 255, ${opacity})`;
    }
  };

  // Chart options
  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: true,
        text: `Patient ${getVitalLabel()} - ${timeRange === 'week' ? 'Last 7 Days' : timeRange === 'month' ? 'Last 30 Days' : 'Last Year'}`,
      },
    },
    scales: {
      y: {
        beginAtZero: false,
      },
    },
  };

  return (
    <div className="patient-vitals-charts">
      {loading ? (
        <div className="loading-container">
          <div className="spinner">Loading...</div>
        </div>
      ) : (
        <>
          <div className="vitals-controls">
            <div className="vital-selector">
              <button 
                className={`vital-button ${selectedVital === 'heartRate' ? 'active' : ''}`}
                onClick={() => setSelectedVital('heartRate')}
              >
                Heart Rate
              </button>
              <button 
                className={`vital-button ${selectedVital === 'bloodPressure' ? 'active' : ''}`}
                onClick={() => setSelectedVital('bloodPressure')}
              >
                Blood Pressure
              </button>
              <button 
                className={`vital-button ${selectedVital === 'temperature' ? 'active' : ''}`}
                onClick={() => setSelectedVital('temperature')}
              >
                Temperature
              </button>
              <button 
                className={`vital-button ${selectedVital === 'oxygenSaturation' ? 'active' : ''}`}
                onClick={() => setSelectedVital('oxygenSaturation')}
              >
                Oxygen
              </button>
            </div>
            
            <div className="time-selector">
              <button 
                className={`time-button ${timeRange === 'week' ? 'active' : ''}`}
                onClick={() => setTimeRange('week')}
              >
                Week
              </button>
              <button 
                className={`time-button ${timeRange === 'month' ? 'active' : ''}`}
                onClick={() => setTimeRange('month')}
              >
                Month
              </button>
              <button 
                className={`time-button ${timeRange === 'year' ? 'active' : ''}`}
                onClick={() => setTimeRange('year')}
              >
                Year
              </button>
            </div>
          </div>
          
          <div className="chart-container">
            <Line data={vitalsData} options={options} />
          </div>
        </>
      )}
    </div>
  );
};

export default PatientVitalsCharts;
