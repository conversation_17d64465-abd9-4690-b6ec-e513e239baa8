import React, { useState, useEffect } from 'react';
import { Ionicons } from '@expo/vector-icons';
import { firebaseDoctorPatientsService } from '../../../../services/firebaseDoctorPatientsService';
import { ROLE_COLORS } from '../../../../config/theme';
import '../../../../styles/dashboards/doctor/doctor_patient_detail/patientMedicationsViewer.css';

const PatientMedicationsViewer = ({ patientId, patientName }) => {
  const [loading, setLoading] = useState(true);
  const [medications, setMedications] = useState([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedMedication, setSelectedMedication] = useState(null);
  const doctorColors = ROLE_COLORS.doctor;

  useEffect(() => {
    fetchPatientMedications();
  }, [patientId]);

  const fetchPatientMedications = async () => {
    if (!patientId) return;

    setLoading(true);
    try {
      // Fetch medications for the selected patient from Firebase
      const medicationsData = await firebaseDoctorPatientsService.getPatientMedications(patientId);
      console.log(`Fetched ${medicationsData.length} medications for patient ${patientId}`);
      setMedications(medicationsData || []);
    } catch (error) {
      console.error('Error fetching patient medications from Firebase:', error);
    } finally {
      setLoading(false);
    }
  };

  const openMedicationDetails = (medication) => {
    setSelectedMedication(medication);
    setModalVisible(true);
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return 'Unknown date';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  // Get frequency text
  const getFrequencyText = (frequency) => {
    if (!frequency) return 'As needed';

    if (frequency.type === 'daily') {
      return `${frequency.times} time(s) daily`;
    } else if (frequency.type === 'weekly') {
      const days = frequency.days || [];
      if (days.length === 7) return 'Every day of the week';
      if (days.length === 0) return 'Weekly';
      return `${days.join(', ')}`;
    } else if (frequency.type === 'monthly') {
      return `${frequency.dayOfMonth || 1} day of each month`;
    }

    return 'Custom schedule';
  };

  // Render the medications list
  const renderMedicationsList = () => {
    if (medications.length === 0) {
      return (
        <div className="emptyContainer">
          <Ionicons name="medkit-outline" size={48} color={doctorColors.primary} />
          <p className="emptyText">No medications found for this patient</p>
        </div>
      );
    }

    return (
      <div>
        {medications.map((item) => (
          <div
            key={item.id}
            className="medicationCard"
            onClick={() => openMedicationDetails(item)}
          >
            <div className="medicationIconContainer">
              <Ionicons name="medkit" size={24} color="#fff" />
            </div>
            <div className="medicationInfo">
              <p className="medicationName">{item.name}</p>
              <p className="medicationDosage">{item.dosage} {item.unit}</p>
              <p className="medicationFrequency">
                {getFrequencyText(item.frequency)}
              </p>
              {item.startDate && (
                <p className="medicationDate">
                  Started: {formatDate(item.startDate)}
                </p>
              )}
            </div>
            <Ionicons name="chevron-forward" size={24} color="#ccc" />
          </div>
        ))}
      </div>
    );
  };

  // Render the detail modal
  const renderDetailModal = () => {
    if (!selectedMedication || !modalVisible) return null;

    return (
      <div className="modalOverlay">
        <div className="modalContainer">
          <div className="modalHeader">
            <h3 className="modalTitle">Medication Details</h3>
            <button
              className="closeButton"
              onClick={() => setModalVisible(false)}
            >
              <Ionicons name="close" size={24} color="#333" />
            </button>
          </div>

          <div className="modalContent">
            <div className="modalMedicationHeader">
              <div className="modalMedicationIconContainer">
                <Ionicons name="medkit" size={32} color="#fff" />
              </div>
              <div>
                <p className="modalMedicationName">{selectedMedication.name}</p>
                <p className="modalMedicationDosage">
                  {selectedMedication.dosage} {selectedMedication.unit}
                </p>
              </div>
            </div>

            <div className="modalSection">
              <h4 className="modalSectionTitle">Schedule</h4>
              <p className="modalSectionText">
                {getFrequencyText(selectedMedication.frequency)}
              </p>
              {selectedMedication.frequency && selectedMedication.frequency.specificTime && (
                <p className="modalSectionText">
                  Time: {selectedMedication.frequency.specificTime}
                </p>
              )}
            </div>

            <div className="modalSection">
              <h4 className="modalSectionTitle">Duration</h4>
              <p className="modalSectionText">
                Start Date: {formatDate(selectedMedication.startDate)}
              </p>
              {selectedMedication.endDate && (
                <p className="modalSectionText">
                  End Date: {formatDate(selectedMedication.endDate)}
                </p>
              )}
            </div>

            {selectedMedication.instructions && (
              <div className="modalSection">
                <h4 className="modalSectionTitle">Instructions</h4>
                <p className="modalSectionText">{selectedMedication.instructions}</p>
              </div>
            )}

            {selectedMedication.reason && (
              <div className="modalSection">
                <h4 className="modalSectionTitle">Reason</h4>
                <p className="modalSectionText">{selectedMedication.reason}</p>
              </div>
            )}

            {selectedMedication.sideEffects && (
              <div className="modalSection">
                <h4 className="modalSectionTitle">Possible Side Effects</h4>
                <p className="modalSectionText">{selectedMedication.sideEffects}</p>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="container">
      <div className="header">
        <h2 className="headerTitle">
          {patientName ? `${patientName}'s Medications` : 'Patient Medications'}
        </h2>
        <p className="headerSubtitle">
          View your patient's medication regimen
        </p>
      </div>

      {loading ? (
        <div className="loadingContainer">
          <div className="spinner" style={{ color: doctorColors.primary }}>Loading...</div>
          <p className="loadingText">Loading patient medications...</p>
        </div>
      ) : (
        <div className="content">
          <div className="section">
            <h3 className="sectionTitle">Current Medications</h3>
            {renderMedicationsList()}
          </div>
        </div>
      )}

      {renderDetailModal()}
    </div>
  );
};

export default PatientMedicationsViewer;
