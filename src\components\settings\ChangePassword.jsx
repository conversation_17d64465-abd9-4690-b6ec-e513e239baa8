import React, { useState } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { getThemeForRole, COLORS } from '../../config/theme';
import { getAuth, EmailAuthProvider, reauthenticateWithCredential, updatePassword } from 'firebase/auth';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faLock, faEye, faEyeSlash } from '@fortawesome/free-solid-svg-icons';
import '../../styles/settings/changePassword.css';

const ChangePassword = ({ navigation }) => {
  const { user, changePassword } = useAuth();
  const theme = getThemeForRole(user?.role || 'default');
  const primaryColor = theme.colors.primary;

  const [formData, setFormData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });
  const [loading, setLoading] = useState(false);
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const handleChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const validateForm = () => {
    if (!formData.currentPassword) {
      alert('Please enter your current password');
      return false;
    }

    if (!formData.newPassword) {
      alert('Please enter a new password');
      return false;
    }

    if (formData.newPassword.length < 6) {
      alert('New password must be at least 6 characters long');
      return false;
    }

    if (formData.newPassword !== formData.confirmPassword) {
      alert('New password and confirm password do not match');
      return false;
    }

    if (formData.currentPassword === formData.newPassword) {
      alert('New password must be different from current password');
      return false;
    }

    return true;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    try {
      setLoading(true);

      // Get the current Firebase auth instance
      const auth = getAuth();
      const user = auth.currentUser;

      if (!user) {
        throw new Error('You must be logged in to change your password.');
      }

      console.log("Starting password change process for user:", user.email);

      try {
        // Create credential
        const credential = EmailAuthProvider.credential(
          user.email,
          formData.currentPassword
        );

        console.log("Credential created, attempting reauthentication");

        // Reauthenticate
        await reauthenticateWithCredential(user, credential);

        console.log("Reauthentication successful, updating password");

        // Update password
        await updatePassword(user, formData.newPassword);

        console.log("Password updated successfully");

        // Show success message
        alert('Your password has been changed successfully');
        navigation.goBack();
      } catch (authError) {
        console.error("Authentication error:", authError);

        if (authError.code === 'auth/wrong-password') {
          throw new Error('Current password is incorrect.');
        } else {
          throw new Error('Authentication failed: ' + (authError.message || 'Please try again.'));
        }
      }
    } catch (error) {
      alert(error.message || 'Failed to change password. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container">
      <div className="content">
        <div className="headerContainer">
          <FontAwesomeIcon icon={faLock} size="2x" style={{ color: primaryColor }} />
          <h1 className="headerText" style={{ color: primaryColor }}>Change Password</h1>
          <p className="subHeaderText">
            Update your password to keep your account secure
          </p>
        </div>

        <div className="formContainer">
          {/* Current Password */}
          <div className="inputContainer">
            <label className="inputLabel">Current Password</label>
            <div className="inputBox">
              <FontAwesomeIcon icon={faLock} className="icon" />
              <input
                className="input"
                type={showCurrentPassword ? "text" : "password"}
                placeholder="Enter current password"
                value={formData.currentPassword}
                onChange={(e) => handleChange('currentPassword', e.target.value)}
              />
              <button
                className="passwordIcon"
                onClick={() => setShowCurrentPassword(!showCurrentPassword)}
              >
                <FontAwesomeIcon
                  icon={showCurrentPassword ? faEyeSlash : faEye}
                  style={{ color: "rgba(16, 107, 0, 1)" }}
                />
              </button>
            </div>
          </div>

          {/* New Password */}
          <div className="inputContainer">
            <label className="inputLabel">New Password</label>
            <div className="inputBox">
              <FontAwesomeIcon icon={faLock} className="icon" />
              <input
                className="input"
                type={showNewPassword ? "text" : "password"}
                placeholder="Enter new password"
                value={formData.newPassword}
                onChange={(e) => handleChange('newPassword', e.target.value)}
              />
              <button
                className="passwordIcon"
                onClick={() => setShowNewPassword(!showNewPassword)}
              >
                <FontAwesomeIcon
                  icon={showNewPassword ? faEyeSlash : faEye}
                  style={{ color: "rgba(16, 107, 0, 1)" }}
                />
              </button>
            </div>
            <p className="passwordHint">Password must be at least 6 characters long</p>
          </div>

          {/* Confirm Password */}
          <div className="inputContainer">
            <label className="inputLabel">Confirm New Password</label>
            <div className="inputBox">
              <FontAwesomeIcon icon={faLock} className="icon" />
              <input
                className="input"
                type={showConfirmPassword ? "text" : "password"}
                placeholder="Confirm new password"
                value={formData.confirmPassword}
                onChange={(e) => handleChange('confirmPassword', e.target.value)}
              />
              <button
                className="passwordIcon"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              >
                <FontAwesomeIcon
                  icon={showConfirmPassword ? faEyeSlash : faEye}
                  style={{ color: "rgba(16, 107, 0, 1)" }}
                />
              </button>
            </div>
          </div>

          {/* Submit Button */}
          <button
            className={`button ${loading ? 'buttonDisabled' : ''}`}
            style={{ backgroundColor: primaryColor }}
            onClick={handleSubmit}
            disabled={loading}
          >
            {loading ? (
              <div className="spinner"></div>
            ) : (
              <span className="buttonText">Update Password</span>
            )}
          </button>

          {/* Cancel Button */}
          <button
            className="cancelButton"
            onClick={() => navigation.goBack()}
            disabled={loading}
          >
            <span className="cancelButtonText" style={{ color: primaryColor }}>Cancel</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default ChangePassword;
