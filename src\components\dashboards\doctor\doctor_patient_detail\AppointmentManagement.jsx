import React, { useState, useEffect } from 'react';
import { format } from 'date-fns';
import { 
  <PERSON><PERSON>, 
  Card, 
  CardContent, 
  Typography, 
  Chip, 
  CircularProgress, 
  Modal, 
  Box, 
  List, 
  ListItem, 
  ListItemText, 
  Divider, 
  Pagination, 
  Alert, 
  Snackbar 
} from '@mui/material';
import { firebaseAppointmentsService } from '../../../services/firebaseAppointmentsService';
import '../../../../styles/dashboards/doctor/doctor_patient_detail/appointmentManagement.css';

const ITEMS_PER_PAGE = 10;

const AppointmentManagement = () => {
  const [appointments, setAppointments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedAppointment, setSelectedAppointment] = useState(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [page, setPage] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [alert, setAlert] = useState({ open: false, message: '', severity: 'info' });

  useEffect(() => {
    fetchAppointments();
  }, [page]);

  const fetchAppointments = async () => {
    try {
      setLoading(true);

      // Use Firebase service to get doctor appointments
      const options = {
        includePatientDetails: true,
        dateOrder: 'desc',  // Most recent appointments first
        timeOrder: 'asc'    // Earlier times first for same day
      };

      // Get appointments from Firebase
      const fetchedAppointments = await firebaseAppointmentsService.getDoctorAppointments(options);
      console.log(`Fetched ${fetchedAppointments.length} total appointments`);

      // Apply pagination manually
      const startIndex = page * ITEMS_PER_PAGE;
      const endIndex = startIndex + ITEMS_PER_PAGE;
      const paginatedAppointments = fetchedAppointments.slice(startIndex, endIndex);

      setAppointments(paginatedAppointments);

      // Calculate total pages
      const totalItems = fetchedAppointments.length;
      setTotalPages(Math.ceil(totalItems / ITEMS_PER_PAGE));

      console.log(`Showing appointments ${startIndex+1} to ${Math.min(endIndex, totalItems)} of ${totalItems}`);
    } catch (error) {
      console.error('Error fetching appointments from Firebase:', error);
      showAlert('Failed to fetch appointments', 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    setPage(0);
    await fetchAppointments();
    setRefreshing(false);
  };

  const handleUpdateStatus = async (appointmentId, newStatus) => {
    try {
      setLoading(true);

      // Update appointment status in Firebase
      await firebaseAppointmentsService.updateAppointment(appointmentId, {
        status: newStatus,
        updatedAt: new Date().toISOString()
      });

      // Refresh the appointments list
      await fetchAppointments();

      showAlert('Appointment status updated successfully', 'success');
    } catch (error) {
      console.error('Error updating appointment status in Firebase:', error);
      showAlert('Failed to update appointment status', 'error');
    } finally {
      setLoading(false);
    }
  };

  const showAlert = (message, severity = 'info') => {
    setAlert({ open: true, message, severity });
  };

  const handleCloseAlert = () => {
    setAlert({ ...alert, open: false });
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return '#FFA500';
      case 'confirmed':
        return '#4CAF50';
      case 'cancelled':
        return '#F44336';
      case 'completed':
        return '#2196F3';
      default:
        return '#757575';
    }
  };

  const renderAppointmentCard = (appointment) => (
    <Card
      key={appointment.id}
      className="card"
      onClick={() => {
        setSelectedAppointment(appointment);
        setShowDetailsModal(true);
      }}
    >
      <CardContent>
        <div className="cardHeader">
          <Typography variant="h6">{format(new Date(appointment.date), 'MMMM dd, yyyy')}</Typography>
          <Chip
            label={appointment.status.toUpperCase()}
            style={{ backgroundColor: getStatusColor(appointment.status), color: 'white' }}
            className="statusChip"
          />
        </div>
        <Typography variant="body1">Time: {appointment.time || 'Not specified'}</Typography>
        <Typography variant="body1">Type: {appointment.type || 'In-person'}</Typography>
        <Typography variant="body1">Patient: {appointment.patient?.name || appointment.patientId || 'Unknown Patient'}</Typography>
      </CardContent>
    </Card>
  );

  const renderDetailsModal = () => (
    <Modal
      open={showDetailsModal}
      onClose={() => setShowDetailsModal(false)}
      aria-labelledby="appointment-details-modal"
    >
      <Box className="modal">
        {selectedAppointment && (
          <div>
            <Typography variant="h5">Appointment Details</Typography>
            <Divider className="divider" />

            <List>
              <ListItem>
                <ListItemText 
                  primary="Date" 
                  secondary={format(new Date(selectedAppointment.date), 'MMMM dd, yyyy')} 
                />
              </ListItem>
              <ListItem>
                <ListItemText 
                  primary="Time" 
                  secondary={selectedAppointment.time} 
                />
              </ListItem>
              <ListItem>
                <ListItemText 
                  primary="Type" 
                  secondary={selectedAppointment.type} 
                />
              </ListItem>
              <ListItem>
                <ListItemText 
                  primary="Patient" 
                  secondary={selectedAppointment.patient?.name || selectedAppointment.patientId || 'Unknown Patient'} 
                />
              </ListItem>
              <ListItem>
                <ListItemText 
                  primary="Reason" 
                  secondary={selectedAppointment.reason || 'Not specified'} 
                />
              </ListItem>
            </List>

            <Divider className="divider" />

            <div className="actionButtons">
              {selectedAppointment.status === 'pending' && (
                <>
                  <Button
                    variant="contained"
                    onClick={() => handleUpdateStatus(selectedAppointment.id, 'confirmed')}
                    className="actionButton confirmButton"
                  >
                    Confirm
                  </Button>
                  <Button
                    variant="contained"
                    onClick={() => handleUpdateStatus(selectedAppointment.id, 'cancelled')}
                    className="actionButton cancelButton"
                  >
                    Cancel
                  </Button>
                </>
              )}
              {selectedAppointment.status === 'confirmed' && (
                <Button
                  variant="contained"
                  onClick={() => handleUpdateStatus(selectedAppointment.id, 'completed')}
                  className="actionButton completeButton"
                >
                  Mark as Completed
                </Button>
              )}
            </div>
          </div>
        )}
      </Box>
    </Modal>
  );

  if (loading && !refreshing) {
    return (
      <div className="loadingContainer">
        <CircularProgress />
      </div>
    );
  }

  return (
    <div className="container">
      <Button 
        variant="contained" 
        color="primary" 
        onClick={handleRefresh}
        disabled={refreshing}
        style={{ margin: '16px' }}
      >
        {refreshing ? 'Refreshing...' : 'Refresh'}
      </Button>
      
      {appointments.length === 0 ? (
        <Typography className="noAppointments">No appointments found</Typography>
      ) : (
        <>
          {appointments.map(renderAppointmentCard)}
          <div className="pagination">
            <Pagination 
              count={totalPages} 
              page={page + 1} 
              onChange={(event, value) => setPage(value - 1)} 
            />
          </div>
        </>
      )}
      
      {renderDetailsModal()}
      
      <Snackbar 
        open={alert.open} 
        autoHideDuration={6000} 
        onClose={handleCloseAlert}
      >
        <Alert onClose={handleCloseAlert} severity={alert.severity}>
          {alert.message}
        </Alert>
      </Snackbar>
    </div>
  );
};

export default AppointmentManagement;
