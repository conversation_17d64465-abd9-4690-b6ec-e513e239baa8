import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { IoCalendar, IoAlertCircle, IoPeople, IoCheckmarkCircle, IoChatbubble, IoDocument, IoAdd, IoStatsChart, IoFlash, IoChevronUp, IoChevronDown, IoMenu, IoMic } from 'react-icons/io5';
import DashboardLayout from '../DashboardLayout';
import DashboardCard from '../DashboardCard';
import DashboardChart from '../DashboardChart';
import UpcomingList from '../UpcomingList';
import { useAuth } from '../../../contexts/AuthContext';
import AddPatientButton from './button/AddPatientButton';
import PatientVitalsCharts from './PatientVitalsCharts';
import PatientProgressNotes from './PatientProgressNotes';
import { firebaseAppointmentsService } from '../../../services/firebaseAppointmentsService';
import { format } from 'date-fns';
import { ROLE_COLORS, COLORS } from '../../../config/theme';
import '../../../styles/dashboards/doctor/doctorDashboard.css';

const DoctorDashboard = ({ notifications = [] }) => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const doctorColors = ROLE_COLORS.doctor;

  // State
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [stats, setStats] = useState({
    todayAppointments: '0',
    pendingRequests: '0',
    totalPatients: '0',
    completedToday: '0'
  });
  const [selectedPatient] = useState(null);
  const [appointments, setAppointments] = useState([]);
  const [requests, setRequests] = useState([]);
  const [expandedSections, setExpandedSections] = useState({
    overview: true,
    appointments: true,
    requests: true,
    vitals: true,
    notes: true,
    quickActions: true
  });

  // Animation states
  const [fadeIn, setFadeIn] = useState(false);
  const [headerAnimated, setHeaderAnimated] = useState(false);
  const [cardsAnimated, setCardsAnimated] = useState(false);
  const [chartsAnimated, setChartsAnimated] = useState(false);
  const [listsAnimated, setListsAnimated] = useState(false);
  const [actionsAnimated, setActionsAnimated] = useState(false);

  // Empty weekly stats structure that will be populated with real data
  const [weeklyStats, setWeeklyStats] = useState({
    labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
    datasets: [
      {
        data: [0, 0, 0, 0, 0, 0, 0],
        color: (opacity = 1) => `rgba(170, 86, 255, ${opacity})`,
        strokeWidth: 2
      }
    ],
    legend: ['Appointments']
  });

  // Empty diagnosis data structure that will be populated with real data
  const [diagnosisData] = useState({
    data: []
  });

  const menuItems = [
    { label: 'Dashboard', icon: 'home', screen: 'DoctorDashboard' },
    { label: 'Appointment', icon: 'calendar', screen: 'DoctorAppointments' },
    { label: 'Patients', icon: 'people', screen: 'Patients' },
    { label: 'Patient Health', icon: 'pulse', screen: 'PatientHealthMonitoring' },
    { label: 'Progress Notes', icon: 'document', screen: 'PatientProgressNotes' },
    { label: 'Messages', icon: 'chatbubble', screen: 'Messages' },
    { label: 'Prescriptions', icon: 'medical', screen: 'Prescriptions' },
    { label: 'My QR Code', icon: 'qr-code', screen: 'UserQRCode' },
    { label: 'Profile', icon: 'person', screen: 'Profile' },
    { label: 'Settings', icon: 'settings', screen: 'Settings' }
  ];

  // Fetch appointments and other dashboard data from Firebase
  const fetchAppointments = async () => {
    if (!refreshing) {
      setLoading(true); // Set loading to true when fetching data (unless refreshing)
    }

    try {
      // Get today's date in YYYY-MM-DD format
      const today = new Date();
      const formattedDate = format(today, 'yyyy-MM-dd');

      // Fetch all appointments first, then filter for today
      const options = {
        includePatientDetails: true,
        dateOrder: 'asc',  // Earliest appointments first
        timeOrder: 'asc'   // Earlier times first
      };

      // Get all appointments and filter for today in JavaScript
      const allAppointments = await firebaseAppointmentsService.getDoctorAppointments(options);
      const todayAppointments = allAppointments.filter(appointment =>
        appointment.date === formattedDate
      );

      // Get completed appointments for today
      const completedToday = allAppointments.filter(appointment =>
        appointment.date === formattedDate &&
        appointment.status?.toLowerCase() === 'completed'
      );

      console.log(`Fetched ${allAppointments.length} total appointments, ${todayAppointments.length} for today`);

      // Format appointments for display in UpcomingList
      const formattedAppointments = todayAppointments.map(appointment => {
        // Format the time for display
        const appointmentTime = appointment.time;
        const timeDisplay = appointmentTime ?
          `Today, ${appointmentTime}` :
          'Time not specified';

        // Get patient name - try different sources
        let patientName = 'Unknown Patient';
        if (appointment.patient && appointment.patient.name) {
          patientName = appointment.patient.name;
        } else if (appointment.patient && appointment.patient.displayName) {
          patientName = appointment.patient.displayName;
        } else if (appointment.patientName) {
          patientName = appointment.patientName;
        }

        return {
          id: appointment.id,
          title: patientName,
          description: appointment.reason || 'Medical Appointment',
          time: timeDisplay,
          type: 'appointment',
          status: appointment.status?.toLowerCase() || 'pending',
          appointmentDate: appointment.date,
          appointmentTime: appointment.time,
          patientId: appointment.patientId
        };
      });

      // Update state with formatted appointments
      setAppointments(formattedAppointments);

      // Get pending requests (appointments with status 'pending')
      const pendingRequests = allAppointments.filter(app =>
        app.status?.toLowerCase() === 'pending'
      );

      // Format pending requests for display
      const formattedRequests = pendingRequests.map(request => {
        // Get patient name
        let patientName = 'Unknown Patient';
        if (request.patient && request.patient.name) {
          patientName = request.patient.name;
        } else if (request.patient && request.patient.displayName) {
          patientName = request.patient.displayName;
        } else if (request.patientName) {
          patientName = request.patientName;
        }

        return {
          id: request.id,
          title: 'Appointment Request',
          description: `${patientName} - ${request.reason || 'Medical Appointment'}`,
          time: `Requested for ${request.date}`,
          type: 'appointment',
          status: 'pending'
        };
      });

      // Update requests state
      setRequests(formattedRequests);

      // Calculate weekly stats (appointments per day for the last 7 days)
      const last7Days = Array(7).fill(0).map((_, i) => {
        const d = new Date();
        d.setDate(d.getDate() - i);
        return format(d, 'yyyy-MM-dd');
      }).reverse();

      const weeklyAppointmentCounts = last7Days.map(date => {
        return allAppointments.filter(app => app.date === date).length;
      });

      // Update weekly stats
      setWeeklyStats({
        labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
        datasets: [
          {
            data: weeklyAppointmentCounts,
            color: (opacity = 1) => `rgba(170, 86, 255, ${opacity})`,
            strokeWidth: 2
          }
        ],
        legend: ['Appointments']
      });

      // Get unique patients count
      const uniquePatientIds = new Set();
      allAppointments.forEach(app => {
        if (app.patientId) {
          uniquePatientIds.add(app.patientId);
        }
      });

      // Update all stats
      setStats({
        todayAppointments: todayAppointments.length.toString(),
        pendingRequests: pendingRequests.length.toString(),
        totalPatients: uniquePatientIds.size.toString(),
        completedToday: completedToday.length.toString()
      });

    } catch (error) {
      console.error('Error fetching appointments:', error);
      // Use a toast notification library here instead of react-native-flash-message
      // For example: toast.error('Failed to load appointments');
    } finally {
      setLoading(false); // Set loading to false when done, regardless of success or failure
    }
  };

  // Load appointments when component mounts
  useEffect(() => {
    fetchAppointments();
  }, []);

  // Animation sequence when data is loaded
  useEffect(() => {
    if (!loading) {
      // Start animations in sequence with timeouts
      setFadeIn(true);
      
      setTimeout(() => {
        setHeaderAnimated(true);
      }, 500);
      
      setTimeout(() => {
        setCardsAnimated(true);
      }, 900);
      
      setTimeout(() => {
        setChartsAnimated(true);
      }, 1000);
      
      setTimeout(() => {
        setListsAnimated(true);
      }, 1100);
      
      setTimeout(() => {
        setActionsAnimated(true);
      }, 1200);
    }
  }, [loading]);

  // Toggle section expansion
  const toggleSection = (section) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const onRefresh = async () => {
    setRefreshing(true);
    // Reset animation values
    setFadeIn(false);
    setHeaderAnimated(false);
    setCardsAnimated(false);
    setChartsAnimated(false);
    setListsAnimated(false);
    setActionsAnimated(false);

    // Fetch real data
    await fetchAppointments();
    setRefreshing(false);
    
    // Restart animations
    setFadeIn(true);
    setTimeout(() => {
      setHeaderAnimated(true);
    }, 500);
    
    setTimeout(() => {
      setCardsAnimated(true);
      setChartsAnimated(true);
      setListsAnimated(true);
      setActionsAnimated(true);
    }, 900);
  };

  // Section header component with toggle functionality
  const SectionHeader = ({ title, section, icon }) => (
    <div 
      className="section-header"
      onClick={() => toggleSection(section)}
      style={{ cursor: 'pointer' }}
    >
      <div className="section-header-content">
        {icon === 'stats-chart' && <IoStatsChart size={22} color={doctorColors.primary} className="section-icon" />}
        {icon === 'calendar' && <IoCalendar size={22} color={doctorColors.primary} className="section-icon" />}
        {icon === 'alert-circle' && <IoAlertCircle size={22} color={doctorColors.primary} className="section-icon" />}
        {icon === 'pulse' && <IoStatsChart size={22} color={doctorColors.primary} className="section-icon" />}
        {icon === 'document-text' && <IoDocument size={22} color={doctorColors.primary} className="section-icon" />}
        {icon === 'flash' && <IoFlash size={22} color={doctorColors.primary} className="section-icon" />}
        <span className="section-title">{title}</span>
      </div>
      {expandedSections[section] ? 
        <IoChevronUp size={22} color={COLORS.textMedium} /> : 
        <IoChevronDown size={22} color={COLORS.textMedium} />
      }
    </div>
  );

  return (
    <DashboardLayout
      title="Doctor Dashboard"
      roleName="Doctor"
      menuItems={menuItems}
      userRole="doctor"
      notifications={notifications}
    >
      {loading ? (
        <div className="loading-container">
          <div className="spinner-border" role="status" style={{ color: doctorColors.primary }}>
            <span className="visually-hidden">Loading...</span>
          </div>
          <div className="loading-text"></div>
        </div>
      ) : (
        <div className={`scroll-view ${fadeIn ? 'fade-in' : ''}`}>
          {/* Enhanced Header Section with Animation */}
          <div className={`header-section ${headerAnimated ? 'slide-down' : ''}`}>
            <div className="header-gradient">
              <div className="greeting">
                <div>
                  <div className="greeting-text">
                    Hello, Dr. {user?.lastName || 'Doctor'}
                  </div>
                  <div className="date-text">
                    {new Date().toLocaleDateString('en-US', {
                      weekday: 'long',
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </div>
                </div>
                <div className="header-actions">
                  <AddPatientButton />
                </div>
              </div>
            </div>
          </div>

          <div className="content-section">
            {/* Today's Overview Section */}
            <SectionHeader title="Today's Overview" section="overview" icon="stats-chart" />

            {expandedSections.overview && (
              <div className={`section-content ${cardsAnimated ? 'slide-up' : ''}`}>
                <div className="cards-container">
                  <div className="card-row">
                    <DashboardCard
                      title="Appointments"
                      value={stats.todayAppointments}
                      icon="calendar"
                      iconColor="#00897B"
                      width="48%"
                      gradientStart="#E8F5E9"
                      gradientEnd="#C8E6C9"
                      onPress={() => navigate('/doctor-appointments')}
                    />
                    <DashboardCard
                      title="Pending Requests"
                      value={stats.pendingRequests}
                      icon="alert-circle"
                      iconColor="#F57C00"
                      width="48%"
                      gradientStart="#FFF3E0"
                      gradientEnd="#FFE0B2"
                      onPress={() => navigate('/requests')}
                    />
                  </div>
                  <div className="card-row">
                    <DashboardCard
                      title="Total Patients"
                      value={stats.totalPatients}
                      icon="people"
                      iconColor="#3949AB"
                      width="48%"
                      gradientStart="#E8EAF6"
                      gradientEnd="#C5CAE9"
                      onPress={() => navigate('/patients')}
                    />
                    <DashboardCard
                      title="Completed Today"
                      value={stats.completedToday}
                      icon="checkmark-circle"
                      iconColor="#43A047"
                      width="48%"
                      gradientStart="#E8F5E9"
                      gradientEnd="#C8E6C9"
                      onPress={() => navigate('/completed-appointments')}
                    />
                  </div>
                  <div className="card-row">
                    <DashboardCard
                      title="Chat Consultations"
                      value="Start Now"
                      icon="chatbubble"
                      iconColor="#9C27B0"
                      width="100%"
                      gradientStart="#E1BEE7"
                      gradientEnd="#CE93D8"
                      onPress={() => navigate('/messages')}
                      style={{
                        borderWidth: 2,
                        borderColor: doctorColors.primary,
                        boxShadow: '0 8px 12px rgba(0, 0, 0, 0.35)',
                      }}
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Charts Section */}
            <div className={`section-container ${chartsAnimated ? 'slide-up' : ''}`}>
              <DashboardChart
                title="Weekly Appointments"
                subtitle="Last 7 days"
                data={weeklyStats}
                type="bar"
                color={doctorColors.primary}
              />
            </div>

            {diagnosisData.data.length > 0 && (
              <div className={`section-container ${chartsAnimated ? 'slide-up' : ''}`}>
                <DashboardChart
                  title="Diagnosis Distribution"
                  subtitle="Last 30 days"
                  data={diagnosisData}
                  type="pie"
                  height={200}
                />
              </div>
            )}

            {/* Appointments Section */}
            <SectionHeader title="Today's Appointments" section="appointments" icon="calendar" />

            {expandedSections.appointments && (
              <div className={`${listsAnimated ? 'slide-up' : ''}`}>
                <UpcomingList
                  data={appointments}
                  onItemPress={(item) => navigate(`/appointment-detail/${item.id}`)}
                  onViewAll={() => navigate('/doctor-appointments')}
                  emptyText="No appointments scheduled for today"
                  backgroundColor="#ffffff"
                />
              </div>
            )}

            {/* Requests Section */}
            <SectionHeader title="Pending Requests" section="requests" icon="alert-circle" />

            {expandedSections.requests && (
              <div className={`${listsAnimated ? 'slide-up' : ''}`}>
                <UpcomingList
                  data={requests}
                  onItemPress={(item) => navigate(`/request-detail/${item.id}`)}
                  onViewAll={() => navigate('/requests')}
                  emptyText="No pending requests"
                  backgroundColor="#ffffff"
                />
              </div>
            )}

            {/* Patient Vitals Section */}
            <SectionHeader title="Patient Vitals Tracking" section="vitals" icon="pulse" />

            {expandedSections.vitals && (
              <div className={`section-container ${listsAnimated ? 'slide-up' : ''}`} style={{ padding: 0 }}>
                <div className="vitals-charts-container">
                  <PatientVitalsCharts />
                </div>
              </div>
            )}

            {/* Progress Notes Section */}
            <SectionHeader title="Progress Notes" section="notes" icon="document-text" />

            {expandedSections.notes && (
              <div className={`section-container ${listsAnimated ? 'slide-up' : ''}`}>
                <PatientProgressNotes
                  patientId={selectedPatient?.id}
                  token={user?.token}
                />
              </div>
            )}

            {/* Quick Actions Section */}
            <SectionHeader title="Quick Actions" section="quickActions" icon="flash" />

            {expandedSections.quickActions && (
              <div className={`quick-actions ${actionsAnimated ? 'slide-up' : ''}`}>
                <div className="action-buttons">
                  <div
                    className="action-button"
                    onClick={() => navigate('/new-appointment')}
                  >
                    <div className="action-icon" style={{ backgroundColor: '#E8F5E9' }}>
                      <IoCalendar size={24} color="#43A047" />
                    </div>
                    <div className="action-text">Create Appointment</div>
                  </div>

                  <div
                    className="action-button"
                    onClick={() => navigate('/messages')}
                  >
                    <div className="action-icon" style={{ backgroundColor: '#E3F2FD' }}>
                      <IoChatbubble size={24} color="#2196F3" />
                    </div>
                    <div className="action-text">Send Message</div>
                  </div>

                  <div
                    className="action-button"
                    onClick={() => navigate('/new-prescription')}
                  >
                    <div className="action-icon" style={{ backgroundColor: '#E1F5FE' }}>
                      <IoDocument size={24} color="#0288D1" />
                    </div>
                    <div className="action-text">Write Prescription</div>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Floating Action Button */}
          <div
            className="fab"
            style={{ backgroundColor: doctorColors.primary }}
            onClick={() => navigate('/new-appointment')}
          >
            <IoAdd size={24} color="#fff" />
          </div>
        </div>
      )}
    </DashboardLayout>
  );
};

export default DoctorDashboard;
