/* Patient Progress Notes CSS */
.patient-progress-notes {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.notes-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.notes-title {
  font-size: 18px;
  font-weight: 600;
  color: #212121;
  margin: 0;
}

.create-note-button {
  display: flex;
  align-items: center;
  background-color: #aa56ff;
  color: white;
  border: none;
  border-radius: 20px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.create-note-button span {
  margin-left: 6px;
}

.create-note-button:hover {
  background-color: #9541e0;
}

.notes-filters {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.search-container {
  flex: 1;
  min-width: 200px;
  position: relative;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #757575;
}

.search-input {
  width: 100%;
  padding: 10px 10px 10px 36px;
  border: 1px solid #e0e0e0;
  border-radius: 20px;
  font-size: 14px;
}

.search-input:focus {
  outline: none;
  border-color: #aa56ff;
}

.filter-container {
  position: relative;
  min-width: 150px;
}

.filter-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #757575;
}

.filter-select {
  width: 100%;
  padding: 10px 10px 10px 36px;
  border: 1px solid #e0e0e0;
  border-radius: 20px;
  font-size: 14px;
  appearance: none;
  background-color: white;
  cursor: pointer;
}

.filter-select:focus {
  outline: none;
  border-color: #aa56ff;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top: 4px solid #aa56ff;
  width: 30px;
  height: 30px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.notes-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

.note-card {
  background-color: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
}

.note-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.note-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.note-category {
  display: flex;
  align-items: center;
  color: #aa56ff;
  font-size: 14px;
  font-weight: 500;
}

.note-category span {
  margin-left: 6px;
}

.note-date {
  font-size: 12px;
  color: #757575;
}

.note-title {
  font-size: 16px;
  font-weight: 600;
  color: #212121;
  margin: 0 0 8px 0;
}

.note-content {
  font-size: 14px;
  color: #757575;
  margin: 0 0 12px 0;
  line-height: 1.4;
}

.note-footer {
  display: flex;
  justify-content: flex-end;
}

.note-doctor {
  font-size: 12px;
  color: #616161;
  font-style: italic;
}

.empty-notes {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  text-align: center;
}

.empty-notes p {
  margin: 16px 0;
  color: #757575;
  font-size: 16px;
}

.create-first-note-button {
  background-color: #aa56ff;
  color: white;
  border: none;
  border-radius: 20px;
  padding: 10px 20px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.create-first-note-button:hover {
  background-color: #9541e0;
}

.select-patient-message {
  font-style: italic;
  color: #9e9e9e !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .notes-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .create-note-button {
    margin-top: 12px;
  }
  
  .notes-filters {
    flex-direction: column;
  }
  
  .notes-list {
    grid-template-columns: 1fr;
  }
}
