import React, { useState } from 'react';
import { IoPersonAdd } from 'react-icons/io5';
import '../../../../styles/scanner/addPatientButton.css';
import PatientScannerModal from '../../../scanner/PatientScannerModal';

const AddPatientButton = ({ onPatientAdded }) => {
  const [showScanner, setShowScanner] = useState(false);

  const handlePatientAdded = (patient) => {
    if (onPatientAdded) {
      onPatientAdded(patient);
    }
  };

  return (
    <>
      <button
        className="addButton"
        onClick={() => setShowScanner(true)}
      >
        <div className="buttonContent">
          <IoPersonAdd size={20} color="#fff" />
          <span className="buttonText">Add Patient</span>
        </div>
      </button>

      <PatientScannerModal
        visible={showScanner}
        onClose={() => setShowScanner(false)}
        onSuccess={handlePatientAdded}
        scannerTitle="Add New Patient"
      />
    </>
  );
};

export default AddPatientButton;
