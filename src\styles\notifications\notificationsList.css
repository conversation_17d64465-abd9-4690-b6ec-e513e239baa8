.container {
  flex: 1;
  background-color: #fff;
  border-radius: 0;
  margin: 0;
  width: 100%;
  overflow: visible;
}

.header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.headerTitle {
  font-size: 16px;
  font-weight: bold;
  color: var(--text-dark);
}

.clearAllButton {
  padding: 4px;
  cursor: pointer;
}

.clearAllText {
  font-size: 14px;
  color: var(--primary);
}

.list {
  max-height: 100%;
}

.notificationItem {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
  transition: opacity 300ms ease;
}

.notificationContent {
  flex: 1;
  cursor: pointer;
}

.notificationInner {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.iconContainer {
  width: 36px;
  height: 36px;
  border-radius: 18px;
  background-color: var(--primary);
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 12px;
}

.textContainer {
  flex: 1;
}

.notificationTitle {
  font-size: 14px;
  font-weight: bold;
  color: var(--text-dark);
  margin-bottom: 4px;
}

.notificationMessage {
  font-size: 13px;
  color: var(--text-medium);
  margin-bottom: 4px;
}

.notificationTime {
  font-size: 12px;
  color: var(--text-light);
}

.dismissButton {
  padding: 4px;
  display: flex;
  justify-content: center;
  cursor: pointer;
}
