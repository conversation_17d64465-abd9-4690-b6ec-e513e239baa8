import React, { useState } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { doc, setDoc } from 'firebase/firestore';
import { db } from '../../config/firebase';
import SharedProfileForm from './SharedProfileForm';
import { authAPI } from '../../config/api';
import '../../styles/profile/profileCompletion.css';

const ProfileCompletion = () => {
  const { user, updateUserProfile } = useAuth();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (formData) => {
    // Validate required fields
    if (!formData.firstName || !formData.lastName || !formData.phone || !formData.dateOfBirth || !formData.gender) {
      toast.error('Please fill in all required fields', {
        position: "top-right",
        autoClose: 5000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
      });
      return;
    }

    setLoading(true);
    try {
      // Show a progress message
      toast.info('Completing your profile', {
        position: "top-right",
        autoClose: 2000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
      });

      // Add profileCompleted flag to the data
      const profileData = {
        ...formData,
        profileCompleted: true,
        updatedAt: new Date().toISOString()
      };

      // First update Firestore (this is most important)
      const userRef = doc(db, 'users', user.uid);
      await setDoc(userRef, profileData, { merge: true });

      // Update local user state right after Firestore success
      await updateUserProfile(profileData);

      // Then try to update the backend API with a shorter local timeout
      try {
        await Promise.race([
          authAPI.updateProfileWithFallback(profileData),
          new Promise((_, reject) => setTimeout(() => reject(new Error('Local API timeout')), 3000))
        ]);
      } catch (apiError) {
        console.warn('API update timed out, but Firestore updated successfully:', apiError);
        // Don't throw error here, we already updated Firestore
      }

      toast.success('Your profile has been successfully completed', {
        position: "top-right",
        autoClose: 5000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
      });

      // Navigate to dashboard
      navigate('/dashboard');
    } catch (error) {
      toast.error('Failed to update profile. Please try again.', {
        position: "top-right",
        autoClose: 5000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
      });
      console.error('Profile update error:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container">
      <div className="profile-form-container">
        <SharedProfileForm
          initialData={{
            firstName: user?.displayName?.split(' ')[0] || '',
            lastName: user?.displayName?.split(' ').slice(1).join(' ') || '',
            email: user?.email || '',
          }}
          onSubmit={handleSubmit}
          isLoading={loading}
          isRequired={true}
          buttonText="Complete Profile"
        />
      </div>
    </div>
  );
};

export default ProfileCompletion;
