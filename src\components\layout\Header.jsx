import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import '../../styles/layout/header.css';

const Header = ({ primaryColor }) => {
  const { user, logout } = useAuth();
  const navigate = useNavigate();

  const handleLogout = async () => {
    try {
      await logout();
      navigate('/login');
    } catch (error) {
      console.error('Failed to log out', error);
    }
  };

  return (
    <header className="header" style={{ backgroundColor: primaryColor }}>
      <div className="header-container">
        <div className="logo">
          <Link to="/">NeuroCare</Link>
        </div>
        <nav className="nav-links">
          <Link to="/" className="nav-link">Dashboard</Link>
          
          {user?.role?.toLowerCase() === 'doctor' && (
            <>
              <Link to="/patients" className="nav-link">Patients</Link>
              <Link to="/doctor-appointments" className="nav-link">Appointments</Link>
            </>
          )}
          
          {user?.role?.toLowerCase() === 'patient' && (
            <>
              <Link to="/appointments" className="nav-link">Appointments</Link>
              <Link to="/medications" className="nav-link">Medications</Link>
              <Link to="/my-prescriptions" className="nav-link">Prescriptions</Link>
            </>
          )}
          
          {user?.role?.toLowerCase() === 'admin' && (
            <Link to="/user-management" className="nav-link">User Management</Link>
          )}
          
          {user?.role?.toLowerCase() === 'supervisor' && (
            <>
              <Link to="/supervisor-tracking" className="nav-link">Patient Tracking</Link>
              <Link to="/caregivers" className="nav-link">Caregivers</Link>
            </>
          )}
          
          {user?.role?.toLowerCase() === 'caregiver' && (
            <>
              <Link to="/caregiver-patients" className="nav-link">Patients</Link>
              <Link to="/caregiver-appointments" className="nav-link">Appointments</Link>
            </>
          )}
          
          <Link to="/messages" className="nav-link">Messages</Link>
        </nav>
        <div className="user-menu">
          <Link to="/notifications" className="icon-button">
            <i className="fas fa-bell"></i>
          </Link>
          <div className="dropdown">
            <button className="dropdown-button">
              {user?.firstName || 'User'} <i className="fas fa-caret-down"></i>
            </button>
            <div className="dropdown-content">
              <Link to="/profile">Profile</Link>
              <Link to="/settings">Settings</Link>
              <button onClick={handleLogout} className="logout-button">Logout</button>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
