.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

.search-container {
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 8px;
  margin: 16px;
  padding: 0 12px;
  height: 50px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.search-icon {
  margin-right: 8px;
  color: #757575;
  font-size: 20px;
}

.search-input {
  flex: 1;
  height: 40px;
  border: none;
  outline: none;
  color: #333;
  font-size: 16px;
}

.search-input::placeholder {
  color: #999;
}

.content-container {
  flex: 1;
  position: relative;
}

.loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  flex: 1;
  height: 100%;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #2196F3;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 10px;
  color: #666;
}

.patient-list {
  padding: 8px;
}

.patient-card {
  display: flex;
  background-color: #fff;
  border-radius: 8px;
  padding: 12px;
  margin: 0 8px 8px 8px;
  border: 1px solid #eee;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.patient-card:hover {
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.selected-patient-card {
  border-color: #2196F3;
  border-width: 2px;
  background-color: #f0f7ff;
}

.patient-avatar-container {
  width: 50px;
  height: 50px;
  border-radius: 25px;
  background-color: #2196F3;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 12px;
  position: relative;
}

.patient-avatar-text {
  color: #fff;
  font-size: 18px;
  font-weight: bold;
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 6px;
  position: absolute;
  bottom: 0;
  right: 0;
  border: 2px solid #fff;
}

.status-online {
  background-color: #4CAF50;
}

.status-offline {
  background-color: #9E9E9E;
}

.patient-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.patient-name {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.patient-email {
  font-size: 14px;
  color: #666;
  margin-bottom: 2px;
}

.patient-phone {
  font-size: 14px;
  color: #666;
}

.empty-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 20px;
  background-color: #fff;
  margin: 16px;
  border-radius: 8px;
  margin-top: 100px;
  text-align: center;
}

.empty-icon {
  font-size: 48px;
  color: #ccc;
}

.empty-text {
  font-size: 18px;
  font-weight: bold;
  color: #666;
  margin-top: 10px;
}

.empty-subtext {
  font-size: 14px;
  color: #999;
  margin-top: 5px;
}

.selected-patient-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.patient-detail-container {
  width: 90%;
  max-width: 500px;
  max-height: 80%;
  background-color: #fff;
  border-radius: 12px;
  padding: 20px;
  position: relative;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  overflow-y: auto;
}

.close-button {
  position: absolute;
  top: 10px;
  right: 10px;
  background: none;
  border: none;
  font-size: 24px;
  color: #666;
  cursor: pointer;
  padding: 5px;
  z-index: 1;
}

.close-button:hover {
  color: #333;
}

.patient-detail-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  margin-top: 10px;
}

.patient-detail-avatar {
  width: 60px;
  height: 60px;
  border-radius: 30px;
  background-color: #2196F3;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 15px;
}

.patient-detail-avatar-text {
  color: #fff;
  font-size: 24px;
  font-weight: bold;
}

.patient-detail-name {
  font-size: 20px;
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
}

.patient-detail-info {
  font-size: 14px;
  color: #666;
  margin-bottom: 3px;
}

.status-container {
  display: flex;
  align-items: center;
  margin-top: 5px;
}

.status-dot {
  width: 10px;
  height: 10px;
  border-radius: 5px;
  margin-right: 5px;
}

.online-dot {
  background-color: #4CAF50;
}

.offline-dot {
  background-color: #9E9E9E;
}

.status-text {
  font-size: 14px;
  color: #666;
}

.action-buttons-container {
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;
}

.action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 10px;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.action-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.video-call-button {
  background-color: #2196F3;
}

.chat-button {
  background-color: #4CAF50;
}

.button-icon {
  margin-right: 10px;
  font-size: 20px;
}

.action-button-text {
  color: #fff;
  font-weight: bold;
  font-size: 16px;
}

.info-container {
  background-color: #f5f5f5;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 10px;
}

.info-title {
  font-size: 14px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.info-value {
  font-size: 14px;
  color: #666;
}
