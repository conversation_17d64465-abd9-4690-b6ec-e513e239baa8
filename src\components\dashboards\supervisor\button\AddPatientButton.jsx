import React, { useState } from 'react';
import { Ionicons } from '@expo/vector-icons';
import PatientScannerModal from '../../scanner/PatientScannerModal';
import { ROLE_COLORS } from '../../../config/theme';
import '../../../../styles/dashboards/supervisor/button/addPatientButton.css';

const AddPatientButton = ({ onPatientAdded, headerStyle }) => {
  const [showScanner, setShowScanner] = useState(false);
  const supervisorColors = ROLE_COLORS.supervisor;

  const handlePatientAdded = (patient) => {
    if (onPatientAdded) {
      onPatientAdded(patient);
    }
  };

  return (
    <>
      <button
        className={`addButton ${headerStyle ? 'headerButton' : ''}`}
        style={{
          backgroundColor: headerStyle ? '#fff' : supervisorColors.primary
        }}
        onClick={() => setShowScanner(true)}
      >
        <div className="buttonContent">
          <Ionicons
            name="person-add"
            size={headerStyle ? 16 : 20}
            color={headerStyle ? supervisorColors.primary : "#fff"}
          />
          <span 
            className="buttonText"
            style={{ color: headerStyle ? supervisorColors.primary : '#fff' }}
          >
            Add Patient
          </span>
        </div>
      </button>

      <PatientScannerModal
        visible={showScanner}
        onClose={() => setShowScanner(false)}
        onSuccess={handlePatientAdded}
        scannerTitle="Add New Patient"
        relationshipType="supervisor-patient"
      />
    </>
  );
};

export default AddPatientButton;
