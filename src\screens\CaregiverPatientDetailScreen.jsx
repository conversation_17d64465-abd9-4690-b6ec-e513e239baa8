import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import '../styles/screens/caregiverPatientDetailScreen.css';
import { ROLE_COLORS } from '../config/theme';
import { useAuth } from '../contexts/AuthContext';
import { doc, getDoc } from 'firebase/firestore';
import { db } from '../config/firebase';
import PatientVitalsViewer from '../components/dashboards/doctor/PatientVitalsViewer';
import PatientSymptomsViewer from '../components/dashboards/doctor/PatientSymptomsViewer';
import PatientMedicationsViewer from '../components/dashboards/doctor/PatientMedicationsViewer';
import PatientActivitiesViewer from '../components/dashboards/caregiver/PatientActivitiesViewer';
import { firebaseCaregiverService } from '../services/firebaseCaregiverService';

// Icon components (you can replace these with your preferred icon library)
const Icon = ({ name, size = 20, color = '#888', className = '' }) => {
  const iconMap = {
    'add-circle': '➕',
    'calendar': '📅',
    'heart': '❤️',
    'medical': '🏥',
    'medkit': '💊',
  };
  
  return (
    <span 
      className={`icon ${className}`}
      style={{ 
        fontSize: `${size}px`, 
        color: color,
        display: 'inline-block',
        width: `${size}px`,
        height: `${size}px`,
        textAlign: 'center'
      }}
    >
      {iconMap[name] || '•'}
    </span>
  );
};

const CaregiverPatientDetailScreen = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { user } = useAuth();
  const patient = location.state?.patient;
  const caregiverColors = ROLE_COLORS.caregiver;

  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState('vitals');
  const [patientDetails, setPatientDetails] = useState(patient);

  useEffect(() => {
    if (patient) {
      loadPatientDetails();
    }
  }, [patient]);

  const loadPatientDetails = async () => {
    try {
      setLoading(true);

      // Get the full patient document to ensure we have all details
      const patientDocRef = doc(db, 'users', patient.uid);
      const patientDocSnap = await getDoc(patientDocRef);

      if (patientDocSnap.exists()) {
        const patientData = patientDocSnap.data();
        setPatientDetails({
          ...patient,
          ...patientData
        });
      }
    } catch (error) {
      console.error('Error loading patient details:', error);
      // You can replace this with your preferred notification system
      alert('Failed to load patient details');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadPatientDetails();
    setRefreshing(false);
  };

  const handleTabChange = (tab) => {
    setActiveTab(tab);
  };

  // Render health data content based on active tab
  const renderHealthContent = () => {
    if (!patient) return null;
    
    switch (activeTab) {
      case 'vitals':
        return (
          <PatientVitalsViewer
            patientId={patient.uid}
            patientName={`${patient.firstName} ${patient.lastName}`}
          />
        );
      case 'symptoms':
        return (
          <PatientSymptomsViewer
            patientId={patient.uid}
            patientName={`${patient.firstName} ${patient.lastName}`}
          />
        );
      case 'medications':
        return (
          <PatientMedicationsViewer
            patientId={patient.uid}
            patientName={`${patient.firstName} ${patient.lastName}`}
          />
        );
      case 'activities':
        return (
          <PatientActivitiesViewer
            patientId={patient.uid}
            patientName={`${patient.firstName} ${patient.lastName}`}
            onAddActivity={() => navigate('/caregiver-record-activity', { state: { patient } })}
          />
        );
      default:
        return null;
    }
  };

  // Render the tab button
  const renderTabButton = (tabName, label, icon) => (
    <button
      key={tabName}
      className={`tab-button ${activeTab === tabName ? 'active-tab-button' : ''}`}
      style={activeTab === tabName ? { borderColor: caregiverColors.primary } : {}}
      onClick={() => handleTabChange(tabName)}
    >
      <Icon
        name={icon}
        size={20}
        color={activeTab === tabName ? caregiverColors.primary : '#888'}
      />
      <span
        className={`tab-button-text ${activeTab === tabName ? 'active-tab-button-text' : ''}`}
        style={activeTab === tabName ? { color: caregiverColors.primary } : {}}
      >
        {label}
      </span>
    </button>
  );

  if (!patient) {
    return (
      <div className="container">
        <div className="loading-container">
          <div className="loading-text">No patient data available</div>
        </div>
      </div>
    );
  }

  return (
    <div className="container">
      {loading ? (
        <div className="loading-container">
          <div 
            className="spinner" 
            style={{ borderTopColor: caregiverColors.primary }}
          ></div>
          <div className="loading-text">Loading patient details...</div>
        </div>
      ) : (
        <div className="scroll-view">
          {/* Patient Header */}
          <div className="patient-header">
            <div 
              className="avatar"
              style={{ backgroundColor: caregiverColors.primary }}
            >
              {`${patient.firstName?.charAt(0) || ''}${patient.lastName?.charAt(0) || ''}`}
            </div>
            <div className="patient-info">
              <div className="patient-name">{`${patient.firstName} ${patient.lastName}`}</div>
              <div className="patient-detail">{patient.email}</div>
              <div className="patient-detail">
                {patient.gender}, {patient.age || 'Age not specified'}
              </div>
              <div 
                className="patient-condition"
                style={{ color: caregiverColors.primary }}
              >
                {patient.condition || 'No condition specified'}
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="action-buttons-container">
            <button
              className="action-button"
              style={{ backgroundColor: caregiverColors.primary }}
              onClick={() => navigate('/caregiver-record-vitals', { state: { patient } })}
            >
              <Icon name="add-circle" size={20} color="#fff" />
              <span className="action-button-text">Record Vitals</span>
            </button>

            <button
              className="action-button"
              style={{ backgroundColor: caregiverColors.primary }}
              onClick={() => navigate('/caregiver-record-activity', { state: { patient } })}
            >
              <Icon name="calendar" size={20} color="#fff" />
              <span className="action-button-text">Record Activity</span>
            </button>
          </div>

          {/* Tab Navigation */}
          <div className="tab-container">
            {renderTabButton('vitals', 'Vitals', 'heart')}
            {renderTabButton('symptoms', 'Symptoms', 'medical')}
            {renderTabButton('medications', 'Medications', 'medkit')}
            {renderTabButton('activities', 'Activities', 'calendar')}
          </div>

          {/* Health Content */}
          <div className="health-content-container">
            <div className="health-content-header">
              <div className="health-content-title">
                {`${patient.firstName}'s ${
                  activeTab === 'vitals'
                    ? 'Vitals'
                    : activeTab === 'symptoms'
                    ? 'Symptoms'
                    : activeTab === 'medications'
                    ? 'Medications'
                    : 'Activities'
                }`}
              </div>
              <div className="health-content-subtitle">
                Monitor your patient's health metrics
              </div>
            </div>
            {renderHealthContent()}
          </div>
        </div>
      )}
    </div>
  );
};

export default CaregiverPatientDetailScreen;
