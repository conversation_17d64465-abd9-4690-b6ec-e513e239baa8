import React from "react";
import { useNavigate } from "react-router-dom";
import { Bell, Activity, Mic, MapPin, Video, Camera, Shield, BarChart2 } from "lucide-react";
import { useTheme } from "../../contexts/ThemeContext";
import '../../styles/homePage/homePage.css';

// Features data
const features = [
  {
    id: 1,
    title: "Reminders",
    description: "Medication and appointment reminders",
    icon: Bell,
    color: "#FF8A65",
  },
  {
    id: 2,
    title: "Symptom Tracking",
    description: "Record your daily symptoms",
    icon: Activity,
    color: "#4DB6AC",
  },
  {
    id: 3,
    title: "Voice Commands",
    description: "Interact with the app by voice",
    icon: Mic,
    color: "#7986CB",
  },
  {
    id: 4,
    title: "GPS Guidance",
    description: "Navigation assistance and alerts",
    icon: MapPin,
    color: "#FFB74D",
  },
  {
    id: 5,
    title: "Communication",
    description: "Voice and video chat with your doctor",
    icon: Video,
    color: "#AED581",
  },
  {
    id: 6,
    title: "Recognition",
    description: "Identifies objects and people",
    icon: Camera,
    color: "#BA68C8",
  },
  {
    id: 7,
    title: "Safety Alerts",
    description: "Fall detection and unusual behavior alerts",
    icon: Shield,
    color: "#F06292",
  },
  {
    id: 8,
    title: "Dashboard",
    description: "Track daily routines",
    icon: BarChart2,
    color: "#4FC3F7",
  },
];

function HomePage() {
  // Initialize navigate hook
  const navigate = useNavigate();

  // Use theme context
  const { isDarkMode } = useTheme();


  return (
    <div className={`landing-page ${isDarkMode ? "" : "light-mode"}`}>

      <main>
        {/* hero section */}
        <section className="hero">
          <div className="video-background">
            <video
              src="/assets/Videos/Greeting/NeuroCare.mp4"
              autoPlay
              loop
              muted
              playsInline
            >
              Your browser does not support the video tag.
            </video>
            <div className="video-overlay"></div>
          </div>
          <div className="hero-content">
            <h2 className="homePage-h2-hero">Supporting daily autonomy</h2>
            <p className="homePage-p-hero">
              An intelligent companion designed to improve the quality of life for people with Alzheimer's,
              Parkinson's, and other neurological conditions
            </p>
            <div className="cta-buttons">
              <button
                onClick={() => navigate('/signup')}
                className="cta-button primary"
              >
                Get Started
              </button>
              <button
                onClick={() => navigate('/features')}
                className="cta-button secondary"
              >
                Learn More
              </button>
            </div>
          </div>
          <div className="hero-image">
            <div className="welcome-card">
              <div className="welcome-image">
                <span>🧠</span>
              </div>
              <h3 className="welcome-title homePage-h3-welcome">Welcome to Neuro Care</h3>
              <p className="welcome-text homePage-p-welcome">Helping you manage your daily life with simplicity and security</p>
            </div>
          </div>
        </section>

        {/* features section */}
        <section className="features-section">
          <h2 className="homePage-h2-features">Our Features</h2>
          <div className="features-grid">
            {features.map((feature) => (
              <div key={feature.id} className="feature-card">
                <div className="feature-icon" style={{ backgroundColor: feature.color }}>
                  <feature.icon size={24} color="#FFFFFF" />
                </div>
                <h3 className={`homePage-h3-feature-${feature.id}`}>{feature.title}</h3>
                <p className={`homePage-p-feature-${feature.id}`}>{feature.description}</p>
              </div>
            ))}
          </div>
          <button
            onClick={() => navigate('/features')}
            className="see-more-button"
          >
            See other features
          </button>
        </section>

        {/*testimonials section */}
        <section className="testimonials">
          <h2 className="homePage-h2-testimonials">Testimonials</h2>
          <div className="testimonial-cards">
            <div className="testimonial-card">
              <p className="testimonial-content homePage-p-testimonial-1-content">
                "This application has transformed our daily lives. The medication reminders are invaluable, and the
                object recognition feature helps my father tremendously."
              </p>
              <p className="testimonial-author homePage-p-testimonial-1-author">Marie L., caregiver</p>
            </div>
            <div className="testimonial-card">
              <p className="testimonial-content homePage-p-testimonial-2-content">
                "The symptom tracking allows me to better communicate with my mother's neurologist. The safety alerts
                have saved us several times."
              </p>
              <p className="testimonial-author homePage-p-testimonial-2-author">Thomas D., caregiver</p>
            </div>
            <div className="testimonial-card">
              <p className="testimonial-content homePage-p-testimonial-3-content">
                "The voice commands feature has given me back my independence. I can navigate the app easily even on
                days when my symptoms are worse."
              </p>
              <p className="testimonial-author homePage-p-testimonial-3-author">Robert M., patient</p>
            </div>
          </div>
        </section>

        </main>
    </div>
  );
}

export default HomePage;