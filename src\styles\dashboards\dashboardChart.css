/* Dashboard Chart CSS */
.dashboard-chart {
  border-radius: 12px;
  overflow: hidden;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chart-header {
  margin-bottom: 16px;
}

.chart-title {
  font-size: 18px;
  font-weight: 600;
  color: #212121;
  margin: 0 0 4px 0;
}

.chart-subtitle {
  font-size: 14px;
  color: #757575;
  margin: 0;
}

.chart-container {
  position: relative;
  width: 100%;
}

.chart-error {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #f44336;
  font-size: 16px;
  text-align: center;
  background-color: rgba(244, 67, 54, 0.05);
  border-radius: 8px;
  padding: 20px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .dashboard-chart {
    padding: 12px;
  }
  
  .chart-title {
    font-size: 16px;
  }
  
  .chart-subtitle {
    font-size: 12px;
  }
}
