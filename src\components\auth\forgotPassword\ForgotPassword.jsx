import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { sendPasswordResetEmail } from 'firebase/auth';
import { auth } from '../../config/firebase';
import { IoMail } from 'react-icons/io5';
import '../../../styles/auth/forgotPassword/forgotPassword.css';
import backgroundImage from '../../../../assets/Backgrounds/pexels-pixabay-40568.jpg';

export default function ForgotPassword() {
  const navigate = useNavigate();
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);

  const handleResetPassword = async () => {
    if (!email.trim()) {
      // Using browser alert instead of react-native-flash-message
      alert('Please enter your email address');
      return;
    }

    try {
      setLoading(true);
      await sendPasswordResetEmail(auth, email);
      
      // Show success message using browser alert
      alert('Password reset email sent. Check your email inbox for instructions to reset your password.');
      navigate('/login');
    } catch (error) {
      let errorMessage = 'An error occurred while sending the reset email';
      
      switch (error.code) {
        case 'auth/invalid-email':
          errorMessage = 'Invalid email address';
          break;
        case 'auth/user-not-found':
          errorMessage = 'No account found with this email';
          break;
        case 'auth/too-many-requests':
          errorMessage = 'Too many attempts. Please try again later';
          break;
        default:
          errorMessage = error.message;
      }
      
      alert(`Error: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div 
      className="background" 
      style={{ backgroundImage: `url(${backgroundImage})` }}
    >
      <div className="container">
        <h1 className="title">Password Reset</h1>
        <p className="subtitle">Enter your email to receive a password reset link</p>
        
        <div className="input-container">
          <IoMail size={24} color="#999" className="icon" />
          <input
            className="input"
            type="email"
            placeholder="Email Address"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
          />
        </div>
        
        <button
          className={`button ${loading ? 'button-disabled' : ''}`}
          onClick={handleResetPassword}
          disabled={loading}
        >
          {loading ? (
            <div className="spinner"></div>
          ) : (
            <span className="button-text">Reset Password</span>
          )}
        </button>
        
        <a
          className="back-link"
          onClick={() => navigate('/login')}
        >
          <span className="back-link-text">Back to Login</span>
        </a>
      </div>
    </div>
  );
}
