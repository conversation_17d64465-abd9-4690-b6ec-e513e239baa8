import React, { useState, useMemo, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { IoMdPeople } from 'react-icons/io';
import { usersAPI } from '../../src/config/api';
import { useAuth } from '../../src/contexts/AuthContext';
import { getThemeForRole } from '../../src/config/theme';
import '../styles/screens/addPatientScreen.css';

const AddPatientScreen = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [userCode, setUserCode] = useState('');
  const [codeError, setCodeError] = useState('');

  // Get theme colors based on user role
  const theme = useMemo(() => {
    return getThemeForRole(user?.role || 'default');
  }, [user]);

  const primaryColor = theme.colors.primary;

  // Auto-capitalize the input
  useEffect(() => {
    if (userCode) {
      setUserCode(userCode.toUpperCase());
    }
  }, [userCode]);

  // Validate code format whenever it changes
  useEffect(() => {
    if (userCode && userCode.length > 0) {
      if (!/^[A-Z0-9]*$/.test(userCode)) {
        setCodeError('Code can only contain letters and numbers');
      } else if (userCode.length < 8 && userCode.length > 0) {
        setCodeError('Code must be 8 characters');
      } else {
        setCodeError('');
      }
    } else {
      setCodeError('');
    }
  }, [userCode]);

  // Determine relationship type based on user role
  const relationshipType = (() => {
    if (user?.role === 'doctor') return 'doctor-patient';
    if (user?.role === 'nurse') return 'nurse-patient';
    if (user?.role === 'pharmacist') return 'pharmacist-patient';
    return '';
  })();

  const handleSubmit = async () => {
    // Validate code format
    if (!userCode || userCode.length !== 8) {
      showMessage('Invalid Format', 'Please enter a valid 8-character code', 'error');
      return;
    }

    if (!/^[A-Z0-9]{8}$/.test(userCode)) {
      showMessage('Invalid Format', 'Code must be 8 characters (letters and numbers only)', 'error');
      return;
    }

    setLoading(true);
    try {
      console.log('Submitting code:', userCode);
      
      // First verify the user exists
      const userResponse = await usersAPI.getUserByCode(userCode);
      console.log('User found:', userResponse);

      if (!userResponse || userResponse?.role !== 'patient') {
        showMessage('Invalid User', 'This code does not belong to a patient', 'error');
        setLoading(false);
        return;
      }

      // Link the user
      console.log('Linking user with type:', relationshipType);
      const linkResponse = await usersAPI.linkUser(userCode, relationshipType);
      console.log('Link response:', linkResponse);

      showMessage('Success', `Patient ${userResponse.displayName} added successfully`, 'success');

      // Go back to the previous screen
      setTimeout(() => {
        navigate(-1);
      }, 1500);
    } catch (error) {
      console.error('Raw error:', error);
      let errorMessage = 'Failed to add patient. Please try again.';

      if (error?.responseData?.error === 'User not found with this code') {
        errorMessage = 'Invalid code. No patient found with this code.';
      } else if (error?.message?.includes('Network Error')) {
        errorMessage = 'Network error. Please check your connection.';
      }

      showMessage('Error', errorMessage, 'error');
      setLoading(false);
    }
  };

  // Simple function to show messages (replace with your preferred notification system)
  const showMessage = (title, message, type) => {
    // This is a placeholder - you would implement this with your preferred notification library
    // For example, you could use react-toastify, material-ui snackbars, etc.
    console.log(`${type.toUpperCase()}: ${title} - ${message}`);
    
    // Example implementation with browser alert (not recommended for production)
    alert(`${title}: ${message}`);
  };

  if (loading) {
    return (
      <div className="loadingContainer">
        <div className="spinner" style={{ borderTopColor: primaryColor }}></div>
        <p className="loadingText">Adding patient...</p>
      </div>
    );
  }

  return (
    <div className="container">
      <div className="formContainer">
        <IoMdPeople size={60} color={primaryColor} className="icon" />

        <h1 className="title">Add Patient</h1>
        <p className="subtitle">
          Enter the 8-character code from the patient's profile
        </p>

        <input
          className={`input ${codeError ? 'inputError' : ''}`}
          value={userCode}
          onChange={(e) => setUserCode(e.target.value)}
          placeholder="Enter code (e.g. ABC12345)"
          maxLength={8}
          autoCapitalize="characters"
        />
        
        {codeError ? (
          <p className="errorText">{codeError}</p>
        ) : (
          <p className="helperText">
            {userCode.length}/8 characters
          </p>
        )}

        <button
          className={`button ${(!userCode || userCode.length !== 8 || codeError) ? 'buttonDisabled' : ''}`}
          onClick={handleSubmit}
          disabled={!userCode || userCode.length !== 8 || !!codeError || loading}
          style={{ backgroundColor: primaryColor }}
        >
          <span className="buttonText">Connect Patient</span>
        </button>
      </div>
    </div>
  );
};

export default AddPatientScreen;
