/* Styles for NotificationHandler component */
.notification-toast {
  position: fixed;
  top: 20px;
  right: 20px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 16px;
  max-width: 350px;
  z-index: 1000;
  animation: slideIn 0.3s ease-out;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.notification-title {
  font-weight: bold;
  font-size: 16px;
  color: #333;
}

.notification-close {
  background: none;
  border: none;
  cursor: pointer;
  color: #999;
  font-size: 18px;
}

.notification-message {
  font-size: 14px;
  color: #666;
  margin-bottom: 12px;
}

.notification-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.notification-btn {
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  border: none;
}

.notification-btn-primary {
  background-color: #4285f4;
  color: white;
}

.notification-btn-secondary {
  background-color: #f1f3f4;
  color: #5f6368;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOut {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

.notification-exit {
  animation: slideOut 0.3s ease-in forwards;
}
