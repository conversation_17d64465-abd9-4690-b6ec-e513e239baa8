.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.scroll-view {
  flex: 1;
  overflow-y: auto;
}

.loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  flex: 1;
  min-height: 50vh;
}

.loading-text {
  margin-top: 10px;
  font-size: 16px;
  color: #666;
}

.patient-header {
  display: flex;
  flex-direction: row;
  padding: 20px;
  background-color: #fff;
  border-radius: 10px;
  margin: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.avatar {
  margin-right: 15px;
  width: 60px;
  height: 60px;
  border-radius: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-weight: bold;
  font-size: 20px;
}

.patient-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.patient-name {
  font-size: 20px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.patient-detail {
  font-size: 14px;
  color: #666;
  margin-bottom: 2px;
}

.patient-condition {
  font-size: 14px;
  font-weight: 500;
  margin-top: 5px;
}

.action-buttons-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin: 15px;
  margin-top: 0;
  gap: 10px;
}

.action-button {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 12px;
  border-radius: 8px;
  flex: 1;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: none;
  cursor: pointer;
  color: #fff;
  font-size: 14px;
  font-weight: 500;
  transition: opacity 0.2s;
}

.action-button:hover {
  opacity: 0.9;
}

.action-button-text {
  color: #fff;
  font-size: 14px;
  font-weight: 500;
  margin-left: 8px;
}

.tab-container {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  background-color: #fff;
  border-radius: 10px;
  margin: 15px;
  margin-top: 0;
  padding: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tab-button {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 8px 16px;
  border-radius: 20px;
  border: 1px solid #e0e0e0;
  background-color: #f5f5f5;
  cursor: pointer;
  transition: all 0.2s;
}

.tab-button:hover {
  background-color: #e8e8e8;
}

.active-tab-button {
  background-color: #f0f8ff;
}

.tab-button-text {
  margin-left: 5px;
  font-size: 14px;
  color: #888;
}

.active-tab-button-text {
  font-weight: 500;
}

.health-content-container {
  background-color: #fff;
  border-radius: 10px;
  margin: 15px;
  margin-top: 0;
  padding: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.health-content-header {
  margin-bottom: 15px;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 10px;
}

.health-content-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.health-content-subtitle {
  font-size: 14px;
  color: #888;
  margin-top: 5px;
}

.icon {
  width: 20px;
  height: 20px;
  margin-right: 5px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive design */
@media (max-width: 768px) {
  .patient-header {
    margin: 10px;
    padding: 15px;
  }
  
  .action-buttons-container {
    flex-direction: column;
    gap: 10px;
  }
  
  .tab-container {
    flex-wrap: wrap;
    gap: 5px;
  }
  
  .tab-button {
    flex: 1;
    min-width: calc(50% - 5px);
  }
}
