:root {
  --doctor-primary-color: rgba(170, 86, 255, 1);
}

.container {
  margin-bottom: 16px;
}

.selectorButton {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  background-color: #fff;
  border-radius: 8px;
  padding: 14px;
  border: 1px solid #eaeaea;
}

.placeholderContainer {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.placeholderText {
  margin-left: 8px;
  font-size: 16px;
  color: #888;
}

.selectedPatientContainer {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.miniAvatarContainer {
  width: 32px;
  height: 32px;
  border-radius: 16px;
  background-color: #f0f7ff;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 10px;
}

.miniAvatarText {
  font-size: 14px;
  font-weight: 600;
  color: var(--doctor-primary-color);
}

.selectedPatientName {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.4);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modalContainer {
  width: 90%;
  max-width: 600px;
  height: 70%;
  max-height: 600px;
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modalHeader {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #eaeaea;
  background-color: #fff;
}

.modalTitle {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.closeButton {
  padding: 6px;
  cursor: pointer;
}

.searchContainer {
  display: flex;
  flex-direction: row;
  align-items: center;
  background-color: #f8f9fa;
  margin: 16px;
  border-radius: 8px;
  padding: 0 12px;
  border: 1px solid #eaeaea;
}

.searchIcon {
  margin-right: 8px;
  color: #888;
}

.searchInput {
  flex: 1;
  height: 44px;
  font-size: 16px;
  color: #333;
  border: none;
  background: transparent;
  outline: none;
}

.patientList {
  padding: 0 16px;
  overflow-y: auto;
  flex: 1;
}

.patientItem {
  display: flex;
  flex-direction: row;
  align-items: center;
  background-color: #fff;
  border-radius: 8px;
  padding: 14px;
  margin-bottom: 10px;
  border: 1px solid #eaeaea;
  cursor: pointer;
}

.avatarContainer {
  width: 50px;
  height: 50px;
  border-radius: 25px;
  background-color: #f0f7ff;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 14px;
}

.avatarText {
  font-size: 18px;
  font-weight: 600;
  color: var(--doctor-primary-color);
}

.patientInfo {
  flex: 1;
}

.patientName {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.patientDetails {
  font-size: 14px;
  color: #666;
  margin-top: 3px;
}

.lastVisit {
  font-size: 12px;
  color: #888;
  margin-top: 3px;
}

.loadingContainer {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.loadingText {
  margin-top: 10px;
  font-size: 14px;
  color: #666;
}

.spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border-left-color: var(--doctor-primary-color);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.emptyContainer {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 30px;
}

.emptyText {
  margin-top: 10px;
  font-size: 14px;
  color: #666;
  text-align: center;
  line-height: 20px;
}
