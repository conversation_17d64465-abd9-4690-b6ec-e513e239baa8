.container {
  flex: 1;
  background-color: var(--color-background);
}

.content {
  padding: 16px;
  background-color: transparent;
}

.card {
  margin-bottom: 16px;
  border-radius: 12px;
  overflow: hidden;
}

.lastCard {
  margin-bottom: 32px;
}

.cardContent {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.sectionTitle {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 8px;
}

.divider {
  height: 1px;
  margin-bottom: 16px;
  background-color: #e0e0e0;
}

.qrContainer {
  padding: 16px;
  background-color: white;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 16px 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.placeholderQR {
  width: 60%;
  height: 60%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  border-radius: 8px;
}

.codeContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 16px;
}

.codeLabel {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 8px;
  color: var(--color-text-dark);
}

.codeText {
  font-size: 20px;
  font-weight: bold;
  letter-spacing: 1px;
  margin-bottom: 8px;
}

.codeDescription {
  font-size: 14px;
  color: var(--color-text-medium);
  text-align: center;
}

.listItem {
  display: flex;
  align-items: center;
  padding: 12px 0;
}

.listItemContent {
  flex: 1;
  margin-left: 12px;
}

.listItemTitle {
  font-size: 16px;
  color: var(--color-text-dark);
}

.listItemDescription {
  font-size: 14px;
  color: var(--color-text-medium);
}

.listItemRight {
  display: flex;
  align-items: center;
}
