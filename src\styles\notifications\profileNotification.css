.container {
  background-color: #fff;
  margin: 8px 0;
  padding: 16px;
  border-bottom: 1px solid #eee;
}

.contentContainer {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 12px;
}

.iconContainer {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  background-color: var(--primary-color);
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 12px;
}

.textContainer {
  flex: 1;
}

.title {
  font-size: 16px;
  font-weight: bold;
  color: var(--text-dark);
  margin-bottom: 4px;
}

.message {
  font-size: 14px;
  color: var(--text-medium);
}

.buttonContainer {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
}

.completeButton {
  background-color: var(--primary-color);
  padding: 8px 16px;
  border-radius: 6px;
  margin-right: 10px;
  cursor: pointer;
  border: none;
}

.buttonText {
  color: #fff;
  font-weight: bold;
  font-size: 14px;
}

.dismissButton {
  padding: 8px 12px;
  background: none;
  border: none;
  cursor: pointer;
}

.dismissText {
  color: var(--text-medium);
  font-size: 14px;
}
