import React, { useState, useEffect } from 'react';
import '../styles/screens/profileScreen.css';
import { useAuth } from '../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import SharedProfileForm from '../components/profile/SharedProfileForm';

const ProfileScreen = () => {
  const { user, updateUserProfile } = useAuth();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [initialData, setInitialData] = useState({});

  useEffect(() => {
    if (user) {
      // Extract relevant user data to populate the form
      const { firstName, lastName, email, phone, address, city, dateOfBirth, gender, countryCode, dialCode } = user;
      setInitialData({
        firstName: firstName || '',
        lastName: lastName || '',
        email: email || '',
        phone: phone || '',
        address: address || '',
        city: city || '',
        dateOfBirth: dateOfBirth || '',
        gender: gender || '',
        countryCode: countryCode || 'US',
        dialCode: dialCode || '+1',
      });
    }
  }, [user]);

  const handleSubmit = async (formData) => {
    try {
      setLoading(true);
      
      // Update the user profile
      await updateUserProfile({
        ...formData,
        profileCompleted: true,
        updatedAt: new Date().toISOString(),
      });

      // Show success message
      alert('Your profile has been updated successfully');

      // Navigate back to dashboard
      setTimeout(() => {
        navigate('/dashboard');
      }, 1000);
    } catch (error) {
      console.error('Error updating profile:', error);
      alert(error.message || 'Failed to update profile. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  if (!user) {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
      </div>
    );
  }

  return (
    <div className="profile-screen-container">
      <SharedProfileForm
        initialData={initialData}
        onSubmit={handleSubmit}
        loading={loading}
        buttonText="Update Profile"
        requiredFields={['firstName', 'lastName', 'email']}
        nonEditableFields={['firstName', 'lastName', 'email']}
      />
    </div>
  );
};

export default ProfileScreen;
