import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { Card, Divider, List, Switch } from '@mui/material';
import { 
  Notifications, 
  DarkMode, 
  Language, 
  Lock, 
  Shield, 
  Description, 
  Info, 
  HelpOutline,
  ChevronRight
} from '@mui/icons-material';
import { getThemeForRole, COLORS } from '../../config/theme';
import QRCode from 'qrcode.react';
import { useNavigate } from 'react-router-dom';
import '../../styles/settings/settings.css';

const Settings = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const theme = getThemeForRole(user?.role || 'default');
  const primaryColor = theme.colors.primary;
  const [qrSize, setQrSize] = useState(300);

  // Settings state
  const [notifications, setNotifications] = useState(true);
  const [darkMode, setDarkMode] = useState(false);
  const [language, setLanguage] = useState('English');
  const [qrError, setQrError] = useState(false);

  // Toggle functions
  const toggleNotifications = () => setNotifications(!notifications);
  const toggleDarkMode = () => setDarkMode(!darkMode);

  // Set QR size based on window width
  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      setQrSize(width * 0.3 > 300 ? 300 : width * 0.3);
    };
    
    handleResize();
    window.addEventListener('resize', handleResize);
    
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  return (
    <div className="container">
      <div className="content">
        {/* QR Code Section */}
        <Card className="card">
          <div className="cardContent">
            <h2 className="sectionTitle" style={{ color: primaryColor }}>My QR Code</h2>
            <div className="divider"></div>

            <div className="qrContainer">
              {user?.userCode ? (
                <QRCode
                  value={user.userCode}
                  size={qrSize}
                  fgColor={primaryColor}
                  bgColor="white"
                  level="M"
                  onError={(e) => {
                    console.error('QR Code Error:', e);
                    setQrError(true);
                  }}
                />
              ) : (
                <div className="placeholderQR">
                  <svg width={qrSize * 0.6} height={qrSize * 0.6} viewBox="0 0 24 24" fill="#ccc">
                    <path d="M3 3h6v6H3zm1 1v4h4V4zm2 2v2h2V6zM11 3h2v2h-2zm4 0h6v6h-6zm1 1v4h4V4zm2 2v2h2V6zM3 11h2v2H3zm4 0h2v2H7zm4 0h2v2h-2zm4 0h2v2h-2zm4 0h2v2h-2zM3 15h6v6H3zm1 1v4h4v-4zm2 2v2h2v-2zm6-2h2v2h-2zm4-2h6v6h-6zm1 1v4h4v-4zm2 2v2h2v-2z"/>
                  </svg>
                </div>
              )}
            </div>

            <div className="codeContainer">
              <div className="codeLabel">Your Unique Code</div>
              <div className="codeText" style={{ color: primaryColor }}>{user?.userCode || 'CODE NOT FOUND'}</div>
              <div className="codeDescription">
                Share this code with healthcare providers to connect with them
              </div>
            </div>
          </div>
        </Card>

        {/* General Settings */}
        <Card className="card">
          <div style={{ padding: '16px' }}>
            <h2 className="sectionTitle" style={{ color: primaryColor }}>General Settings</h2>
            <div className="divider"></div>

            <div className="listItem">
              <Notifications style={{ color: primaryColor }} />
              <div className="listItemContent">
                <div className="listItemTitle">Notifications</div>
                <div className="listItemDescription">Enable push notifications</div>
              </div>
              <div className="listItemRight">
                <Switch 
                  checked={notifications} 
                  onChange={toggleNotifications} 
                  color="primary" 
                />
              </div>
            </div>

            <div className="listItem">
              <DarkMode style={{ color: primaryColor }} />
              <div className="listItemContent">
                <div className="listItemTitle">Dark Mode</div>
                <div className="listItemDescription">Enable dark theme</div>
              </div>
              <div className="listItemRight">
                <Switch 
                  checked={darkMode} 
                  onChange={toggleDarkMode} 
                  color="primary" 
                />
              </div>
            </div>

            <div className="listItem" onClick={() => {/* Open language selection */}} style={{ cursor: 'pointer' }}>
              <Language style={{ color: primaryColor }} />
              <div className="listItemContent">
                <div className="listItemTitle">Language</div>
                <div className="listItemDescription">{language}</div>
              </div>
              <div className="listItemRight">
                <ChevronRight style={{ color: COLORS.textMedium }} />
              </div>
            </div>
          </div>
        </Card>

        {/* Privacy & Security */}
        <Card className="card">
          <div style={{ padding: '16px' }}>
            <h2 className="sectionTitle" style={{ color: primaryColor }}>Privacy & Security</h2>
            <div className="divider"></div>

            <div className="listItem" onClick={() => navigate('/change-password')} style={{ cursor: 'pointer' }}>
              <Lock style={{ color: primaryColor }} />
              <div className="listItemContent">
                <div className="listItemTitle">Change Password</div>
              </div>
              <div className="listItemRight">
                <ChevronRight style={{ color: COLORS.textMedium }} />
              </div>
            </div>

            <div className="listItem" onClick={() => {/* Open privacy policy */}} style={{ cursor: 'pointer' }}>
              <Shield style={{ color: primaryColor }} />
              <div className="listItemContent">
                <div className="listItemTitle">Privacy Policy</div>
              </div>
              <div className="listItemRight">
                <ChevronRight style={{ color: COLORS.textMedium }} />
              </div>
            </div>

            <div className="listItem" onClick={() => {/* Open terms of service */}} style={{ cursor: 'pointer' }}>
              <Description style={{ color: primaryColor }} />
              <div className="listItemContent">
                <div className="listItemTitle">Terms of Service</div>
              </div>
              <div className="listItemRight">
                <ChevronRight style={{ color: COLORS.textMedium }} />
              </div>
            </div>
          </div>
        </Card>

        {/* About */}
        <Card className="card lastCard">
          <div style={{ padding: '16px' }}>
            <h2 className="sectionTitle" style={{ color: primaryColor }}>About</h2>
            <div className="divider"></div>

            <div className="listItem">
              <Info style={{ color: primaryColor }} />
              <div className="listItemContent">
                <div className="listItemTitle">App Version</div>
                <div className="listItemDescription">1.0.0</div>
              </div>
            </div>

            <div className="listItem" onClick={() => {/* Open support contact */}} style={{ cursor: 'pointer' }}>
              <HelpOutline style={{ color: primaryColor }} />
              <div className="listItemContent">
                <div className="listItemTitle">Contact Support</div>
              </div>
              <div className="listItemRight">
                <ChevronRight style={{ color: COLORS.textMedium }} />
              </div>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default Settings;
