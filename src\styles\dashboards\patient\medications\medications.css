/* Medications Component Styles */

/* Container and Layout */
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.headerTitle {
  font-size: 20px;
  font-weight: bold;
  color: #212121;
}

.backButton, .addButton {
  padding: 8px;
  cursor: pointer;
}

.content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.section {
  margin-bottom: 24px;
}

.sectionHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.sectionTitle {
  font-size: 18px;
  font-weight: bold;
  color: #212121;
  margin-bottom: 12px;
}

/* Medication Items */
.medicationItem {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.2s;
}

.medicationItem:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
}

.medicationHeader {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.medicationInfo {
  flex: 1;
  margin-left: 12px;
}

.medicationName {
  font-size: 16px;
  font-weight: bold;
  color: #212121;
}

.medicationDosage {
  font-size: 14px;
  color: #616161;
}

.medicationDetails {
  margin-top: 8px;
}

.medicationFrequency, .medicationInstructions {
  font-size: 14px;
  color: #616161;
  margin-bottom: 4px;
}

.detailLabel {
  font-weight: bold;
  color: #424242;
}

/* Empty State */
.emptyContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px;
  background-color: #f9f9f9;
  border-radius: 8px;
}

.emptyText {
  font-size: 16px;
  color: #757575;
  margin-top: 8px;
  margin-bottom: 16px;
}

.emptyButton {
  background-color: #4285F4;
  padding: 8px 16px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
}

.emptyButtonText {
  color: #fff;
  font-weight: bold;
}

/* Loading State */
.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px;
  background-color: #f9f9f9;
  border-radius: 8px;
}

.loadingText {
  font-size: 16px;
  color: #757575;
  margin-top: 12px;
}

/* Error and Info States */
.errorContainer {
  display: flex;
  align-items: center;
  padding: 16px;
  background-color: #FFEBEE;
  border-radius: 8px;
  margin-top: 16px;
}

.errorText {
  font-size: 14px;
  color: #D32F2F;
  margin-left: 8px;
  flex: 1;
}

.infoContainer {
  display: flex;
  align-items: center;
  padding: 16px;
  background-color: #E3F2FD;
  border-radius: 8px;
  margin-top: 16px;
}

.infoText {
  font-size: 14px;
  color: #0D47A1;
  margin-left: 8px;
  flex: 1;
}

/* Clear Button */
.clearButton {
  display: flex;
  align-items: center;
  background-color: #FFEBEE;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
}

.clearButtonText {
  color: #F44336;
  font-size: 12px;
  font-weight: 500;
  margin-left: 4px;
}

/* Modal Styles */
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modalContent {
  width: 90%;
  max-width: 500px;
  max-height: 80vh;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modalHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
}

.modalTitle {
  font-size: 18px;
  font-weight: bold;
  color: #212121;
}

.modalBody {
  padding: 16px;
  overflow-y: auto;
}

.modalFooter {
  display: flex;
  justify-content: flex-end;
  padding: 16px;
  border-top: 1px solid #e0e0e0;
}

/* Form Styles */
.formGroup {
  margin-bottom: 16px;
}

.formLabel {
  font-size: 14px;
  font-weight: bold;
  color: #424242;
  margin-bottom: 8px;
  display: block;
}

.formInput {
  width: 100%;
  background-color: #f5f5f5;
  border-radius: 4px;
  padding: 12px;
  font-size: 16px;
  color: #212121;
  border: 1px solid #e0e0e0;
}

.textArea {
  min-height: 80px;
  resize: vertical;
}

/* Button Styles */
.modalButton {
  padding: 10px 16px;
  border-radius: 4px;
  margin-left: 8px;
  cursor: pointer;
  border: none;
}

.cancelButton {
  background-color: #f5f5f5;
}

.cancelButtonText {
  color: #757575;
  font-weight: bold;
}

.saveButton {
  background-color: #4285F4;
}

.saveButtonText {
  color: #fff;
  font-weight: bold;
}

.loadingButtonContent {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Spinner for loading states */
.spinner {
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top: 3px solid #4285F4;
  width: 20px;
  height: 20px;
  animation: spin 1s linear infinite;
  margin-right: 8px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Voice container styles */
.voiceContainer {
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.voiceInstructions {
  font-size: 16px;
  color: #424242;
  text-align: center;
  margin-bottom: 16px;
}

.voiceExample {
  font-size: 14px;
  color: #757575;
  font-style: italic;
  text-align: center;
  margin-bottom: 24px;
  background-color: #f5f5f5;
  padding: 12px;
  border-radius: 8px;
}

.recordButton {
  width: 80px;
  height: 80px;
  border-radius: 40px;
  background-color: #E3F2FD;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 20px 0;
  border: none;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.recordingButton {
  background-color: #EA4335;
}

.recordingText {
  font-size: 14px;
  color: #EA4335;
  margin-top: 8px;
}

.processingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.processingText {
  font-size: 16px;
  color: #424242;
  margin-top: 16px;
}

.resultContainer {
  width: 100%;
  padding: 16px;
}

.recognizedTextTitle {
  font-size: 16px;
  font-weight: bold;
  color: #424242;
  margin-bottom: 8px;
}

.recognizedText {
  font-size: 14px;
  color: #757575;
  background-color: #f5f5f5;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 16px;
}

.medicationSummary {
  background-color: #E3F2FD;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
}

.summaryTitle {
  font-size: 16px;
  font-weight: bold;
  color: #4285F4;
  margin-bottom: 12px;
}

.summaryItem {
  display: flex;
  margin-bottom: 8px;
}

.summaryLabel {
  font-size: 14px;
  font-weight: bold;
  color: #424242;
  width: 100px;
}

.summaryValue {
  font-size: 14px;
  color: #424242;
  flex: 1;
}

.voiceActionButtons {
  display: flex;
  justify-content: space-between;
  margin-top: 16px;
}

.voiceActionButton {
  flex: 1;
  padding: 12px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  margin: 0 4px;
}

.primaryVoiceButton {
  background-color: #4285F4;
}

.primaryVoiceButtonText {
  color: #fff;
  font-weight: bold;
  font-size: 14px;
}

.secondaryVoiceButton {
  background-color: #f5f5f5;
  border: 1px solid #BDBDBD;
}

.secondaryVoiceButtonText {
  color: #757575;
  font-weight: bold;
  font-size: 14px;
}

/* Time and date picker styles */
.timePickerInput, .datePickerInput {
  background: none;
  border: none;
  font-size: 16px;
  color: #212121;
  margin-left: 8px;
  flex: 1;
}

.timePickerInput:focus, .datePickerInput:focus {
  outline: none;
}

/* Frequency options */
.frequencyOptions {
  display: flex;
  justify-content: space-between;
  margin-top: 8px;
}

.frequencyOption {
  flex: 1;
  padding: 10px 12px;
  border: 1px solid #E0E0E0;
  border-radius: 4px;
  margin: 0 4px;
  background: none;
  cursor: pointer;
}

.frequencyOptionSelected {
  background-color: #E3F2FD;
  border-color: #4285F4;
}

.frequencyOptionText {
  color: #757575;
  font-weight: 500;
}

.frequencyOptionTextSelected {
  color: #4285F4;
  font-weight: bold;
}

/* Day selection */
.daysContainer {
  display: flex;
  justify-content: space-between;
  margin-top: 8px;
}

.dayButton {
  width: 36px;
  height: 36px;
  border-radius: 18px;
  border: 1px solid #E0E0E0;
  display: flex;
  justify-content: center;
  align-items: center;
  background: none;
  cursor: pointer;
}

.dayButtonSelected {
  background-color: #4285F4;
  border-color: #4285F4;
}

.dayButtonText {
  font-size: 14px;
  font-weight: bold;
  color: #757575;
}

.dayButtonTextSelected {
  color: #FFFFFF;
}

/* Time slot management */
.timeHeaderContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.addTimeButton {
  display: flex;
  align-items: center;
  background: none;
  border: none;
  cursor: pointer;
}

.addTimeText {
  color: #4285F4;
  margin-left: 4px;
  font-weight: 500;
}

.timeSlotContainer {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.timePickerButton {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 4px;
  padding: 12px;
}

.removeTimeButton {
  margin-left: 8px;
  padding: 4px;
  background: none;
  border: none;
  cursor: pointer;
}

/* Modal header actions */
.modalHeaderActions {
  display: flex;
  align-items: center;
}

.voiceModeButton {
  margin-right: 16px;
  background: none;
  border: none;
  cursor: pointer;
}
