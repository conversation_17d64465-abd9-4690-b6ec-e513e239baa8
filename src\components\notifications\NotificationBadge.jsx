import React from 'react';
import { IoNotificationsOutline } from 'react-icons/io5';
import '../../styles/notifications/notificationBadge.css';

const NotificationBadge = ({ count = 0, onPress }) => {
  // Add debug logging
  const handlePress = () => {
    console.log('NotificationBadge pressed, count:', count);
    if (onPress) {
      onPress();
    }
  };

  return (
    <div 
      onClick={handlePress} 
      className="notification-badge-container"
      style={{ cursor: 'pointer' }}
    >
      <IoNotificationsOutline size={24} color="white" />
      {count > 0 && (
        <div className="notification-badge">
          <span className="notification-badge-text">
            {count > 9 ? '9+' : count}
          </span>
        </div>
      )}
    </div>
  );
};

export default NotificationBadge;
