import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import '../../styles/notifications/profileNotification.css';
// Assuming you're using react-icons instead of expo icons
import { IoPersonSharp } from 'react-icons/io5';

const ProfileNotification = ({ userName }) => {
  const navigate = useNavigate();
  const [dismissed, setDismissed] = useState(false);

  if (dismissed) {
    return null;
  }

  return (
    <div className="container">
      <div className="contentContainer">
        <div className="iconContainer">
          <IoPersonSharp size={24} color="#fff" />
        </div>
        <div className="textContainer">
          <h3 className="title">Complete Your Profile</h3>
          <p className="message">
            Hello {userName || 'there'}! Please complete your profile to unlock all features.
          </p>
        </div>
      </div>
      <div className="buttonContainer">
        <button 
          className="completeButton"
          onClick={() => navigate('/profile')}
        >
          <span className="buttonText">Complete Now</span>
        </button>
        <button 
          className="dismissButton"
          onClick={() => setDismissed(true)}
        >
          <span className="dismissText">Later</span>
        </button>
      </div>
    </div>
  );
};

export default ProfileNotification;
