.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-title {
  font-size: 20px;
  font-weight: bold;
  color: #333;
  margin: 0;
}

.refresh-button {
  padding: 8px;
  background: none;
  border: none;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.refresh-button:hover {
  background-color: #f0f0f0;
}

.refresh-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.refresh-icon {
  width: 24px;
  height: 24px;
}

.refresh-icon.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.search-container {
  margin: 16px;
}

.search-input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 16px;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  box-sizing: border-box;
}

.search-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  flex: 1;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-radius: 50%;
  border-top: 4px solid #007bff;
  animation: spin 1s linear infinite;
}

.loading-text {
  margin-top: 16px;
  font-size: 16px;
  color: #666;
}

.patients-list {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.patients-list.empty-list {
  display: flex;
  justify-content: center;
  align-items: center;
}

.patient-card {
  background-color: #fff;
  margin-bottom: 12px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
}

.patient-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.patient-card-content {
  display: flex;
  align-items: center;
  padding: 16px;
}

.patient-avatar {
  width: 50px;
  height: 50px;
  border-radius: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-weight: bold;
  font-size: 18px;
}

.patient-info {
  flex: 1;
  margin-left: 12px;
}

.patient-name {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 2px;
}

.patient-email {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.patient-details {
  font-size: 12px;
  color: #666;
}

.view-button {
  padding: 8px;
  background: none;
  border: none;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.view-button:hover {
  background-color: #f0f0f0;
}

.chevron-icon {
  width: 24px;
  height: 24px;
}

.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.empty-icon {
  width: 60px;
  height: 60px;
  color: #ccc;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 18px;
  font-weight: 600;
  color: #666;
  margin-bottom: 8px;
}

.empty-subtext {
  font-size: 14px;
  color: #999;
  max-width: 80%;
  line-height: 1.4;
}

/* Responsive design */
@media (max-width: 768px) {
  .header {
    padding: 12px;
  }
  
  .header-title {
    font-size: 18px;
  }
  
  .search-container {
    margin: 12px;
  }
  
  .patients-list {
    padding: 12px;
  }
  
  .patient-card-content {
    padding: 12px;
  }
  
  .patient-avatar {
    width: 40px;
    height: 40px;
    border-radius: 20px;
    font-size: 16px;
  }
}
