import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { collection, getDocs, query, where, orderBy } from 'firebase/firestore';
import { format } from 'date-fns';
import { FiUserPlus, FiSettings, FiFileText } from 'react-icons/fi';
import { getThemeForRole, ROLE_COLORS } from '../../../config/theme';
import { db } from '../../../config/firebase';
import { useAuth } from '../../../contexts/AuthContext';
import DashboardLayout from '../DashboardLayout';
import DashboardCard from '../DashboardCard';
import Dashboard<PERSON>hart from '../DashboardChart';
import UpcomingList from '../UpcomingList';
import '../../../styles/dashboards/admin/adminDashboard.css';

const AdminDashboard = ({ notifications = [] }) => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const theme = getThemeForRole('admin');
  const adminColors = ROLE_COLORS.admin;
  const [refreshing, setRefreshing] = useState(false);
  const [stats, setStats] = useState({
    activeUsers: '0',
    newPatients: '0',
    appointments: '0',
    revenue: '0'
  });

  const [tasks, setTasks] = useState([]);

  // Empty revenue data structure
  const [revenueData, setRevenueData] = useState({
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
    datasets: [
      {
        data: [0, 0, 0, 0, 0, 0],
        color: (opacity = 1) => `rgba(76, 175, 80, ${opacity})`,
        strokeWidth: 2
      }
    ],
    legend: ['Revenue ($)']
  });

  // Empty user type data structure
  const [userTypeData, setUserTypeData] = useState({
    data: []
  });

  // Empty appointment data structure
  const [appointmentData, setAppointmentData] = useState({
    labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
    datasets: [
      {
        data: [0, 0, 0, 0, 0, 0, 0],
        color: (opacity = 1) => `rgba(156, 39, 176, ${opacity})`,
        strokeWidth: 2
      }
    ],
    legend: ['Appointments']
  });

  const menuItems = [
    { label: 'Dashboard', icon: 'home', screen: 'AdminDashboard' },
    { label: 'User Management', icon: 'people', screen: 'UserManagement' },
    { label: 'Reports', icon: 'bar-chart', screen: 'Reports' },
    { label: 'Billing', icon: 'card', screen: 'Billing' },
    { label: 'Inventory', icon: 'cube', screen: 'Inventory' },
    { label: 'Support Tickets', icon: 'help-buoy', screen: 'SupportTickets' },
    { label: 'My QR Code', icon: 'qr-code', screen: 'UserQRCode' },
    { label: 'Profile', icon: 'person', screen: 'Profile' },
    { label: 'Settings', icon: 'settings', screen: 'Settings' }
  ];

  // Fetch dashboard data from Firebase
  const fetchDashboardData = async () => {
    try {
      // Fetch users data
      const usersCollection = collection(db, 'users');
      const usersSnapshot = await getDocs(usersCollection);

      // Count active users
      const activeUsers = usersSnapshot.docs.filter(doc =>
        doc.data().status === 'active' || !doc.data().status
      ).length;

      // Count new patients in the last 30 days
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const newPatients = usersSnapshot.docs.filter(doc => {
        const userData = doc.data();
        const isPatient = userData.role?.toLowerCase() === 'patient';
        const createdAt = userData.createdAt?.toDate?.() || new Date(userData.createdAt);
        return isPatient && createdAt > thirtyDaysAgo;
      }).length;

      // Fetch appointments data
      const appointmentsCollection = collection(db, 'appointments');
      const appointmentsSnapshot = await getDocs(appointmentsCollection);
      const appointments = appointmentsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      // Count total appointments
      const totalAppointments = appointments.length;

      // Calculate user distribution by role
      const usersByRole = {};
      usersSnapshot.docs.forEach(doc => {
        const role = doc.data().role?.toLowerCase() || 'other';
        usersByRole[role] = (usersByRole[role] || 0) + 1;
      });

      // Format user distribution for pie chart
      const userTypeChartData = Object.keys(usersByRole).map((role, index) => {
        const colors = ['#5C6BC0', '#26A69A', '#FFA726', '#EF5350', '#9E9E9E'];
        return {
          name: role.charAt(0).toUpperCase() + role.slice(1),
          value: usersByRole[role],
          color: colors[index % colors.length],
          legendFontColor: '#7F7F7F',
          legendFontSize: 12
        };
      });

      // Calculate weekly appointment distribution
      const last7Days = Array(7).fill(0).map((_, i) => {
        const d = new Date();
        d.setDate(d.getDate() - i);
        return format(d, 'yyyy-MM-dd');
      }).reverse();

      const weeklyAppointmentCounts = last7Days.map(date => {
        return appointments.filter(app => app.date === date).length;
      });

      // Update state with real data
      setStats({
        activeUsers: activeUsers.toString(),
        newPatients: newPatients.toString(),
        appointments: totalAppointments.toString(),
        revenue: '0' // Revenue data might not be available
      });

      setUserTypeData({
        data: userTypeChartData
      });

      setAppointmentData({
        labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
        datasets: [
          {
            data: weeklyAppointmentCounts,
            color: (opacity = 1) => `rgba(156, 39, 176, ${opacity})`,
            strokeWidth: 2
          }
        ],
        legend: ['Appointments']
      });

      // Fetch administrative tasks if available
      const tasksCollection = collection(db, 'tasks');
      try {
        const tasksQuery = query(
          tasksCollection,
          where('assignedTo', '==', 'admin'),
          orderBy('dueDate', 'asc')
        );
        const tasksSnapshot = await getDocs(tasksQuery);

        const formattedTasks = tasksSnapshot.docs.map(doc => {
          const taskData = doc.data();
          return {
            id: doc.id,
            title: taskData.title || 'Task',
            description: taskData.description || '',
            time: taskData.dueDate ? `Due ${taskData.dueDate}` : 'No due date',
            type: 'task',
            status: taskData.status || 'pending'
          };
        });

        setTasks(formattedTasks);
      } catch (error) {
        console.log('No tasks collection available:', error);
        setTasks([]);
      }

    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    }
  };

  // Load data when component mounts
  useEffect(() => {
    fetchDashboardData();
  }, []);

  const onRefresh = async () => {
    setRefreshing(true);
    await fetchDashboardData();
    setRefreshing(false);
  };

  return (
    <DashboardLayout
      title="Admin Dashboard"
      roleName="Administrator"
      menuItems={menuItems}
      userRole="admin"
      notifications={notifications}
    >
      <div className="admin-dashboard">
        <div className="refresh-control">
          {refreshing ? (
            <div className="refresh-spinner"></div>
          ) : (
            <button className="refresh-button" onClick={onRefresh}>
              Refresh Data
            </button>
          )}
        </div>

        <div className="header-section">
          <div className="header-gradient">
            <div className="greeting">
              <div>
                <h2 className="greeting-text">
                  Welcome, {user?.firstName || 'Admin'}
                </h2>
                <p className="date-text">
                  {new Date().toLocaleDateString('en-US', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="content-section">
          <h3 className="section-title">System Overview</h3>

          <div className="cards-container">
            <div className="card-row">
              <DashboardCard
                title="Active Users"
                value={stats.activeUsers}
                icon="people"
                iconColor="#5C6BC0"
                width="48%"
                onPress={() => navigate('UserManagement')}
              />
              <DashboardCard
                title="New Patients"
                value={stats.newPatients}
                icon="person-add"
                iconColor="#26A69A"
                width="48%"
                trend="up"
                trendValue="+8%"
                onPress={() => navigate('PatientRegistrations')}
              />
            </div>
            <div className="card-row">
              <DashboardCard
                title="Appointments"
                value={stats.appointments}
                icon="calendar"
                iconColor="#FFA726"
                width="48%"
                onPress={() => navigate('AppointmentStats')}
              />
              <DashboardCard
                title="Revenue (USD)"
                value={stats.revenue}
                unit="$"
                icon="stats-chart"
                iconColor="#66BB6A"
                width="48%"
                trend="up"
                trendValue="+5%"
                onPress={() => navigate('FinanceReport')}
              />
            </div>
          </div>

          {/* Only show revenue chart if there's data */}
          {revenueData.datasets[0].data.some(value => value > 0) && (
            <DashboardChart
              title="Monthly Revenue"
              subtitle="Last 6 months"
              data={revenueData}
              type="line"
              chartConfig={{
                color: (opacity = 1) => `rgba(76, 175, 80, ${opacity})`,
              }}
            />
          )}

          <div className="chart-row">
            {/* Only show user distribution chart if there's data */}
            {userTypeData.data.length > 0 && (
              <DashboardChart
                title="User Distribution"
                subtitle="By role"
                data={userTypeData}
                type="pie"
                height={180}
                width="48%"
                className="half-chart"
              />
            )}

            {/* Only show appointments chart if there's data */}
            {appointmentData.datasets[0].data.some(value => value > 0) && (
              <DashboardChart
                title="Weekly Appointments"
                subtitle="Current week"
                data={appointmentData}
                type="bar"
                height={180}
                width="48%"
                className="half-chart"
              />
            )}
          </div>

          <UpcomingList
            title="Administrative Tasks"
            data={tasks}
            onItemPress={(item) => navigate('TaskDetail', { id: item.id })}
            onViewAll={() => navigate('Tasks')}
            emptyText="No pending tasks"
          />

          <UpcomingList
            title="System Notifications"
            data={notifications}
            onItemPress={(item) => navigate('NotificationDetail', { id: item.id })}
            onViewAll={() => navigate('Notifications')}
            emptyText="No notifications"
          />

          <div className="quick-actions">
            <h3 className="section-title">Quick Actions</h3>
            <div className="action-buttons">
              <div
                className="action-button"
                onClick={() => navigate('AddUser')}
              >
                <div className="action-icon" style={{ backgroundColor: '#EDE7F6' }}>
                  <FiUserPlus size={24} color="#673AB7" />
                </div>
                <p className="action-text">Add User</p>
              </div>

              <div
                className="action-button"
                onClick={() => navigate('GenerateReport')}
              >
                <div className="action-icon" style={{ backgroundColor: '#E0F7FA' }}>
                  <FiFileText size={24} color="#00ACC1" />
                </div>
                <p className="action-text">Generate Report</p>
              </div>

              <div
                className="action-button"
                onClick={() => navigate('SystemSettings')}
              >
                <div className="action-icon" style={{ backgroundColor: '#FFF3E0' }}>
                  <FiSettings size={24} color="#FF9800" />
                </div>
                <p className="action-text">System Settings</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default AdminDashboard;