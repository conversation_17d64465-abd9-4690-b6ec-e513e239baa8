.card {
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.titleContainer {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  margin-bottom: 16px;
}

.title {
  font-size: 18px;
  font-weight: 600;
}

.profileImageContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  margin-bottom: 16px;
}

.profileImage {
  width: 150px;
  height: 150px;
  border-radius: 75px;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.editButton {
  position: absolute;
  bottom: 5px;
  right: 32%;
  width: 40px;
  height: 40px;
  border-radius: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.3);
}

.helperText {
  text-align: center;
  font-size: 14px;
  color: #666;
  margin-top: 8px;
}

/* Modal styles */
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.profileModalContent {
  background-color: #fff;
  border-radius: 12px;
  width: 90%;
  max-width: 450px;
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  border: 1px solid #e0e0e0;
}

.modalHeaderBar {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background-color: #ffffff;
  border-bottom: 1px solid #e0e0e0;
}

.modalDivider {
  height: 1px;
  background-color: #e0e0e0;
  width: 100%;
}

.modalInstructions {
  padding: 15px;
  display: flex;
  align-items: center;
}

.modalInstructionsText {
  font-size: 16px;
  color: #666;
  text-align: center;
}

.closeButton {
  padding: 5px;
  cursor: pointer;
}

.modalTitle {
  font-size: 20px;
  font-weight: bold;
  color: #333;
}

.modalButtons {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin-bottom: 15px;
  margin-top: 10px;
  padding: 0 20px;
}

.profileModalButton {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 10px;
  border-radius: 12px;
  height: 120px;
  background-color: #ffffff;
  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.2);
  border: 1px solid #e0e0e0;
  cursor: pointer;
}

.profileButtonIconContainer {
  width: 60px;
  height: 60px;
  border-radius: 30px;
  background-color: #f5f5f5;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 10px;
  border: 1px solid #e0e0e0;
}

.profileModalButtonText {
  color: #333333;
  font-size: 16px;
  font-weight: 600;
  text-align: center;
}

.previewContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 20px 0;
}

.previewTitle {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 10px;
  color: #333;
}

.previewImage {
  width: 200px;
  height: 200px;
  border-radius: 100px;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.loadingText {
  margin-top: 10px;
  font-size: 16px;
  color: #666;
}

.cancelButton {
  padding: 15px;
  margin: 20px;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #ffffff;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  cursor: pointer;
}

.cancelButtonText {
  font-size: 16px;
  color: #555;
  font-weight: 600;
}

.avatarText {
  width: 120px;
  height: 120px;
  border-radius: 60px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 40px;
  color: white;
}

.cardContent {
  padding: 16px;
}
