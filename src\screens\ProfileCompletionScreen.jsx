import React, { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import SharedProfileForm from '../components/profile/SharedProfileForm';
import { COLORS } from '../config/theme';
import '../styles/screens/profileCompletionScreen.css';

const ProfileCompletionScreen = () => {
  const { user, updateUserProfile } = useAuth();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);

  // Initial form data from user object
  const initialData = {
    firstName: user?.firstName || '',
    lastName: user?.lastName || '',
    email: user?.email || '',
    // Include other fields as needed
  };

  const handleSubmit = async (formData) => {
    try {
      setLoading(true);
      
      // Update the user profile
      await updateUserProfile({
        ...formData,
        profileCompleted: true,
        updatedAt: new Date().toISOString(),
      });

      // Show success message
      toast.success('Profile completed successfully!', {
        position: "top-right",
        autoClose: 3000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
      });

      // Wait for message to be visible before navigating
      setTimeout(() => {
        navigate('/dashboard', { replace: true });
      }, 1000);
      
    } catch (error) {
      console.error('Error completing profile:', error);
      toast.error(error.message || 'Failed to complete profile. Please try again.', {
        position: "top-right",
        autoClose: 5000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  if (!user) {
    return (
      <div className="loading-container">
        <div className="spinner"></div>
      </div>
    );
  }

  return (
    <div className="container">
      <h1 className="header">Complete Your Profile</h1>
      <p className="subheader">
        Please provide the following information to complete your profile.
      </p>
      
      <SharedProfileForm
        initialData={initialData}
        onSubmit={handleSubmit}
        loading={loading}
        buttonText="Complete Profile"
        requiredFields={['firstName', 'lastName', 'email', 'phone', 'dateOfBirth', 'gender']}
        nonEditableFields={['firstName', 'lastName', 'email']}
      />
    </div>
  );
};

export default ProfileCompletionScreen;
