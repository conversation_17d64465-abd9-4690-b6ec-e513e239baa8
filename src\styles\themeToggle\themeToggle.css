/* Theme toggle button */
.theme-toggle {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: var(--primary);
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  box-shadow: var(--shadow-md);
  z-index: 100;
  border: none;
}

.theme-toggle:hover {
  background-color: var(--primary-dark);
}
