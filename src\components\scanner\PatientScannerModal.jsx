import React from 'react';
import { IoClose, IoQrCode, IoKeypad, IoChevronForward } from 'react-icons/io5';
import { useNavigate } from 'react-router-dom';
import '../../styles/scanner/patientScannerModal';

const PatientScannerModal = ({ visible, onClose, onSuccess, scannerTitle = "Scan Patient", relationshipType }) => {
  const navigate = useNavigate();

  // If the modal is not visible, don't render anything
  if (!visible) return null;

  const handleScanQRCode = () => {
    onClose();
    // Navigate to the QR code scanner screen
    navigate('/scan-patient-qr', {
      state: {
        mode: 'camera',
        relationshipType: relationshipType
      }
    });
  };

  const handleEnterCode = () => {
    onClose();
    // Navigate to the manual code entry screen
    navigate('/scan-patient-qr', {
      state: {
        mode: 'manual',
        relationshipType: relationshipType
      }
    });
  };

  const handleOverlayClick = (e) => {
    // Close the modal when clicking on the overlay (outside the modal content)
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div className="overlay" onClick={handleOverlayClick}>
      <div className="modalContent" onClick={(e) => e.stopPropagation()}>
        <div className="header">
          <h2 className="title">{scannerTitle}</h2>
          <button className="closeButton" onClick={onClose}>
            <IoClose size={24} color="#666" />
          </button>
        </div>

        <div className="optionsContainer">
          <div className="option" onClick={handleScanQRCode}>
            <div className="iconContainer blueIconContainer">
              <IoQrCode size={28} color="#2196F3" />
            </div>
            <div className="optionTextContainer">
              <h3 className="optionTitle">Scan QR Code</h3>
              <p className="optionDescription">
                Use camera to scan patient's QR code
              </p>
            </div>
            <IoChevronForward size={20} className="chevronIcon" />
          </div>

          <div className="divider" />

          <div className="option" onClick={handleEnterCode}>
            <div className="iconContainer greenIconContainer">
              <IoKeypad size={28} color="#4CAF50" />
            </div>
            <div className="optionTextContainer">
              <h3 className="optionTitle">Enter Code Manually</h3>
              <p className="optionDescription">
                Type the 8-character patient code
              </p>
            </div>
            <IoChevronForward size={20} className="chevronIcon" />
          </div>
        </div>
      </div>
    </div>
  );
};

export default PatientScannerModal;
