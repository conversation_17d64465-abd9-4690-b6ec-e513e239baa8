.cardWrapper {
  margin-bottom: 16px;
}

.card {
  border-radius: 16px;
  box-shadow: 0 6px 10px rgba(0, 0, 0, 0.3);
  border-left-width: 4px;
  overflow: hidden;
  background-color: #ffffff;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.cardGradient {
  width: 100%;
  padding: 16px;
}

.cardContent {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 150px;
  justify-content: center;
  padding: 10px 0;
}

.iconContainer {
  width: 56px;
  height: 56px;
  border-radius: 28px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 12px;
  box-shadow: 0 3px 5px rgba(0, 0, 0, 0.25);
  border: 1px solid rgba(255, 255, 255, 0.5);
}

.cardTextContainer {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.cardTitle {
  font-size: 15px;
  color: #616161;
  margin-bottom: 8px;
  text-align: center;
  font-weight: 600;
  letter-spacing: 0.3px;
}

.cardValue {
  font-size: 28px;
  font-weight: bold;
  color: #212121;
  text-align: center;
  letter-spacing: 0.5px;
}

.trendContainer {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-left: 8px;
  background-color: rgba(0, 0, 0, 0.05);
  padding: 2px 8px;
  border-radius: 12px;
}

.trendValue {
  font-size: 12px;
  margin-left: 2px;
  font-weight: bold;
}

.valueContainer {
  display: flex;
  flex-direction: row;
  align-items: baseline;
  flex-wrap: wrap;
  justify-content: center;
}

.unitText {
  font-size: 14px;
  color: #757575;
  margin-left: 4px;
  font-weight: 500;
}

.statusContainer {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-top: 8px;
  padding: 4px 10px;
  border-radius: 12px;
  align-self: center;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.statusText {
  font-size: 12px;
  margin-left: 4px;
  font-weight: bold;
  letter-spacing: 0.2px;
}
