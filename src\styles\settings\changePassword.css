.container {
  flex: 1;
  background-color: var(--background);
}

.content {
  padding: 16px;
  background-color: transparent;
}

.headerContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 24px 0;
}

.headerText {
  font-size: 24px;
  font-weight: bold;
  margin-top: 16px;
  margin-bottom: 8px;
}

.subHeaderText {
  font-size: 16px;
  color: var(--text-medium);
  text-align: center;
}

.formContainer {
  margin-top: 16px;
}

.inputContainer {
  margin-bottom: 20px;
}

.inputLabel {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
  color: var(--text-dark);
}

.inputBox {
  display: flex;
  flex-direction: row;
  align-items: center;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 0 12px;
  background-color: #fff;
}

.icon {
  margin-right: 10px;
  color: rgba(16, 107, 0, 1);
}

.input {
  flex: 1;
  height: 50px;
  color: var(--text-dark);
  border: none;
  outline: none;
  background: transparent;
}

.passwordIcon {
  padding: 10px;
  cursor: pointer;
  background: none;
  border: none;
}

.passwordHint {
  font-size: 12px;
  color: var(--text-medium);
  margin-top: 4px;
}

.button {
  height: 50px;
  border-radius: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 16px;
  border: none;
  cursor: pointer;
  color: #fff;
  font-weight: bold;
}

.buttonDisabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.buttonText {
  color: #fff;
  font-size: 16px;
  font-weight: bold;
}

.cancelButton {
  height: 50px;
  border-radius: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 12px;
  background: transparent;
  border: none;
  cursor: pointer;
}

.cancelButtonText {
  font-size: 16px;
  font-weight: 500;
}
