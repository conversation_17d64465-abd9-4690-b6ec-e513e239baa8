import React, { useState, useEffect } from 'react';
import '../styles/screens/myDoctorsScreen.css';

// Mock icons component for web
const Ionicons = ({ name, size, color, style, className }) => {
  const iconMap = {
    'chevron-up': '▲',
    'chevron-down': '▼',
    'mail': '✉',
    'location': '📍',
    'call': '📞',
    'business': '🏥',
    'school': '🎓',
    'briefcase': '💼',
    'globe': '🌐',
    'medical': '⚕',
    'calendar': '📅',
    'videocam': '📹',
    'chatbubble': '💬'
  };

  return (
    <span
      className={`icon ${className || ''}`}
      style={{
        fontSize: `${size}px`,
        color: color,
        display: 'inline-block',
        ...style
      }}
    >
      {iconMap[name] || '?'}
    </span>
  );
};

// Mock API and config imports
const usersAPI = {
  getLinkedUsers: async (type) => {
    // Mock implementation
    return [];
  }
};

const PATIENT_COLORS = {
  primary: '#007bff'
};

const ROLE_COLORS = {
  doctor: {
    primary: '#28a745'
  }
};

// Mock Firebase
const db = {};
const doc = () => ({});
const getDoc = async () => ({ exists: () => false, data: () => ({}) });

// Mock navigation
const useNavigation = () => ({
  navigate: (screen, params) => {
    console.log(`Navigate to ${screen}`, params);
  }
});

// Mock flash message
const showMessage = ({ message, description, type }) => {
  console.log(`${type}: ${message} - ${description}`);
};

const DoctorCard = ({ doctor, onRequestAppointment, onStartVideoCall }) => {
  const [showDetails, setShowDetails] = useState(false);

  // Extract first and last name from displayName
  const nameParts = doctor.displayName ? doctor.displayName.split(' ') : ['', ''];
  const firstName = nameParts[0] || '';
  const lastName = nameParts.slice(1).join(' ') || '';

  // Create initials for avatar
  const initials = `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();

  const toggleDetails = () => {
    setShowDetails(!showDetails);
  };

  // Format address if available
  const formatAddress = () => {
    if (!doctor.address) return 'Address not available';

    // Handle both string and object address formats
    if (typeof doctor.address === 'string') {
      return doctor.address;
    }

    const { street, city, state, zipCode, country } = doctor.address;
    const parts = [];

    if (street) parts.push(street);
    if (city) parts.push(city);
    if (state) parts.push(state);
    if (zipCode && zipCode !== '...') parts.push(zipCode);
    else if (zipCode === '...') parts.push('Zip: ...');
    if (country) parts.push(country);

    return parts.join(', ') || 'Address not available';
  };

  return (
    <div className="doctor-card">
      <div
        className="card-content"
        onClick={toggleDetails}
      >
        <div className="card-header">
          <div className="avatar-container">
            {doctor.profileImage ? (
              <img src={doctor.profileImage} alt="Doctor" className="avatar" />
            ) : (
              <div className="avatar-placeholder" style={{ backgroundColor: ROLE_COLORS.doctor.primary }}>
                <span className="avatar-text">{initials}</span>
              </div>
            )}
          </div>

          <div className="doctor-info">
            <h3 className="doctor-name">{doctor.displayName ? `Dr. ${doctor.displayName}` : 'Doctor'}</h3>
            <p className="doctor-specialty">
              {doctor.specialty === '...' ? 'Specialty: ...' : doctor.specialty}
            </p>

            {doctor.email && (
              <div className="contact-info">
                <div className="contact-item">
                  <Ionicons name="mail" size={16} color={ROLE_COLORS.doctor.primary} />
                  <span className="contact-text">{doctor.email}</span>
                </div>
              </div>
            )}
          </div>

          <Ionicons
            name={showDetails ? "chevron-up" : "chevron-down"}
            size={24}
            color={ROLE_COLORS.doctor.primary}
            className="expand-icon"
          />
        </div>

        {showDetails && (
          <div className="details-container">
            <div className="detail-section">
              <div className="detail-title-container">
                <Ionicons name="location" size={16} color={ROLE_COLORS.doctor.primary} />
                <span className="detail-title">Address</span>
              </div>
              <p className="detail-text">{formatAddress()}</p>
            </div>

            {doctor.phoneNumber && (
              <div className="detail-section">
                <div className="detail-title-container">
                  <Ionicons name="call" size={16} color={ROLE_COLORS.doctor.primary} />
                  <span className="detail-title">Phone</span>
                </div>
                <p className="detail-text">{doctor.phoneNumber}</p>
              </div>
            )}

            {doctor.hospital && (
              <div className="detail-section">
                <div className="detail-title-container">
                  <Ionicons name="business" size={16} color={ROLE_COLORS.doctor.primary} />
                  <span className="detail-title">Hospital</span>
                </div>
                <p className="detail-text">{doctor.hospital}</p>
              </div>
            )}

            {doctor.education && (
              <div className="detail-section">
                <div className="detail-title-container">
                  <Ionicons name="school" size={16} color={ROLE_COLORS.doctor.primary} />
                  <span className="detail-title">Education</span>
                </div>
                <p className="detail-text">{doctor.education}</p>
              </div>
            )}

            {doctor.experience && (
              <div className="detail-section">
                <div className="detail-title-container">
                  <Ionicons name="briefcase" size={16} color={ROLE_COLORS.doctor.primary} />
                  <span className="detail-title">Experience</span>
                </div>
                <p className="detail-text">{doctor.experience} years</p>
              </div>
            )}

            {doctor.languages && doctor.languages.length > 0 && (
              <div className="detail-section">
                <div className="detail-title-container">
                  <Ionicons name="globe" size={16} color={ROLE_COLORS.doctor.primary} />
                  <span className="detail-title">Languages</span>
                </div>
                <p className="detail-text">{doctor.languages.join(', ')}</p>
              </div>
            )}

            {!doctor.hospital && !doctor.education &&
             !doctor.experience && (!doctor.languages || doctor.languages.length === 0) && (
              <div className="no-details-container">
                <p className="no-details-text">No additional details available</p>
              </div>
            )}
          </div>
        )}
      </div>

      <div className="actions-container">
        <button
          className="action-button primary-action-button"
          onClick={() => onRequestAppointment(doctor)}
        >
          <Ionicons name="calendar" size={22} color="#fff" />
          <span className="action-text primary-action-text">Request Appointment</span>
        </button>

        <div className="secondary-actions-row">
          <button
            className="action-button secondary-action-button"
            onClick={() => onStartVideoCall(doctor)}
          >
            <Ionicons name="videocam" size={22} color={PATIENT_COLORS.primary} />
            <span className="action-text">Video Call</span>
          </button>

          <button className="action-button secondary-action-button">
            <Ionicons name="chatbubble" size={22} color={PATIENT_COLORS.primary} />
            <span className="action-text">Message</span>
          </button>
        </div>
      </div>
    </div>
  );
};

const MyDoctorsScreen = () => {
  const navigation = useNavigation();
  const [doctors, setDoctors] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    fetchDoctors();
  }, []);

  const fetchDoctorsFromFirebase = async (doctorIds) => {
    try {
      const detailedDoctors = [];

      for (const doctorId of doctorIds) {
        // Get the doctor document from Firestore
        const doctorDoc = doc(db, 'users', doctorId);
        const doctorSnapshot = await getDoc(doctorDoc);

        if (doctorSnapshot.exists()) {
          const doctorData = doctorSnapshot.data();
          console.log('Raw doctor data:', doctorData);

          // Format the doctor data with only real data from Firebase
          const doctorInfo = {
            uid: doctorId
          };

          // Name fields
          if (doctorData.displayName) doctorInfo.displayName = doctorData.displayName;
          else if (doctorData.firstName || doctorData.lastName) {
            doctorInfo.displayName = `${doctorData.firstName || ''} ${doctorData.lastName || ''}`.trim();
          }

          if (doctorData.firstName) doctorInfo.firstName = doctorData.firstName;
          if (doctorData.lastName) doctorInfo.lastName = doctorData.lastName;

          // Contact information
          if (doctorData.email) doctorInfo.email = doctorData.email;
          // Check for phone number in different possible field names
          if (doctorData.phoneNumber) doctorInfo.phoneNumber = doctorData.phoneNumber;
          else if (doctorData.phone) doctorInfo.phoneNumber = doctorData.phone;
          else if (doctorData.tel) doctorInfo.phoneNumber = doctorData.tel;
          else if (doctorData.telephone) doctorInfo.phoneNumber = doctorData.telephone;
          else if (doctorData.mobile) doctorInfo.phoneNumber = doctorData.mobile;
          else if (doctorData.mobileNumber) doctorInfo.phoneNumber = doctorData.mobileNumber;

          // Specialty - add '...' if not available
          doctorInfo.specialty = doctorData.specialty || doctorData.speciality || '...';

          // Profile image
          if (doctorData.profileImage) doctorInfo.profileImage = doctorData.profileImage;

          // Address information - handle both string and object formats
          if (typeof doctorData.address === 'string') {
            doctorInfo.address = {
              street: doctorData.address
            };
          } else if (doctorData.address && typeof doctorData.address === 'object') {
            doctorInfo.address = doctorData.address;
          }

          // Add country, city if they exist directly in the doctor data
          if (doctorData.country) {
            if (!doctorInfo.address) doctorInfo.address = {};
            doctorInfo.address.country = doctorData.country;
          }

          if (doctorData.city) {
            if (!doctorInfo.address) doctorInfo.address = {};
            doctorInfo.address.city = doctorData.city;
          }

          // Add zipCode - set to '...' if not available
          if (doctorData.zipCode) {
            if (!doctorInfo.address) doctorInfo.address = {};
            doctorInfo.address.zipCode = doctorData.zipCode;
          } else {
            if (!doctorInfo.address) doctorInfo.address = {};
            doctorInfo.address.zipCode = '...';
          }

          // Other professional information
          if (doctorData.hospital) doctorInfo.hospital = doctorData.hospital;
          if (doctorData.education) doctorInfo.education = doctorData.education;
          if (doctorData.experience) doctorInfo.experience = doctorData.experience;
          if (doctorData.languages && doctorData.languages.length > 0) doctorInfo.languages = doctorData.languages;
          if (doctorData.bio) doctorInfo.bio = doctorData.bio;

          detailedDoctors.push(doctorInfo);
        }
      }

      return detailedDoctors;
    } catch (error) {
      console.error('Error fetching detailed doctor information:', error);
      throw error;
    }
  };

  const fetchDoctors = async () => {
    try {
      setLoading(true);

      // First, get the linked doctor IDs from the API
      const linkedDoctors = await usersAPI.getLinkedUsers('doctors');

      if (!linkedDoctors || linkedDoctors.length === 0) {
        console.log('No linked doctors found');
        setDoctors([]);
        return;
      }

      // Extract doctor IDs
      const doctorIds = linkedDoctors.map(doctor => doctor.uid);

      // Log the doctor IDs for debugging
      console.log('Doctor IDs to fetch:', doctorIds);

      // Fetch detailed information for each doctor from Firebase
      const detailedDoctors = await fetchDoctorsFromFirebase(doctorIds);

      console.log(`Fetched ${detailedDoctors.length} doctors with detailed information`);
      console.log('Doctor details:', JSON.stringify(detailedDoctors, null, 2));

      // Log the raw data from Firebase for debugging
      for (const doctorId of doctorIds) {
        try {
          const doctorDoc = doc(db, 'users', doctorId);
          const doctorSnapshot = await getDoc(doctorDoc);
          if (doctorSnapshot.exists()) {
            console.log(`Raw Firebase data for doctor ${doctorId}:`, JSON.stringify(doctorSnapshot.data(), null, 2));
          }
        } catch (error) {
          console.error(`Error fetching raw data for doctor ${doctorId}:`, error);
        }
      }
      setDoctors(detailedDoctors);
    } catch (error) {
      console.error('Error fetching doctors:', error);
      showMessage({
        message: 'Error',
        description: 'Failed to load your doctors. Please try again.',
        type: 'danger',
      });
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleRefresh = () => {
    setRefreshing(true);
    fetchDoctors();
  };

  const renderEmptyList = () => (
    <div className="empty-container">
      <Ionicons name="medical" size={60} color="#ccc" className="empty-icon" />
      <h3 className="empty-text">No doctors following you yet</h3>
      <p className="empty-subtext">
        When doctors add you as their patient, they will appear here
      </p>
    </div>
  );

  // Handle request appointment button press
  const handleRequestAppointment = (doctor) => {
    // Navigate to Appointments screen with parameter to open request modal
    navigation.navigate('Appointments', {
      openRequestModal: true,
      selectedDoctorId: doctor.uid
    });
  };

  // Handle start video call button press
  const handleStartVideoCall = (doctor) => {
    // Generate a unique room name
    const roomName = `neurocare_${doctor.uid}_${Date.now()}`;

    // Navigate to PatientVideoCall screen
    navigation.navigate('PatientVideoCall', {
      roomName: roomName,
      doctorInfo: doctor
    });
  };

  return (
    <div className="container">
      {loading && !refreshing ? (
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p className="loading-text">Loading your doctors...</p>
        </div>
      ) : (
        <div className={doctors.length === 0 ? "empty-list-content" : "list-content"}>
          {doctors.length === 0 ? (
            renderEmptyList()
          ) : (
            <div className="doctors-list">
              {doctors.map((doctor) => (
                <DoctorCard
                  key={doctor.uid}
                  doctor={doctor}
                  onRequestAppointment={handleRequestAppointment}
                  onStartVideoCall={handleStartVideoCall}
                />
              ))}
            </div>
          )}
          {refreshing && (
            <div className="refresh-indicator">
              <div className="loading-spinner"></div>
              <p>Refreshing...</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default MyDoctorsScreen;
