import React, { useState, useEffect } from 'react';
import { IoCalendarOutline, IoHappyOutline, IoDocumentTextOutline, IoChevronForward, IoClose, IoCalendar, IoHappy, IoFitness } from 'react-icons/io5';
import '../../../../styles/dashboards/doctor/doctor_patient_detail/patientSymptomsViewer.css';
import { ROLE_COLORS } from '../../../../config/theme';
import { firebaseDoctorPatientsService } from '../../../../services/firebaseDoctorPatientsService';

const PatientSymptomsViewer = ({ patientId, patientName }) => {
  const [loading, setLoading] = useState(true);
  const [symptoms, setSymptoms] = useState([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedSymptomLog, setSelectedSymptomLog] = useState(null);
  const [timeRange, setTimeRange] = useState('week');
  const doctorColors = ROLE_COLORS.doctor;

  useEffect(() => {
    fetchPatientSymptoms();
  }, [patientId]);

  const fetchPatientSymptoms = async () => {
    if (!patientId) return;

    setLoading(true);
    try {
      // Fetch symptoms for the selected patient from Firebase
      const symptomsData = await firebaseDoctorPatientsService.getPatientSymptoms(patientId);
      console.log(`Fetched ${symptomsData.length} symptom logs for patient ${patientId}`);
      setSymptoms(symptomsData || []);
    } catch (error) {
      console.error('Error fetching patient symptoms from Firebase:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleTimeRangeChange = (range) => {
    setTimeRange(range);
  };

  const openSymptomDetails = (symptomLog) => {
    setSelectedSymptomLog(symptomLog);
    setModalVisible(true);
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return 'Unknown date';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Get severity color
  const getSeverityColor = (severity) => {
    if (severity >= 8) return '#D32F2F'; // High severity - red
    if (severity >= 5) return '#FFA000'; // Medium severity - amber
    return '#388E3C'; // Low severity - green
  };

  // Get severity text
  const getSeverityText = (severity) => {
    if (severity >= 8) return 'High';
    if (severity >= 5) return 'Medium';
    return 'Low';
  };

  // Filter symptoms by time range
  const getFilteredSymptoms = () => {
    const now = new Date();
    const cutoffDate = new Date();

    if (timeRange === 'week') {
      cutoffDate.setDate(now.getDate() - 7);
    } else if (timeRange === 'month') {
      cutoffDate.setDate(now.getDate() - 30);
    } else {
      cutoffDate.setDate(now.getDate() - 90);
    }

    return symptoms.filter(log => new Date(log.timestamp) >= cutoffDate)
      .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
  };

  // Render the symptoms list
  const renderSymptomsList = () => {
    const filteredSymptoms = getFilteredSymptoms();

    if (filteredSymptoms.length === 0) {
      return (
        <div className="emptyContainer">
          <IoDocumentTextOutline size={48} color={doctorColors.primary} />
          <p className="emptyText">No symptom logs found for this time period</p>
        </div>
      );
    }

    return (
      <div>
        {filteredSymptoms.map((item) => (
          <div
            key={item.id}
            className="symptomCard"
            onClick={() => openSymptomDetails(item)}
          >
            <div className="symptomHeader">
              <div className="dateContainer">
                <IoCalendarOutline size={16} color="#666" />
                <span className="dateText">{formatDate(item.timestamp)}</span>
              </div>
              <div className="moodContainer">
                <IoHappyOutline size={16} color="#666" />
                <span className="moodText">{item.mood}</span>
              </div>
            </div>

            <div className="symptomsContainer">
              {item.symptoms.slice(0, 2).map((symptom, index) => (
                <div key={index} className="symptomItem">
                  <div className="symptomNameContainer">
                    <span className="symptomName">{symptom.type}</span>
                    <div className="severityBadge" style={{ backgroundColor: getSeverityColor(symptom.severity) }}>
                      <span className="severityText">{getSeverityText(symptom.severity)}</span>
                    </div>
                  </div>
                  <p className="symptomDescription">
                    {symptom.description}
                  </p>
                </div>
              ))}

              {item.symptoms.length > 2 && (
                <p className="moreSymptoms">
                  +{item.symptoms.length - 2} more symptoms
                </p>
              )}
            </div>

            {item.notes && (
              <p className="notes">
                Notes: {item.notes}
              </p>
            )}

            <IoChevronForward size={24} color="#ccc" className="chevron" />
          </div>
        ))}
      </div>
    );
  };

  // Render the detail modal
  const renderDetailModal = () => {
    if (!selectedSymptomLog || !modalVisible) return null;

    return (
      <div className="modalOverlay">
        <div className="modalContainer">
          <div className="modalHeader">
            <h3 className="modalTitle">Symptom Log Details</h3>
            <button
              className="closeButton"
              onClick={() => setModalVisible(false)}
            >
              <IoClose size={24} color="#333" />
            </button>
          </div>

          <div className="modalContent">
            <div className="modalDateContainer">
              <IoCalendar size={20} color={doctorColors.primary} />
              <span className="modalDateText">
                {formatDate(selectedSymptomLog.timestamp)}
              </span>
            </div>

            <div className="modalSection">
              <h4 className="modalSectionTitle">Mood</h4>
              <div className="modalMoodContainer">
                <IoHappy size={24} color="#666" />
                <span className="modalMoodText">{selectedSymptomLog.mood}</span>
              </div>
            </div>

            <div className="modalSection">
              <h4 className="modalSectionTitle">Symptoms</h4>
              {selectedSymptomLog.symptoms.map((symptom, index) => (
                <div key={index} className="modalSymptomItem">
                  <div className="modalSymptomHeader">
                    <span className="modalSymptomName">{symptom.type}</span>
                    <div className="modalSeverityBadge" style={{ backgroundColor: getSeverityColor(symptom.severity) }}>
                      <span className="modalSeverityText">
                        Severity: {symptom.severity}/10
                      </span>
                    </div>
                  </div>
                  <p className="modalSymptomDescription">
                    {symptom.description}
                  </p>
                </div>
              ))}
            </div>

            {selectedSymptomLog.activities && selectedSymptomLog.activities.length > 0 && (
              <div className="modalSection">
                <h4 className="modalSectionTitle">Activities</h4>
                <div className="modalActivitiesContainer">
                  {selectedSymptomLog.activities.map((activity, index) => (
                    <div key={index} className="modalActivityItem">
                      <IoFitness size={16} color="#666" />
                      <span className="modalActivityText">{activity}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {selectedSymptomLog.notes && (
              <div className="modalSection">
                <h4 className="modalSectionTitle">Notes</h4>
                <p className="modalNotesText">{selectedSymptomLog.notes}</p>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="container">
      <div className="header">
        <h2 className="headerTitle">
          {patientName ? `${patientName}'s Symptoms` : 'Patient Symptoms'}
        </h2>
        <p className="headerSubtitle">
          Track your patient's reported symptoms
        </p>
      </div>

      {/* Time Range Selector */}
      <div className="timeRangeContainer">
        <span className="timeRangeLabel">Time Range:</span>
        <div className="timeRangeButtons">
          <button
            className={`timeRangeButton ${timeRange === 'week' ? 'selectedTimeRange' : ''}`}
            onClick={() => handleTimeRangeChange('week')}
          >
            <span className={`timeRangeText ${timeRange === 'week' ? 'selectedTimeRangeText' : ''}`}>
              Week
            </span>
          </button>

          <button
            className={`timeRangeButton ${timeRange === 'month' ? 'selectedTimeRange' : ''}`}
            onClick={() => handleTimeRangeChange('month')}
          >
            <span className={`timeRangeText ${timeRange === 'month' ? 'selectedTimeRangeText' : ''}`}>
              Month
            </span>
          </button>

          <button
            className={`timeRangeButton ${timeRange === 'quarter' ? 'selectedTimeRange' : ''}`}
            onClick={() => handleTimeRangeChange('quarter')}
          >
            <span className={`timeRangeText ${timeRange === 'quarter' ? 'selectedTimeRangeText' : ''}`}>
              3 Months
            </span>
          </button>
        </div>
      </div>

      {loading ? (
        <div className="loadingContainer">
          <div className="spinner" style={{ borderTopColor: doctorColors.primary }}></div>
          <p className="loadingText">Loading patient symptoms...</p>
        </div>
      ) : (
        <div className="content">
          {/* Symptoms List */}
          <div className="section">
            <h3 className="sectionTitle">Symptom Logs</h3>
            {renderSymptomsList()}
          </div>
        </div>
      )}

      {renderDetailModal()}
    </div>
  );
};

export default PatientSymptomsViewer;
