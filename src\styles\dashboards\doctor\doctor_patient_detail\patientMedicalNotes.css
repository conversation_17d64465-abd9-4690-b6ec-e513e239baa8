/* Patient Medical Notes CSS for Web */
.container {
  display: flex;
  flex-direction: column;
  background-color: #f8f9fa;
  height: 100%;
}

.header {
  background-color: #fff;
  padding: 16px;
  border-bottom: 1px solid #eaeaea;
}

.headerTitle {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.headerSubtitle {
  font-size: 14px;
  color: #666;
  margin-top: 4px;
}

.content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.noteCard {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 10px;
  border: 1px solid #eaeaea;
  position: relative;
  cursor: pointer;
}

.noteHeader {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.categoryContainer {
  display: flex;
  align-items: center;
}

.categoryText {
  font-size: 14px;
  font-weight: bold;
  color: var(--doctor-primary-color);
  margin-left: 4px;
}

.dateText {
  font-size: 12px;
  color: #666;
}

.noteContent {
  font-size: 14px;
  color: #333;
  margin-right: 20px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.chevron {
  position: absolute;
  right: 16px;
  top: 50%;
  margin-top: -12px;
  color: #ccc;
}

.emptyContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px;
}

.emptyText {
  font-size: 14px;
  color: #666;
  text-align: center;
  margin-top: 8px;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 24px;
  flex: 1;
}

.loadingText {
  font-size: 14px;
  color: #666;
  margin-top: 8px;
}

/* Modal styles */
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.4);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modalContainer {
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #eaeaea;
  background-color: #fff;
}

.modalTitle {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.closeButton {
  padding: 6px;
  cursor: pointer;
}

.modalContent {
  padding: 16px;
  max-height: calc(80vh - 60px);
  overflow-y: auto;
}

.modalSection {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
}

.modalSectionTitle {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
}

.modalDateText {
  font-size: 14px;
  color: #666;
}

.modalContentText {
  font-size: 16px;
  color: #333;
  line-height: 24px;
}

.modalMetadata {
  padding: 8px;
}

.modalMetadataText {
  font-size: 12px;
  color: #999;
  font-style: italic;
}
