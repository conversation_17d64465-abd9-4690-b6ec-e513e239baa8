Stack trace:
Frame         Function      Args
0007FFFFBBB0  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFFAAB0) msys-2.0.dll+0x2118E
0007FFFFBBB0  0002100469BA (000000000000, 000000000000, 000000000000, 0007FFFFBE88) msys-2.0.dll+0x69BA
0007FFFFBBB0  0002100469F2 (00021028DF99, 0007FFFFBA68, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFFBBB0  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFBBB0  00021006A545 (0007FFFFBBC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0007FFFFBE90  00021006B9A5 (0007FFFFBBC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFE3CEC0000 ntdll.dll
7FFE3BA40000 KERNEL32.DLL
7FFE3A8C0000 KERNELBASE.dll
7FFE3CB10000 USER32.dll
000210040000 msys-2.0.dll
7FFE3AC90000 win32u.dll
7FFE3CD70000 GDI32.dll
7FFE3A550000 gdi32full.dll
7FFE3A4A0000 msvcp_win.dll
7FFE3A2B0000 ucrtbase.dll
7FFE3C250000 advapi32.dll
7FFE3C4C0000 msvcrt.dll
7FFE3B590000 sechost.dll
7FFE3B730000 RPCRT4.dll
7FFE395A0000 CRYPTBASE.DLL
7FFE3A400000 bcryptPrimitives.dll
7FFE3CAD0000 IMM32.DLL
