/* AdminDashboard.css */

.admin-dashboard {
  background-color: #f5f7fa;
  height: 100%;
  overflow-y: auto;
}

.refresh-control {
  display: flex;
  justify-content: flex-end;
  padding: 10px 20px;
}

.refresh-button {
  background-color: #4285F4;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
}

.refresh-button:hover {
  background-color: #3367d6;
}

.refresh-spinner {
  width: 24px;
  height: 24px;
  border: 3px solid rgba(66, 133, 244, 0.3);
  border-radius: 50%;
  border-top-color: #4285F4;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.header-section {
  width: 100%;
  margin-bottom: 20px;
}

.header-gradient {
  padding: 25px 20px;
  border-bottom-left-radius: 20px;
  border-bottom-right-radius: 20px;
  background: linear-gradient(to right, #4285F4, #f5f5f5);
}

.greeting {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.greeting-text {
  font-size: 24px;
  font-weight: bold;
  color: white;
  margin-bottom: 5px;
}

.date-text {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
  margin-top: 4px;
}

.content-section {
  padding: 0 16px 20px;
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 16px;
  color: #212121;
}

.cards-container {
  margin-bottom: 24px;
}

.card-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
}

.chart-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
}

.half-chart {
  width: 48%;
}

.quick-actions {
  margin: 16px 0;
}

.action-buttons {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}

.action-button {
  width: 30%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 16px;
  cursor: pointer;
  transition: transform 0.2s;
}

.action-button:hover {
  transform: translateY(-3px);
}

.action-icon {
  width: 60px;
  height: 60px;
  border-radius: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 8px;
}

.action-text {
  font-size: 12px;
  color: #424242;
  text-align: center;
  margin: 0;
}
