import React, { createContext, useState, useContext, useEffect } from 'react';
import { useAuth } from './AuthContext';
import { firebaseMedicationsService } from '../services/firebaseMedicationsService';
import { localMedicationsService } from '../services/localStorageService';
// Replace React Native Alert with a custom function for web
// Replace AsyncStorage with browser's localStorage

// Create context
const MedicationContext = createContext();

// Custom hook to use the medication context
export const useMedications = () => {
  const context = useContext(MedicationContext);
  if (!context) {
    throw new Error('useMedications must be used within a MedicationProvider');
  }
  return context;
};

// Alert function for web (replaces React Native Alert)
const showAlert = (title, message, buttons) => {
  // Simple version with browser's native confirm/alert
  if (buttons && buttons.length > 1) {
    // If there are multiple buttons, use confirm
    if (window.confirm(`${title}\n\n${message}`)) {
      // If the user clicks OK, execute the action of the first non-OK button
      const okButton = buttons.find(btn => btn.text !== "OK");
      if (okButton && okButton.onPress) {
        okButton.onPress();
      }
    } else {
      // If the user clicks Cancel, do nothing or execute the action of the OK button
      const okButton = buttons.find(btn => btn.text === "OK");
      if (okButton && okButton.onPress) {
        okButton.onPress();
      }
    }
  } else {
    // If there's only one button, use alert
    window.alert(`${title}\n\n${message}`);
    // Execute the button action if it exists
    if (buttons && buttons.length > 0 && buttons[0].onPress) {
      buttons[0].onPress();
    }
  }
};

export const MedicationProvider = ({ children }) => {
  const [medications, setMedications] = useState([]);
  const [reminders, setReminders] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [useLocalStorage, setUseLocalStorage] = useState(false);
  const { user } = useAuth();

  // Fetch medications when user changes
  useEffect(() => {
    if (user) {
      fetchMedications();
      fetchReminders();
    } else {
      setMedications([]);
      setReminders([]);
    }
  }, [user]);

  // Show a one-time alert about Firebase permissions or indexes if needed
  useEffect(() => {
    if (error && !useLocalStorage) {
      if (error.includes('permissions')) {
        showAlert(
          "Storage Notice",
          "We're having trouble accessing the cloud database. Your medications will be stored locally on your device for now.",
          [
            {
              text: "OK",
              onPress: () => setUseLocalStorage(true)
            }
          ]
        );
      } else if (error.includes('requires an index')) {
        showAlert(
          "Database Setup Required",
          "The database needs additional setup. Please contact the administrator to set up the required indexes.",
          [
            {
              text: "Use Local Storage",
              onPress: () => setUseLocalStorage(true)
            },
            {
              text: "Try Again",
              onPress: () => {
                setError(null);
                fetchMedications();
                fetchReminders();
              }
            }
          ]
        );
      }
    }
  }, [error, useLocalStorage]);

  // Fetch all medications for the current user
  const fetchMedications = async () => {
    if (!user) return;

    setLoading(true);
    setError(null);

    // If we've already determined to use local storage, don't try Firebase
    if (useLocalStorage) {
      try {
        const localMeds = await localMedicationsService.getPatientMedications(user.uid);
        setMedications(localMeds);
      } catch (localErr) {
        console.error('Error fetching local medications:', localErr);
        setError('Failed to load medications from local storage.');
      } finally {
        setLoading(false);
      }
      return;
    }

    // Try Firebase first
    try {
      const medicationsData = await firebaseMedicationsService.getPatientMedications(user.uid);
      setMedications(medicationsData);
    } catch (err) {
      console.error('Error fetching medications:', err);

      // Check if it's a permissions or index error
      if (err.toString().includes('Missing or insufficient permissions')) {
        setError('Missing or insufficient permissions to access medications.');
      } else if (err.toString().includes('requires an index')) {
        setError('The database requires an index to be created for medications.');
      } else {
        setError('Failed to load medications. Please try again.');
      }

      // Fallback to local storage if Firebase fails
      try {
        const localMeds = await localMedicationsService.getPatientMedications(user.uid);
        setMedications(localMeds);
      } catch (localErr) {
        console.error('Error fetching local medications:', localErr);
      }
    } finally {
      setLoading(false);
    }
  };

  // Fetch all reminders for the current user
  const fetchReminders = async () => {
    if (!user) return;

    setLoading(true);
    setError(null);

    // If we've already determined to use local storage, don't try Firebase
    if (useLocalStorage) {
      try {
        const localReminders = await localMedicationsService.getPatientReminders(user.uid);
        setReminders(localReminders);
      } catch (localErr) {
        console.error('Error fetching local medication reminders:', localErr);
        setError('Failed to load medication reminders from local storage.');
      } finally {
        setLoading(false);
      }
      return;
    }

    // Try Firebase first
    try {
      const remindersData = await firebaseMedicationsService.getPatientReminders(user.uid);
      setReminders(remindersData);
    } catch (err) {
      console.error('Error fetching medication reminders:', err);

      // Check if it's a permissions or index error
      if (err.toString().includes('Missing or insufficient permissions')) {
        setError('Missing or insufficient permissions to access medication reminders.');
      } else if (err.toString().includes('requires an index')) {
        setError('The database requires an index to be created for medication reminders.');
      } else {
        setError('Failed to load medication reminders. Please try again.');
      }

      // Fallback to local storage if Firebase fails
      try {
        const localReminders = await localMedicationsService.getPatientReminders(user.uid);
        setReminders(localReminders);
      } catch (localErr) {
        console.error('Error fetching local medication reminders:', localErr);
      }
    } finally {
      setLoading(false);
    }
  };

  // Add a new medication
  const addMedication = async (medicationData) => {
    setLoading(true);
    setError(null);

    // If we've already determined to use local storage, don't try Firebase
    if (useLocalStorage) {
      try {
        const localSavedMed = await localMedicationsService.saveMedication({
          ...medicationData,
          patientId: user.uid
        });

        setMedications(prev => [localSavedMed, ...prev]);
        return localSavedMed;
      } catch (localErr) {
        console.error('Error adding local medication:', localErr);
        setError('Failed to add medication to local storage.');
        throw localErr;
      } finally {
        setLoading(false);
      }
      return;
    }

    // Try Firebase first
    try {
      // Save to Firebase
      const savedMedication = await firebaseMedicationsService.saveMedication({
        ...medicationData,
        patientId: user.uid
      });

      // Update state
      setMedications(prev => [savedMedication, ...prev]);

      return savedMedication;
    } catch (err) {
      console.error('Error adding medication:', err);

      // Check if it's a permissions or index error
      if (err.toString().includes('Missing or insufficient permissions')) {
        setError('Missing or insufficient permissions to save medication.');
        setUseLocalStorage(true); // Switch to local storage for future operations
      } else if (err.toString().includes('requires an index')) {
        setError('The database requires an index to be created for medications.');
      } else {
        setError('Failed to add medication. Please try again.');
      }

      // Fallback to local storage if Firebase fails
      try {
        const localSavedMed = await localMedicationsService.saveMedication({
          ...medicationData,
          patientId: user.uid
        });

        setMedications(prev => [localSavedMed, ...prev]);
        return localSavedMed;
      } catch (localErr) {
        console.error('Error adding local medication:', localErr);
        throw localErr;
      }
    } finally {
      setLoading(false);
    }
  };

  // Update an existing medication
  const updateMedication = async (medicationId, updateData) => {
    setLoading(true);
    setError(null);

    try {
      // Update in Firebase
      const updatedMedication = await firebaseMedicationsService.updateMedication(
        medicationId,
        updateData
      );

      // Update state
      if (updatedMedication) {
        setMedications(prev =>
          prev.map(med => med.id === medicationId ? updatedMedication : med)
        );
      }

      return updatedMedication;
    } catch (err) {
      console.error('Error updating medication:', err);
      setError('Failed to update medication. Please try again.');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Delete a medication
  const deleteMedication = async (medicationId) => {
    setLoading(true);
    setError(null);

    try {
      // Delete from Firebase
      const success = await firebaseMedicationsService.deleteMedication(medicationId);

      // Update state if successful
      if (success) {
        setMedications(prev => prev.filter(med => med.id !== medicationId));

        // Also remove any reminders for this medication
        setReminders(prev => prev.filter(rem => rem.medicationId !== medicationId));
      }

      return success;
    } catch (err) {
      console.error('Error deleting medication:', err);
      setError('Failed to delete medication. Please try again.');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Add a medication reminder
  const addReminder = async (reminderData) => {
    setLoading(true);
    setError(null);

    // If we've already determined to use local storage, don't try Firebase
    if (useLocalStorage) {
      try {
        const localSavedReminder = await localMedicationsService.saveReminder({
          ...reminderData,
          patientId: user.uid
        });

        setReminders(prev => [localSavedReminder, ...prev]);
        return localSavedReminder;
      } catch (localErr) {
        console.error('Error adding local medication reminder:', localErr);
        setError('Failed to add medication reminder to local storage.');
        throw localErr;
      } finally {
        setLoading(false);
      }
      return;
    }

    // Try Firebase first
    try {
      // Save to Firebase
      const savedReminder = await firebaseMedicationsService.saveReminder({
        ...reminderData,
        patientId: user.uid
      });

      // Update state
      setReminders(prev => [savedReminder, ...prev]);

      return savedReminder;
    } catch (err) {
      console.error('Error adding medication reminder:', err);

      // Check if it's a permissions or index error
      if (err.toString().includes('Missing or insufficient permissions')) {
        setError('Missing or insufficient permissions to save medication reminder.');
        setUseLocalStorage(true); // Switch to local storage for future operations
      } else if (err.toString().includes('requires an index')) {
        setError('The database requires an index to be created for medication reminders.');
      } else {
        setError('Failed to add medication reminder. Please try again.');
      }

      // Fallback to local storage if Firebase fails
      try {
        const localSavedReminder = await localMedicationsService.saveReminder({
          ...reminderData,
          patientId: user.uid
        });

        setReminders(prev => [localSavedReminder, ...prev]);
        return localSavedReminder;
      } catch (localErr) {
        console.error('Error adding local medication reminder:', localErr);
        throw localErr;
      }
    } finally {
      setLoading(false);
    }
  };

  // Update a reminder status
  const updateReminderStatus = async (reminderId, status) => {
    setLoading(true);
    setError(null);

    try {
      // Update in Firebase
      const updatedReminder = await firebaseMedicationsService.updateReminderStatus(
        reminderId,
        status
      );

      // Update state
      if (updatedReminder) {
        setReminders(prev =>
          prev.map(rem => rem.id === reminderId ? updatedReminder : rem)
        );
      }

      return updatedReminder;
    } catch (err) {
      console.error('Error updating reminder status:', err);
      setError('Failed to update reminder status. Please try again.');

      // Fallback to local storage if Firebase fails
      try {
        const localUpdatedReminder = await localMedicationsService.updateReminderStatus(
          reminderId,
          status
        );

        if (localUpdatedReminder) {
          setReminders(prev =>
            prev.map(rem => rem.id === reminderId ? localUpdatedReminder : rem)
          );
        }

        return localUpdatedReminder;
      } catch (localErr) {
        console.error('Error updating local reminder status:', localErr);
        throw localErr;
      }
    } finally {
      setLoading(false);
    }
  };

  // Create reminders for a medication based on its frequency settings
  const createRemindersForMedication = async (medication) => {
    try {
      const now = new Date();
      const dayNames = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
      const createdReminders = [];

      // Ensure medication has all required properties
      if (!medication || !medication.id || !medication.frequencyTimes) {
        console.error('Invalid medication data for creating reminders:', medication);
        return [];
      }

      // Create reminders for the next 30 days
      for (let i = 0; i < 30; i++) {
        const date = new Date(now);
        date.setDate(date.getDate() + i);

        const dayName = dayNames[date.getDay()];

        // Skip if this day is not selected for specific-days frequency
        if (medication.frequencyType === 'specific-days' &&
            medication.frequencyDays &&
            !medication.frequencyDays[dayName]) {
          continue;
        }

        // Create a reminder for each time slot
        for (const timeSlot of medication.frequencyTimes) {
          // Skip invalid time slots
          if (typeof timeSlot.hour !== 'number' || typeof timeSlot.minute !== 'number') {
            continue;
          }

          const reminderTime = new Date(date);
          reminderTime.setHours(timeSlot.hour, timeSlot.minute, 0, 0);

          // Skip if the time is in the past
          if (reminderTime < now) {
            continue;
          }

          const reminder = {
            patientId: medication.patientId,
            medicationId: medication.id,
            medicationName: medication.name || 'Medication',
            dosage: medication.dosage || '',
            instructions: medication.instructions || '',
            scheduledTime: reminderTime.toISOString(),
            status: 'scheduled',
          };

          try {
            const savedReminder = await addReminder(reminder);
            if (savedReminder) {
              createdReminders.push(savedReminder);
            }
          } catch (reminderError) {
            console.error('Error saving individual reminder:', reminderError);
            // Continue with other reminders even if one fails
          }
        }
      }

      return createdReminders;
    } catch (error) {
      console.error('Error creating reminders for medication:', error);
      return []; // Return empty array instead of throwing to prevent cascading errors
    }
  };

  // Migrate local data to Firebase (useful for existing users)
  const migrateLocalDataToFirebase = async () => {
    if (!user) return;

    // Skip migration if we're already using local storage due to permissions issues
    if (useLocalStorage) return false;

    // Check if we've already migrated data (to avoid duplicates)
    const migrationKey = `neurocare:migration_completed_${user.uid}`;
    try {
      const migrationCompleted = localStorage.getItem(migrationKey);
      if (migrationCompleted === 'true') {
        console.log('Migration already completed for this user, skipping');
        return true;
      }
    } catch (err) {
      console.error('Error checking migration status:', err);
    }

    setLoading(true);
    setError(null);

    try {
      // First check if there are already medications in Firebase
      const existingMeds = await firebaseMedicationsService.getPatientMedications(user.uid);

      // If there are already medications in Firebase, don't migrate
      if (existingMeds && existingMeds.length > 0) {
        console.log('User already has medications in Firebase, skipping migration');
        localStorage.setItem(migrationKey, 'true');
        return true;
      }

      // Get local medications
      const localMeds = await localMedicationsService.getPatientMedications(user.uid);

      // Only migrate if there are local medications
      if (localMeds && localMeds.length > 0) {
        console.log(`Migrating ${localMeds.length} local medications to Firebase`);

        // Save each medication to Firebase
        for (const med of localMeds) {
          // Skip the id as Firebase will generate a new one
          const { id, ...medData } = med;

          await firebaseMedicationsService.saveMedication({
            ...medData,
            patientId: user.uid
          });
        }

        // Get local reminders
        const localRems = await localMedicationsService.getPatientReminders(user.uid);

        // Save each reminder to Firebase
        for (const rem of localRems) {
          // Skip the id as Firebase will generate a new one
          const { id, ...remData } = rem;

          await firebaseMedicationsService.saveReminder({
            ...remData,
            patientId: user.uid
          });
        }

        // Mark migration as completed
        localStorage.setItem(migrationKey, 'true');
      } else {
        console.log('No local medications to migrate');
      }

      // Refresh data from Firebase
      await fetchMedications();
      await fetchReminders();

      return true;
    } catch (err) {
      console.error('Error migrating local data to Firebase:', err);
      setError('Failed to migrate local data to Firebase. Please try again.');
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Context value
  const value = {
    medications,
    reminders,
    loading,
    error,
    useLocalStorage,
    fetchMedications,
    fetchReminders,
    addMedication,
    updateMedication,
    deleteMedication,
    addReminder,
    updateReminderStatus,
    createRemindersForMedication,
    migrateLocalDataToFirebase,
    setUseLocalStorage
  };

  return (
    <MedicationContext.Provider value={value}>
      {children}
    </MedicationContext.Provider>
  );
};

export default MedicationContext;