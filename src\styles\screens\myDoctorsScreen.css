.container {
  flex: 1;
  background-color: #f5f5f5;
  padding-top: 10px;
  min-height: 100vh;
}

.list-content {
  padding: 16px;
  padding-top: 8px;
}

.empty-list-content {
  flex-grow: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16px;
  min-height: 80vh;
}

.doctors-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.doctor-card {
  background-color: #fff;
  border-radius: 16px;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  border: 1px solid #f0f0f0;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.doctor-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 12px rgba(0, 0, 0, 0.2);
}

.card-content {
  padding: 0;
  cursor: pointer;
}

.card-header {
  display: flex;
  padding: 18px;
  align-items: center;
  position: relative;
  border-bottom: 1px solid #f5f5f5;
  background-color: #fcfcfc;
}

.avatar-container {
  margin-right: 16px;
  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);
}

.avatar {
  width: 75px;
  height: 75px;
  border-radius: 50%;
  border: 2px solid #fff;
  object-fit: cover;
}

.avatar-placeholder {
  width: 75px;
  height: 75px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 2px solid #fff;
}

.avatar-text {
  font-size: 26px;
  font-weight: bold;
  color: #fff;
}

.doctor-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.doctor-name {
  font-size: 19px;
  font-weight: bold;
  margin-bottom: 5px;
  color: #333;
  letter-spacing: 0.3px;
  margin: 0 0 5px 0;
}

.doctor-specialty {
  font-size: 15px;
  color: #28a745;
  margin-bottom: 8px;
  font-style: italic;
  opacity: 0.9;
  margin: 0 0 8px 0;
}

.expand-icon {
  position: absolute;
  right: 16px;
  top: 16px;
}

.contact-info {
  margin-top: 8px;
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 6px 10px;
  align-self: flex-start;
}

.contact-item {
  display: flex;
  align-items: center;
  margin-bottom: 2px;
}

.contact-text {
  font-size: 14px;
  color: #555;
  margin-left: 8px;
  font-weight: 500;
}

.details-container {
  padding: 18px;
  padding-top: 10px;
  background-color: #f9f9f9;
  border-top: 1px solid #eee;
}

.detail-section {
  margin-bottom: 16px;
  background-color: #fff;
  border-radius: 10px;
  padding: 12px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.detail-title-container {
  display: flex;
  align-items: center;
  margin-bottom: 6px;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 6px;
}

.detail-title {
  font-size: 15px;
  font-weight: bold;
  color: #28a745;
  margin-left: 8px;
  letter-spacing: 0.3px;
}

.detail-text {
  font-size: 15px;
  color: #444;
  line-height: 22px;
  padding: 2px 4px 0;
  margin: 0;
}

.no-details-container {
  padding: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  border-radius: 10px;
  margin-top: 5px;
}

.no-details-text {
  font-size: 15px;
  color: #999;
  font-style: italic;
  text-align: center;
  margin: 0;
}

.actions-container {
  display: flex;
  flex-direction: column;
  border-top: 1px solid #eee;
  padding: 14px;
  background-color: #fff;
}

.secondary-actions-row {
  display: flex;
  justify-content: space-between;
  margin-top: 10px;
  gap: 8px;
}

.action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px 14px;
  background-color: #f8f8f8;
  border-radius: 8px;
  border: none;
  cursor: pointer;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.action-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.secondary-action-button {
  flex: 1;
  background-color: #fff;
  border: 1px solid #007bff;
}

.secondary-action-button:hover {
  background-color: #f8f9fa;
}

.action-text {
  margin-left: 8px;
  color: #007bff;
  font-weight: 600;
  font-size: 15px;
}

.primary-action-button {
  background-color: #007bff;
  border: none;
  box-shadow: 0 2px 2px rgba(0, 0, 0, 0.1);
}

.primary-action-button:hover {
  background-color: #0056b3;
}

.primary-action-text {
  color: #fff;
  font-weight: 700;
}

.loading-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 12px;
  font-size: 16px;
  color: #666;
}

.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  text-align: center;
}

.empty-icon {
  margin-bottom: 16px;
}

.empty-text {
  font-size: 18px;
  font-weight: bold;
  color: #666;
  margin-bottom: 8px;
  margin: 0 0 8px 0;
}

.empty-subtext {
  font-size: 14px;
  color: #999;
  text-align: center;
  padding: 0 32px;
  margin: 0;
}

.refresh-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  color: #666;
}

.refresh-indicator .loading-spinner {
  width: 20px;
  height: 20px;
  border-width: 2px;
  margin-bottom: 8px;
}

.icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* Responsive design */
@media (max-width: 768px) {
  .list-content {
    padding: 8px;
  }
  
  .card-header {
    padding: 12px;
  }
  
  .avatar, .avatar-placeholder {
    width: 60px;
    height: 60px;
  }
  
  .avatar-text {
    font-size: 20px;
  }
  
  .doctor-name {
    font-size: 17px;
  }
  
  .secondary-actions-row {
    flex-direction: column;
    gap: 8px;
  }
  
  .secondary-action-button {
    flex: none;
  }
}
