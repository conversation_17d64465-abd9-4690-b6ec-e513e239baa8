import React, { useEffect, useRef, useContext, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import axios from 'axios';
import { API_URL } from '../../../config/constants';
import { AuthContext } from '../../../contexts/AuthContext';
import '../../styles/common/notificationHandler.css';

/**
 * Web version of the NotificationHandler component
 * Handles web notifications and Firebase Cloud Messaging for web
 */
const NotificationHandler = () => {
  const { user, accessToken } = useContext(AuthContext);
  const navigate = useNavigate();
  const location = useLocation();
  const [notification, setNotification] = useState(null);
  const notificationTimeoutRef = useRef(null);

  useEffect(() => {
    // Don't proceed if navigation or user context isn't ready
    if (!navigate || !user || !accessToken) return;

    // Setup web notifications
    const setupNotifications = async () => {
      try {
        // Check if browser supports notifications
        if (!('Notification' in window)) {
          console.log('This browser does not support desktop notifications');
          return;
        }

        // Request permission for notifications
        let permission = Notification.permission;
        
        if (permission !== 'granted' && permission !== 'denied') {
          permission = await Notification.requestPermission();
        }
        
        if (permission !== 'granted') {
          console.log('Notification permission not granted');
          return;
        }

        // Initialize Firebase messaging for web if available
        if (typeof firebase !== 'undefined' && firebase.messaging) {
          try {
            const messaging = firebase.messaging();
            
            // Get token
            const token = await messaging.getToken();
            
            if (token) {
              // Register the token with our server
              try {
                await axios.post(
                  `${API_URL}/users/fcm-token`,
                  { fcmToken: token },
                  {
                    headers: {
                      'Content-Type': 'application/json',
                      Authorization: `Bearer ${accessToken}`,
                    },
                  }
                );
                console.log('FCM token updated successfully');
              } catch (error) {
                console.error('Error updating FCM token:', error);
              }
              
              // Handle foreground messages
              messaging.onMessage((payload) => {
                console.log('Message received in foreground:', payload);
                const { type, appointmentId, status } = payload.data || {};
                
                if (type === 'appointment_update') {
                  showNotification({
                    title: 'Appointment Update',
                    message: `Your appointment has been ${status}`,
                    appointmentId
                  });
                } else if (type === 'appointment_request') {
                  showNotification({
                    title: 'New Appointment Request',
                    message: 'A patient has requested an appointment',
                    appointmentId
                  });
                } else if (type === 'appointment_cancelled') {
                  showNotification({
                    title: 'Appointment Cancelled',
                    message: 'An appointment has been cancelled',
                    appointmentId
                  });
                }
              });
            }
          } catch (error) {
            console.log('Error setting up Firebase messaging for web:', error.message);
          }
        } else {
          console.log('Firebase messaging not available for web');
        }
      } catch (error) {
        console.log('Error setting up web notifications:', error.message);
      }
    };

    setupNotifications();

    // Clean up function
    return () => {
      if (notificationTimeoutRef.current) {
        clearTimeout(notificationTimeoutRef.current);
      }
    };
  }, [user, accessToken, navigate]);

  // Show in-app notification
  const showNotification = (notificationData) => {
    setNotification(notificationData);
    
    // Auto-dismiss after 5 seconds
    notificationTimeoutRef.current = setTimeout(() => {
      dismissNotification();
    }, 5000);
  };

  // Dismiss the notification
  const dismissNotification = () => {
    setNotification(null);
    if (notificationTimeoutRef.current) {
      clearTimeout(notificationTimeoutRef.current);
    }
  };

  // Handle navigation to appointment
  const handleAppointmentNavigation = (appointmentId) => {
    if (!appointmentId || !user) return;

    try {
      if (user.role === 'doctor') {
        navigate('/doctor/appointments', { state: { appointmentId } });
      } else if (user.role === 'patient') {
        navigate('/patient/appointments', { state: { appointmentId } });
      }
      dismissNotification();
    } catch (error) {
      console.log('Navigation error:', error.message);
    }
  };

  // Render the notification toast if there is a notification
  if (notification) {
    return (
      <div className="notification-toast">
        <div className="notification-header">
          <div className="notification-title">{notification.title}</div>
          <button className="notification-close" onClick={dismissNotification}>×</button>
        </div>
        <div className="notification-message">{notification.message}</div>
        <div className="notification-actions">
          {notification.appointmentId && (
            <button 
              className="notification-btn notification-btn-primary"
              onClick={() => handleAppointmentNavigation(notification.appointmentId)}
            >
              View
            </button>
          )}
          <button 
            className="notification-btn notification-btn-secondary"
            onClick={dismissNotification}
          >
            Dismiss
          </button>
        </div>
      </div>
    );
  }

  // Component doesn't render anything by default
  return null;
};

export default NotificationHandler;
