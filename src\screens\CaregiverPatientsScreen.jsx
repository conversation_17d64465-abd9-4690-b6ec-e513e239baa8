import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { 
  ChevronRightIcon, 
  UsersIcon, 
  ArrowPathIcon 
} from '@heroicons/react/24/outline';
import { useAuth } from '../contexts/AuthContext';
import { ROLE_COLORS } from '../config/theme';
import { getDoc, doc, collection, query, where, getDocs } from 'firebase/firestore';
import { db } from '../config/firebase';
import '../styles/screens/caregiverPatientsScreen.css';

const CaregiverPatientsScreen = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user } = useAuth();
  const caregiverColors = ROLE_COLORS.caregiver;

  // Check if we're in navigation mode
  const { navigateMode } = location.state || {};

  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [patients, setPatients] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    loadLinkedPatients();
  }, []);

  // Function to load patients linked to this caregiver
  const loadLinkedPatients = async () => {
    try {
      setLoading(true);

      // Get the current user's document to find linked patients
      const userDocRef = doc(db, 'users', user.uid);
      const userDocSnap = await getDoc(userDocRef);

      if (!userDocSnap.exists()) {
        throw new Error('User document not found');
      }

      const userData = userDocSnap.data();
      const linkedPatientIds = userData.linkedPatients || [];

      if (linkedPatientIds.length === 0) {
        // No linked patients
        setPatients([]);
        setLoading(false);
        return;
      }

      // Get user documents for all linked patients
      const usersCollection = collection(db, 'users');
      const patientDocs = [];

      for (const patientId of linkedPatientIds) {
        const patientDocRef = doc(usersCollection, patientId);
        const patientDocSnap = await getDoc(patientDocRef);

        if (patientDocSnap.exists()) {
          patientDocs.push({
            uid: patientDocSnap.id,
            ...patientDocSnap.data()
          });
        }
      }

      setPatients(patientDocs);
    } catch (error) {
      console.error('Error loading linked patients:', error);
      // Replace showMessage with a web-compatible notification
      alert('Failed to load patients. Please try again.');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleRefresh = () => {
    setRefreshing(true);
    loadLinkedPatients();
  };

  const handlePatientSelect = (patient) => {
    if (navigateMode) {
      // Navigate to the patient navigation screen
      navigate('/caregiver-patient-navigation', { state: { patient } });
    } else {
      // Navigate to the patient detail screen
      navigate('/caregiver-patient-detail', { state: { patient } });
    }
  };

  const handleSearchChange = (e) => {
    setSearchQuery(e.target.value);
  };

  // Filter patients based on search query
  const filteredPatients = patients.filter(patient => {
    const fullName = `${patient.firstName || ''} ${patient.lastName || ''}`.toLowerCase();
    return fullName.includes(searchQuery.toLowerCase());
  });

  const renderPatientItem = (patient) => {
    const initials = `${patient.firstName?.charAt(0) || ''}${patient.lastName?.charAt(0) || ''}`;
    const displayName = `${patient.firstName || ''} ${patient.lastName || ''}`.trim() || 'Unknown Patient';

    return (
      <div key={patient.uid} className="patient-card" onClick={() => handlePatientSelect(patient)}>
        <div className="patient-card-content">
          <div 
            className="patient-avatar"
            style={{ backgroundColor: caregiverColors.primary }}
          >
            {initials}
          </div>
          <div className="patient-info">
            <div className="patient-name">{displayName}</div>
            <div className="patient-email">{patient.email || 'No email'}</div>
            <div className="patient-details">
              {patient.condition || 'No condition specified'}
            </div>
          </div>
          <button
            className="view-button"
            onClick={(e) => {
              e.stopPropagation();
              handlePatientSelect(patient);
            }}
          >
            <ChevronRightIcon className="chevron-icon" style={{ color: caregiverColors.primary }} />
          </button>
        </div>
      </div>
    );
  };

  const renderEmptyList = () => (
    <div className="empty-container">
      <UsersIcon className="empty-icon" />
      <div className="empty-text">No patients found</div>
      <div className="empty-subtext">
        {navigateMode
          ? 'You need to have patients assigned to you before you can navigate them'
          : 'Patients assigned to you will appear here'
        }
      </div>
    </div>
  );

  return (
    <div className="container">
      <div className="header">
        <h1 className="header-title">
          {navigateMode ? 'Select Patient to Navigate' : 'My Patients'}
        </h1>
        <button
          className="refresh-button"
          onClick={handleRefresh}
          disabled={refreshing}
        >
          <ArrowPathIcon 
            className={`refresh-icon ${refreshing ? 'spinning' : ''}`}
            style={{ color: caregiverColors.primary }} 
          />
        </button>
      </div>

      <div className="search-container">
        <input
          type="text"
          placeholder={navigateMode ? "Search patient to navigate" : "Search patients"}
          value={searchQuery}
          onChange={handleSearchChange}
          className="search-input"
        />
      </div>

      {loading && !refreshing ? (
        <div className="loading-container">
          <div className="loading-spinner" style={{ borderTopColor: caregiverColors.primary }}></div>
          <div className="loading-text">Loading patients...</div>
        </div>
      ) : (
        <div className={`patients-list ${filteredPatients.length === 0 ? 'empty-list' : ''}`}>
          {filteredPatients.length === 0 ? (
            renderEmptyList()
          ) : (
            filteredPatients.map(renderPatientItem)
          )}
        </div>
      )}
    </div>
  );
};

export default CaregiverPatientsScreen;
