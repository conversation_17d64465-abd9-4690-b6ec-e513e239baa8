import React, { useState, useEffect, useRef } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faUser, faIdCard, faPhone, faMapMarkerAlt, 
  faChevronUp, faChevronDown, faCamera, faImage 
} from '@fortawesome/free-solid-svg-icons';
import axios from 'axios';
import '../../styles/profile/sharedProfileForm.css';

// Storage key for profile image
const PROFILE_IMAGE_STORAGE_KEY = 'neurocare:profile_image';

// Country codes data
const countries = [
  { code: 'AF', name: 'Afghanistan', dialCode: '+93' },
  { code: 'AL', name: 'Albania', dialCode: '+355' },
  { code: 'DZ', name: 'Algeria', dialCode: '+213' },
  // ... autres pays (à ajouter selon besoin)
  { code: 'US', name: 'United States', dialCode: '+1' },
  { code: 'GB', name: 'United Kingdom', dialCode: '+44' },
  { code: 'FR', name: 'France', dialCode: '+33' },
  { code: 'DE', name: 'Germany', dialCode: '+49' },
  { code: 'CA', name: 'Canada', dialCode: '+1' },
];

// Generate days, months, and years for date picker
const days = Array.from({ length: 31 }, (_, i) => i + 1);
const months = [
  { value: 1, label: 'January' },
  { value: 2, label: 'February' },
  { value: 3, label: 'March' },
  { value: 4, label: 'April' },
  { value: 5, label: 'May' },
  { value: 6, label: 'June' },
  { value: 7, label: 'July' },
  { value: 8, label: 'August' },
  { value: 9, label: 'September' },
  { value: 10, label: 'October' },
  { value: 11, label: 'November' },
  { value: 12, label: 'December' },
];
const currentYear = new Date().getFullYear();
const years = Array.from({ length: 100 }, (_, i) => currentYear - i);

// Form section definitions
const SECTIONS = {
  PROFILE: 'profile',
  PERSONAL: 'personal',
  CONTACT: 'contact',
  ADDRESS: 'address'
};

const SharedProfileForm = ({
  initialData = {},
  onSubmit,
  loading: isLoading = false,
  requiredFields = [],
  buttonText = 'Save',
  theme,
  nonEditableFields = []
}) => {
  // State to track open/closed sections
  const [expandedSections, setExpandedSections] = useState({
    [SECTIONS.PROFILE]: true,
    [SECTIONS.PERSONAL]: true,
    [SECTIONS.CONTACT]: true,
    [SECTIONS.ADDRESS]: true
  });

  // Form state
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    countryCode: 'US',
    dialCode: '+1',
    address: '',
    city: '',
    dateOfBirth: '',
    gender: '',
    ...initialData
  });

  const [cities, setCities] = useState([]);
  const [loadingCities, setLoadingCities] = useState(false);
  const [showDateModal, setShowDateModal] = useState(false);
  const [showCountryModal, setShowCountryModal] = useState(false);
  const [showImagePickerModal, setShowImagePickerModal] = useState(false);
  const [profileImage, setProfileImage] = useState(null);
  const [selectedImage, setSelectedImage] = useState(null);
  const [datePickerValues, setDatePickerValues] = useState({
    day: 1,
    month: 1,
    year: currentYear - 30
  });
  const [countrySearch, setCountrySearch] = useState('');

  // Filter countries based on search
  const filteredCountries = countrySearch.length > 0
    ? countries.filter(country =>
        country.name.toLowerCase().includes(countrySearch.toLowerCase()))
    : countries;

  // Function to toggle section expansion
  const toggleSection = (section) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  useEffect(() => {
    setFormData(prev => ({ ...prev, ...initialData }));

    // Parse date of birth if it exists
    if (initialData.dateOfBirth) {
      try {
        // Try to parse dates in MM/DD/YYYY format
        const parts = initialData.dateOfBirth.split('/');
        if (parts.length === 3) {
          setDatePickerValues({
            day: parseInt(parts[1], 10),
            month: parseInt(parts[0], 10),
            year: parseInt(parts[2], 10)
          });
        }
      } catch (error) {
        console.error('Error parsing date:', error);
      }
    }

    // Load profile image from local storage
    loadProfileImage();
  }, [initialData]);

  // Function to load profile image from local storage
  const loadProfileImage = () => {
    try {
      const savedImageUri = localStorage.getItem(PROFILE_IMAGE_STORAGE_KEY);
      if (savedImageUri) {
        setProfileImage(savedImageUri);
      }
    } catch (error) {
      console.error('Error loading profile image:', error);
    }
  };

  // Function to save profile image to local storage
  const saveProfileImage = (imageUri) => {
    try {
      // Save locally
      localStorage.setItem(PROFILE_IMAGE_STORAGE_KEY, imageUri);
      console.log('Image saved locally');
      return imageUri;
    } catch (error) {
      console.error('Error saving profile image:', error);
      alert('Unable to save profile image');
    }
  };

  // Function to handle image selection
  const handleImageSelection = (e) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      const reader = new FileReader();
      
      reader.onloadend = () => {
        setSelectedImage({
          uri: reader.result,
          id: Date.now()
        });
        setProfileImage(reader.result);
        saveProfileImage(reader.result);
        setShowImagePickerModal(false);
      };
      
      reader.readAsDataURL(file);
    }
  };

  const handleChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const formatPhoneNumber = (phoneNumber) => {
    // Remove all non-numeric characters
    const cleaned = phoneNumber.replace(/\D/g, '');

    // Apply different formatting based on country
    if (formData.countryCode === 'US') {
      // Format as (XXX) XXX-XXXX for US
      if (cleaned.length <= 3) {
        return cleaned;
      } else if (cleaned.length <= 6) {
        return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3)}`;
      } else {
        return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6, 10)}`;
      }
    } else {
      // Simple formatting for other countries - just group digits
      if (cleaned.length <= 3) {
        return cleaned;
      } else if (cleaned.length <= 7) {
        return `${cleaned.slice(0, 3)} ${cleaned.slice(3)}`;
      } else {
        return `${cleaned.slice(0, 3)} ${cleaned.slice(3, 7)} ${cleaned.slice(7, 11)}`;
      }
    }
  };

  const handlePhoneChange = (value) => {
    const formatted = formatPhoneNumber(value);
    handleChange('phone', formatted);
  };

  const handleDateSelection = () => {
    const { day, month, year } = datePickerValues;
    // Format as MM/DD/YYYY
    const formattedDate = `${month.toString().padStart(2, '0')}/${day.toString().padStart(2, '0')}/${year}`;
    handleChange('dateOfBirth', formattedDate);
    setShowDateModal(false);
  };

  const fetchCities = async (countryCode) => {
    setLoadingCities(true);
    try {
      const country = countries.find(c => c.code === countryCode);
      const response = await axios.post('https://countriesnow.space/api/v0.1/countries/cities', {
        country: country?.name
      });

      if (response.data && response.data.data) {
        setCities(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching cities:', error);
      alert('Failed to load cities');
    } finally {
      setLoadingCities(false);
    }
  };

  const handleCountryChange = (countryCode) => {
    const country = countries.find(c => c.code === countryCode);
    if (country) {
      setFormData(prev => ({
        ...prev,
        countryCode: country.code,
        dialCode: country.dialCode,
        city: '' // Reset city when country changes
      }));
      fetchCities(countryCode);
    }
  };

  const renderField = (label, field, placeholder) => (
    <div className="field-container">
      <label className="label">
        {label}
        {requiredFields.includes(field) && <span className="required">*</span>}
        {nonEditableFields.includes(field) && <span className="non-editable"> (Non-editable)</span>}
      </label>
      <input
        className={`input ${nonEditableFields.includes(field) ? 'input-disabled' : ''}`}
        value={formData[field] || ''}
        onChange={(e) => handleChange(field, e.target.value)}
        placeholder={placeholder}
        disabled={nonEditableFields.includes(field)}
      />
    </div>
  );

  const validateForm = () => {
    const errors = {};
    let isValid = true;

    // Check required fields
    requiredFields.forEach(field => {
      if (!formData[field] || formData[field].trim() === '') {
        errors[field] = `${field.charAt(0).toUpperCase() + field.slice(1)} is required`;
        isValid = false;
      }
    });

    // Email validation
    if (formData.email && !/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = 'Please enter a valid email address';
      isValid = false;
    }

    // Phone validation
    if (formData.phone && !/^\d{8,15}$/.test(formData.phone.replace(/\D/g, ''))) {
      errors.phone = 'Please enter a valid phone number';
      isValid = false;
    }

    if (!isValid) {
      // Show first error message
      const firstError = Object.values(errors)[0];
      alert(firstError);
    }

    return isValid;
  };

  const handleFormSubmit = (e) => {
    e.preventDefault();
    if (validateForm()) {
      onSubmit(formData);
    }
  };

  // Component for section header
  const SectionHeader = ({ title, icon, section }) => {
    return (
      <div
        className="section-header"
        onClick={() => toggleSection(section)}
      >
        <div className="section-header-content">
          <FontAwesomeIcon icon={icon} size="lg" className="section-icon" style={{ color: 'rgba(16, 107, 0, 1)' }} />
          <span className="section-title">{title}</span>
        </div>
        <FontAwesomeIcon 
          icon={expandedSections[section] ? faChevronUp : faChevronDown} 
          size="lg" 
          style={{ color: 'rgba(16, 107, 0, 1)' }} 
        />
      </div>
    );
  };

  // Component for section content
  const SectionContent = ({ section, children }) => {
    return (
      <div
        className="section-content"
        style={{ display: expandedSections[section] ? 'block' : 'none' }}
      >
        {children}
      </div>
    );
  };

  // Function to render the profile image section
  const renderProfileImageSection = () => (
    <div className="profile-image-section">
      <div className="profile-image-container">
        {profileImage ? (
          <img
            src={profileImage}
            alt="Profile"
            className="profile-image"
          />
        ) : (
          <div className="profile-image-placeholder">
            <span className="profile-image-placeholder-text">
              {formData.firstName && formData.lastName
                ? `${formData.firstName.charAt(0)}${formData.lastName.charAt(0)}`
                : 'U'}
            </span>
          </div>
        )}

        <div
          className="edit-image-button"
          onClick={() => setShowImagePickerModal(true)}
        >
          <FontAwesomeIcon icon={faCamera} size="lg" color="white" />
        </div>
      </div>
    </div>
  );

  return (
    <div className="container">
      <form className="form" onSubmit={handleFormSubmit}>
        {/* Profile Picture Section */}
        <div className="card">
          <SectionHeader
            title="Profile Picture"
            icon={faUser}
            section={SECTIONS.PROFILE}
          />
          <SectionContent section={SECTIONS.PROFILE}>
            {renderProfileImageSection()}
          </SectionContent>
        </div>

        {/* Personal Information Section */}
        <div className="card">
          <SectionHeader
            title="Personal Information"
            icon={faIdCard}
            section={SECTIONS.PERSONAL}
          />
          <SectionContent section={SECTIONS.PERSONAL}>
            {renderField('First Name', 'firstName', 'Enter your first name')}
            {renderField('Last Name', 'lastName', 'Enter your last name')}
            <div className="field-container">
              <label className="label">
                Date of Birth
                {requiredFields.includes('dateOfBirth') && <span className="required">*</span>}
              </label>
              <div
                className="input"
                onClick={() => setShowDateModal(true)}
                style={{ cursor: 'pointer' }}
              >
                <span className={formData.dateOfBirth ? 'date-text' : 'placeholder-text'}>
                  {formData.dateOfBirth || 'Select Date of Birth'}
                </span>
              </div>
            </div>
            <div className="field-container">
              <label className="label">
                Gender
                {requiredFields.includes('gender') && <span className="required">*</span>}
              </label>
              <div className="picker-container">
                <select
                  className="picker"
                  value={formData.gender || ''}
                  onChange={(e) => handleChange('gender', e.target.value)}
                >
                  <option value="">Select Gender</option>
                  <option value="male">Male</option>
                  <option value="female">Female</option>
                </select>
              </div>
            </div>
          </SectionContent>
        </div>

        {/* Contact Information Section */}
        <div className="card">
          <SectionHeader
            title="Contact Information"
            icon={faPhone}
            section={SECTIONS.CONTACT}
          />
          <SectionContent section={SECTIONS.CONTACT}>
            {renderField('Email', 'email', 'Enter your email')}

            <div className="field-container">
              <label className="label">
                Phone Number
                {requiredFields.includes('phone') && <span className="required">*</span>}
              </label>
              <div className="phone-container">
                <div className="dial-code">
                  <span>{formData.countryCode}</span>
                  <span className="dial-code-text">{formData.dialCode}</span>
                </div>
                <input
                  className="phone-input"
                  value={formData.phone || ''}
                  onChange={(e) => handlePhoneChange(e.target.value)}
                  placeholder="Enter phone number"
                  type="tel"
                />
              </div>
            </div>
          </SectionContent>
        </div>

        {/* Address Section */}
        <div className="card">
          <SectionHeader
            title="Address"
            icon={faMapMarkerAlt}
            section={SECTIONS.ADDRESS}
          />
          <SectionContent section={SECTIONS.ADDRESS}>
            <div className="field-container">
              <label className="label">
                Country
                {requiredFields.includes('countryCode') && <span className="required">*</span>}
              </label>
              <div className="picker-container">
                <select
                  className="picker"
                  value={formData.countryCode || ''}
                  onChange={(e) => handleCountryChange(e.target.value)}
                >
                  <option value="">Select Country</option>
                  {countries.map((country) => (
                    <option key={country.code} value={country.code}>
                      {country.name} ({country.dialCode})
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div className="field-container">
              <label className="label">
                City
                {requiredFields.includes('city') && <span className="required">*</span>}
              </label>
              <div className="picker-container">
                {loadingCities ? (
                  <div style={{ padding: '15px', textAlign: 'center' }}>Loading cities...</div>
                ) : (
                  <select
                    className="picker"
                    value={formData.city || ''}
                    onChange={(e) => handleChange('city', e.target.value)}
                    disabled={cities.length === 0}
                  >
                    <option value="">Select City</option>
                    {cities.map((city, index) => (
                      <option key={index} value={city}>{city}</option>
                    ))}
                  </select>
                )}
              </div>
            </div>

            {renderField('Address', 'address', 'Enter your address')}
          </SectionContent>
        </div>

        <div className="button-container">
          <button
            type="submit"
            className={`submit-button ${isLoading ? 'submit-button-disabled' : ''}`}
            disabled={isLoading}
          >
            {isLoading ? (
              <span>Loading...</span>
            ) : (
              <span className="submit-button-text">{buttonText}</span>
            )}
          </button>
        </div>
      </form>

      {/* Date Picker Modal */}
      {showDateModal && (
        <div className="modal-overlay">
          <div className="modal-content">
            <h3 className="modal-title">Select Date of Birth</h3>

            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '20px' }}>
              {/* Month Picker */}
              <div style={{ flex: 1, textAlign: 'center' }}>
                <label style={{ display: 'block', marginBottom: '5px', fontWeight: '600' }}>Month</label>
                <select
                  value={datePickerValues.month}
                  onChange={(e) => setDatePickerValues(prev => ({ ...prev, month: parseInt(e.target.value) }))}
                  style={{ width: '100%', padding: '8px' }}
                >
                  {months.map(month => (
                    <option key={month.value} value={month.value}>{month.label}</option>
                  ))}
                </select>
              </div>

              {/* Day Picker */}
              <div style={{ flex: 1, textAlign: 'center', margin: '0 10px' }}>
                <label style={{ display: 'block', marginBottom: '5px', fontWeight: '600' }}>Day</label>
                <select
                  value={datePickerValues.day}
                  onChange={(e) => setDatePickerValues(prev => ({ ...prev, day: parseInt(e.target.value) }))}
                  style={{ width: '100%', padding: '8px' }}
                >
                  {days.map(day => (
                    <option key={day} value={day}>{day}</option>
                  ))}
                </select>
              </div>

              {/* Year Picker */}
              <div style={{ flex: 1, textAlign: 'center' }}>
                <label style={{ display: 'block', marginBottom: '5px', fontWeight: '600' }}>Year</label>
                <select
                  value={datePickerValues.year}
                  onChange={(e) => setDatePickerValues(prev => ({ ...prev, year: parseInt(e.target.value) }))}
                  style={{ width: '100%', padding: '8px' }}
                >
                  {years.map(year => (
                    <option key={year} value={year}>{year}</option>
                  ))}
                </select>
              </div>
            </div>

            <div className="modal-buttons">
              <div
                className="modal-button-cancel"
                onClick={() => setShowDateModal(false)}
              >
                <span className="modal-button-text-cancel">Cancel</span>
              </div>
              <div
                className="modal-button-confirm"
                onClick={handleDateSelection}
              >
                <span className="modal-button-text-confirm">Confirm</span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Image Picker Modal */}
      {showImagePickerModal && (
        <div className="modal-overlay">
          <div className="modal-content">
            <h3 className="modal-title">Choose Profile Picture</h3>
            
            <div style={{ textAlign: 'center', margin: '20px 0' }}>
              <p>Select a method to set your profile picture</p>
              
              <input
                type="file"
                accept="image/*"
                onChange={handleImageSelection}
                style={{ display: 'none' }}
                id="image-upload"
              />
              
              <div style={{ display: 'flex', justifyContent: 'center', margin: '20px 0' }}>
                <label htmlFor="image-upload" style={{ 
                  display: 'flex', 
                  flexDirection: 'column', 
                  alignItems: 'center',
                  padding: '20px',
                  border: '1px solid #e0e0e0',
                  borderRadius: '8px',
                  cursor: 'pointer'
                }}>
                  <FontAwesomeIcon icon={faImage} size="2x" style={{ marginBottom: '10px' }} />
                  <span>Choose Image</span>
                </label>
              </div>
            </div>
            
            <div className="modal-buttons">
              <div
                className="modal-button-cancel"
                onClick={() => setShowImagePickerModal(false)}
              >
                <span className="modal-button-text-cancel">Cancel</span>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SharedProfileForm;
