.sort-container {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.dropdown-button {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 6px 10px;
  border-radius: 4px;
  background-color: #FFFFFF;
  border: 1px solid #E0E6ED;
  min-width: 120px;
  justify-content: space-between;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  height: 32px;
  margin-right: 8px;
  cursor: pointer;
}

.dropdown-button-text {
  color: #2C3E50;
  font-weight: 500;
  font-size: 14px;
  margin-right: 8px;
}

.direction-button {
  width: 32px;
  height: 32px;
  border-radius: 4px;
  background-color: #4CAF50;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid #3d9140;
  cursor: pointer;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  z-index: 1000;
}

.dropdown-menu {
  position: absolute;
  background-color: #FFFFFF;
  border-radius: 4px;
  border: 1px solid #E0E6ED;
  width: 160px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 1001;
}

.dropdown-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 14px;
  border-bottom: 1px solid #F5F7FA;
  width: 100%;
  text-align: left;
  background: none;
  border-left: none;
  border-right: none;
  border-top: none;
  cursor: pointer;
}

.selected-dropdown-item {
  background-color: #F5F7FA;
}

.dropdown-item-text {
  color: #2C3E50;
  font-size: 14px;
}

.selected-dropdown-item-text {
  font-weight: 500;
  color: #2C3E50;
}
