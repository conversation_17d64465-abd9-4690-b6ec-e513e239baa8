import React from 'react';
import { IoCalendarOutline, IoChevronForward, IoAddCircleOutline } from 'react-icons/io5';
import '../../styles/dashboards/upcomingList.css';

// List item component
const ListItem = ({ item, onPress }) => {
  // Determine status color
  const getStatusColor = () => {
    switch (item.status) {
      case 'completed':
        return '#4CAF50';
      case 'pending':
        return '#FF9800';
      case 'cancelled':
        return '#F44336';
      case 'confirmed':
        return '#2196F3';
      default:
        return '#757575';
    }
  };

  // Format status text
  const formatStatus = (status) => {
    if (!status) return '';
    return status.charAt(0).toUpperCase() + status.slice(1);
  };

  return (
    <div 
      className="list-item"
      onClick={() => onPress && onPress(item)}
    >
      <div className="list-item-content">
        <div className="list-item-header">
          <h3 className="list-item-title">{item.title}</h3>
          <span 
            className="list-item-status"
            style={{ backgroundColor: getStatusColor() }}
          >
            {formatStatus(item.status)}
          </span>
        </div>
        <p className="list-item-description">{item.description}</p>
        <div className="list-item-footer">
          <span className="list-item-time">{item.time}</span>
          <IoChevronForward className="list-item-icon" />
        </div>
      </div>
    </div>
  );
};

const UpcomingList = ({
  title = 'Upcoming',
  data = [],
  onItemPress,
  onViewAll,
  emptyText = 'No upcoming items',
  backgroundColor = '#fff',
  maxItems = 5,
  style,
}) => {
  // Verify if data is valid
  const validData = Array.isArray(data) ? data : [];

  const hasItems = validData && validData.length > 0;
  const displayData = hasItems ? validData.slice(0, maxItems) : [];
  const hasMore = hasItems && validData.length > maxItems;

  return (
    <div 
      className="upcoming-list-container"
      style={{ backgroundColor, ...style }}
    >
      {title && (
        <div className="upcoming-list-header">
          <h2 className="upcoming-list-title">{title}</h2>
          {hasMore && (
            <button
              className="view-all-button"
              onClick={onViewAll}
            >
              <span>View All</span>
              <IoChevronForward size={16} color="#1976D2" />
            </button>
          )}
        </div>
      )}

      {hasItems ? (
        <div className="upcoming-list-items">
          {displayData.map((item, index) => (
            <React.Fragment key={`${item.id || index}`}>
              {index > 0 && <div style={{ height: 8 }} />}
              <ListItem item={item} onPress={onItemPress} />
            </React.Fragment>
          ))}
        </div>
      ) : (
        <div className="empty-container">
          <IoCalendarOutline size={48} color="#E0E0E0" />
          <p className="empty-text">{emptyText}</p>
          {title.toLowerCase().includes('appointment') && (
            <button
              className="empty-action-button"
              onClick={onViewAll}
            >
              <IoAddCircleOutline size={16} color="#FFFFFF" style={{ marginRight: 6 }} />
              <span>Request Appointment</span>
            </button>
          )}
        </div>
      )}
    </div>
  );
};

export default UpcomingList;
