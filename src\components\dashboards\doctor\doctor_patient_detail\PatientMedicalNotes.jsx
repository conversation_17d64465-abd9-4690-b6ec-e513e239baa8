import React, { useState, useEffect } from 'react';
import { Ionicons } from '@expo/vector-icons';
import { useNavigate } from 'react-router-dom';
import { ROLE_COLORS } from '../../../../config/theme';
import { localMedicalNotesService } from '../../../../services/localMedicalNotesService';
import '../../../../styles/dashboards/doctor/doctor_patient_detail/patientMedicalNotes.css';

const PatientMedicalNotes = ({ patientId, patientName, noteType }) => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [notes, setNotes] = useState([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedNote, setSelectedNote] = useState(null);
  const doctorColors = ROLE_COLORS.doctor;

  useEffect(() => {
    fetchPatientNotes();
  }, [patientId, noteType]);

  const fetchPatientNotes = async () => {
    if (!patientId) return;

    setLoading(true);
    try {
      // Fetch notes for the selected patient
      const notesData = await localMedicalNotesService.getPatientMedicalNotes(patientId);

      // Filter notes by type if specified
      const filteredNotes = noteType
        ? notesData.filter(note => note.type === noteType)
        : notesData;

      setNotes(filteredNotes || []);
    } catch (error) {
      console.error('Error fetching patient medical notes:', error);
    } finally {
      setLoading(false);
    }
  };

  const openNoteDetails = (note) => {
    setSelectedNote(note);
    setModalVisible(true);
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return 'Unknown date';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Render the notes list
  const renderNotesList = () => {
    if (notes.length === 0) {
      return (
        <div className="emptyContainer">
          <Ionicons name="document-text-outline" size={48} color={doctorColors.primary} />
          <p className="emptyText">No medical notes found for this patient</p>
        </div>
      );
    }

    return (
      <div>
        {notes.map(item => (
          <div
            key={item.id}
            className="noteCard"
            onClick={() => openNoteDetails(item)}
          >
            <div className="noteHeader">
              <div className="categoryContainer">
                <Ionicons
                  name={item.type === 'vitals' ? 'pulse' : 'document-text'}
                  size={16}
                  color={doctorColors.primary}
                />
                <span className="categoryText">{item.category}</span>
              </div>
              <span className="dateText">{formatDate(item.timestamp)}</span>
            </div>

            <p className="noteContent">
              {item.content}
            </p>

            <Ionicons name="chevron-forward" size={24} className="chevron" />
          </div>
        ))}
      </div>
    );
  };

  // Render the detail modal
  const renderDetailModal = () => {
    if (!selectedNote || !modalVisible) return null;

    return (
      <div className="modalOverlay">
        <div className="modalContainer">
          <div className="modalHeader">
            <h3 className="modalTitle">Medical Note Details</h3>
            <div
              className="closeButton"
              onClick={() => setModalVisible(false)}
            >
              <Ionicons name="close" size={24} color="#333" />
            </div>
          </div>

          <div className="modalContent">
            <div className="modalSection">
              <h4 className="modalSectionTitle">
                {selectedNote.category}
              </h4>
              <p className="modalDateText">
                {formatDate(selectedNote.timestamp)}
              </p>
            </div>

            <div className="modalSection">
              <p className="modalContentText">
                {selectedNote.content}
              </p>
            </div>

            <div className="modalMetadata">
              <p className="modalMetadataText">
                Note ID: {selectedNote.id}
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="container">
      <div className="header">
        <h2 className="headerTitle">
          {patientName ? `${patientName}'s Medical Notes` : 'Patient Medical Notes'}
        </h2>
        <p className="headerSubtitle">
          {noteType === 'vitals' ? 'Notes for Vital Signs' :
           noteType === 'symptoms' ? 'Notes for Symptoms' :
           'All Medical Notes'}
        </p>
      </div>

      {loading ? (
        <div className="loadingContainer">
          <div className="spinner" style={{ color: doctorColors.primary }}></div>
          <p className="loadingText">Loading medical notes...</p>
        </div>
      ) : (
        <div className="content">
          {renderNotesList()}
        </div>
      )}

      {renderDetailModal()}
    </div>
  );
};

export default PatientMedicalNotes;
