.container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.gradient-background {
  min-height: 100vh;
  background: linear-gradient(to bottom, var(--doctor-primary-light) 0%, #f5f5f5 20%);
}

.header {
  padding: 16px;
}

.title {
  font-size: 24px;
  font-weight: bold;
  color: #212121;
  margin-bottom: 4px;
}

.subtitle {
  font-size: 14px;
  color: #757575;
}

.search-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  background-color: #fff;
  border-radius: 8px;
  margin: 8px 16px;
  padding: 8px 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-icon {
  margin-right: 8px;
}

.search-input {
  flex: 1;
  font-size: 16px;
  color: #212121;
  border: none;
  outline: none;
  background: transparent;
}

.filter-container {
  display: flex;
  flex-direction: row;
  padding: 8px 16px;
  margin: 8px 0;
}

.filter-button {
  padding: 8px 16px;
  border-radius: 20px;
  margin-right: 8px;
  cursor: pointer;
  border: none;
  background: transparent;
}

.active-filter {
  background-color: var(--doctor-primary);
}

.filter-text {
  color: #757575;
  font-weight: 500;
}

.active-filter-text {
  color: #fff;
  font-weight: 500;
}

.list-content {
  padding: 16px;
  flex-grow: 1;
}

.patient-card {
  background-color: #fff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 12px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.2s ease;
}

.patient-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.patient-info {
  display: flex;
  flex-direction: row;
  flex: 1;
}

.avatar-container {
  position: relative;
  margin-right: 12px;
}

.avatar {
  width: 50px;
  height: 50px;
  border-radius: 25px;
  object-fit: cover;
}

.avatar-placeholder {
  width: 50px;
  height: 50px;
  border-radius: 25px;
  background-color: var(--doctor-primary-lighter);
  display: flex;
  justify-content: center;
  align-items: center;
}

.avatar-text {
  font-size: 18px;
  font-weight: bold;
  color: var(--doctor-primary);
}

.status-indicator {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 12px;
  height: 12px;
  border-radius: 6px;
  border: 2px solid #fff;
}

.status-online {
  background-color: #4CAF50;
}

.status-offline {
  background-color: #9e9e9e;
}

.patient-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.patient-name {
  font-size: 16px;
  font-weight: bold;
  color: #212121;
  margin-bottom: 2px;
}

.patient-condition {
  font-size: 14px;
  color: #757575;
  margin-bottom: 2px;
}

.patient-age {
  font-size: 12px;
  color: #9e9e9e;
}

.consultation-info {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-end;
}

.last-consultation {
  font-size: 12px;
  color: #9e9e9e;
  margin-bottom: 8px;
}

.consult-button {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 8px 12px;
  border-radius: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  border: none;
  cursor: pointer;
}

.consult-button-active {
  background-color: var(--doctor-primary);
}

.consult-button-disabled {
  background-color: #9e9e9e;
  cursor: not-allowed;
}

.consult-button-text {
  color: #fff;
  font-size: 14px;
  font-weight: bold;
  margin-left: 6px;
}

.loading-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 20px;
  min-height: 300px;
}

.loading-text {
  margin-top: 10px;
  font-size: 16px;
  color: #757575;
}

.empty-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 20px;
  min-height: 300px;
}

.empty-text {
  font-size: 18px;
  font-weight: bold;
  color: #757575;
  margin-top: 10px;
}

.empty-sub-text {
  font-size: 14px;
  color: #9e9e9e;
  text-align: center;
  margin-top: 5px;
}

.spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border-left-color: var(--doctor-primary);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
