/* Patient Guidance Receiver Styles */
.container {
  flex: 1;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.header {
  padding: 16px;
  background-color: #fff;
  border-bottom: 1px solid #e0e0e0;
}

.headerTitle {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

.guidanceList {
  padding: 16px;
}

.guidanceItem {
  display: flex;
  flex-direction: row;
  align-items: center;
  background-color: #fff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.guidanceIconContainer {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  background-color: var(--patient-primary-color);
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 12px;
}

.guidanceContent {
  flex: 1;
}

.guidanceDestination {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.guidanceSupervisor {
  font-size: 14px;
  color: #666;
}

.guidanceTime {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

.emptyContainer {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 24px;
  height: 70vh;
}

.emptyText {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-top: 16px;
}

.emptySubtext {
  font-size: 14px;
  color: #666;
  text-align: center;
  margin-top: 8px;
}

.modalBackdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: flex-end;
  z-index: 1000;
}

.modalContent {
  background-color: #fff;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  padding: 16px;
  max-height: 80vh;
  width: 100%;
  overflow-y: auto;
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.modalTitle {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.closeButton {
  background: none;
  border: none;
  cursor: pointer;
}

.guidanceDetails {
  margin-bottom: 16px;
}

.guidanceDetailRow {
  display: flex;
  flex-direction: row;
  margin-bottom: 12px;
}

.guidanceDetailLabel {
  width: 100px;
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.guidanceDetailValue {
  flex: 1;
  font-size: 16px;
  color: #333;
}

.routeTypeContainer {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.routeTypeText {
  margin-left: 8px;
  font-size: 16px;
  color: #333;
}

.routeInfoContainer {
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 12px;
}

.routeInfoItem {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 8px;
}

.routeInfoText {
  margin-left: 8px;
  font-size: 14px;
  color: #333;
}

.instructionsContainer {
  margin-top: 8px;
  margin-bottom: 16px;
}

.instructionsLabel {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
}

.instructionsText {
  font-size: 16px;
  color: #333;
  line-height: 24px;
  background-color: #f5f5f5;
  padding: 12px;
  border-radius: 8px;
}

.startNavigationButton {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  background-color: var(--patient-primary-color);
  padding: 16px;
  border-radius: 8px;
  margin-top: 16px;
  cursor: pointer;
  border: none;
}

.startNavigationText {
  color: #fff;
  font-weight: bold;
  font-size: 16px;
  margin-left: 8px;
}
