import React, { useState, useEffect } from 'react';
import { IoSearch, IoClose, IoCloseCircle, IoChevronForward, IoPeople } from 'react-icons/io5';
import { ROLE_COLORS } from '../../config/theme';
import { firebaseDoctorPatientsService } from '../../services/firebaseDoctorPatientsService';
import '../../styles/doctor/doctorPatientSelectorModal.css';

const DoctorPatientSelectorModal = ({ visible, onClose, onSuccess, scannerTitle = 'Select Patient' }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [patients, setPatients] = useState([]);
  const [loading, setLoading] = useState(true);
  const doctorColors = ROLE_COLORS.doctor;

  // Load patients when modal becomes visible
  useEffect(() => {
    if (visible) {
      loadPatients();
    }
  }, [visible]);

  // Load patients linked to the current doctor
  const loadPatients = async () => {
    setLoading(true);
    try {
      const doctorPatients = await firebaseDoctorPatientsService.getDoctorPatients();
      setPatients(doctorPatients);
    } catch (error) {
      console.error('Error loading doctor patients:', error);
    } finally {
      setLoading(false);
    }
  };

  // Filter patients based on search query
  const filteredPatients = patients.filter(patient => {
    const fullName = `${patient.firstName} ${patient.lastName}`.toLowerCase();
    return fullName.includes(searchQuery.toLowerCase());
  });

  const handlePatientSelect = (patient) => {
    // Format patient data for the prescription form
    const formattedPatient = {
      id: patient.id,
      name: `${patient.firstName} ${patient.lastName}`
    };
    onSuccess(formattedPatient);
    onClose();
  };

  const renderPatientItem = (item) => (
    <div 
      className="patientItem"
      onClick={() => handlePatientSelect(item)}
      key={item.id}
    >
      <div className="patientIcon">
        <span className="patientInitials">
          {item.firstName.charAt(0)}{item.lastName.charAt(0)}
        </span>
      </div>
      <div className="patientInfo">
        <div className="patientName">{item.firstName} {item.lastName}</div>
        <div className="patientDetails">
          {item.gender ? `${item.gender} • ` : ''}
          {item.age ? `${item.age} years` : ''}
        </div>
      </div>
      <IoChevronForward size={20} color="#9E9E9E" />
    </div>
  );

  if (!visible) return null;

  return (
    <div className="modalOverlay">
      <div className="modalContent">
        <div className="modalHeader">
          <h2 className="modalTitle">{scannerTitle}</h2>
          <button className="closeButton" onClick={onClose}>
            <IoClose size={24} color="#333" />
          </button>
        </div>

        <div className="searchContainer">
          <IoSearch size={20} color="#757575" className="searchIcon" />
          <input
            className="searchInput"
            placeholder="Search patients..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            autoCapitalize="none"
          />
          {searchQuery ? (
            <button className="clearButton" onClick={() => setSearchQuery('')}>
              <IoCloseCircle size={20} color="#757575" />
            </button>
          ) : null}
        </div>

        {loading ? (
          <div className="loadingContainer">
            <div className="spinner" style={{ borderTopColor: doctorColors.primary }}></div>
            <p className="loadingText">Loading your patients...</p>
          </div>
        ) : (
          <div className="patientList">
            {filteredPatients.length > 0 ? (
              filteredPatients.map(patient => renderPatientItem(patient))
            ) : (
              <div className="emptyContainer">
                {searchQuery ? (
                  <>
                    <IoSearch size={48} color="#BDBDBD" />
                    <p className="emptyText">No patients found matching "{searchQuery}"</p>
                  </>
                ) : (
                  <>
                    <IoPeople size={48} color="#BDBDBD" />
                    <p className="emptyText">You don't have any linked patients yet</p>
                  </>
                )}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default DoctorPatientSelectorModal;
