import React, { createContext, useState, useEffect, useContext } from "react";
import { collection, getDocs, doc, setDoc, deleteDoc, updateDoc } from 'firebase/firestore';
import { db } from '../config/firebase';
// Import default avatar image
import defaultAvatar from "../assets/ImagesTest/avatar.png";

export const UserContext = createContext();

export const UserProvider = ({ children }) => {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);

  // Get Users from Firebase
  const fetchUsers = async () => {
    setLoading(true);
    try {
      // Get users collection from Firestore
      const usersCollection = collection(db, 'users');
      const usersSnapshot = await getDocs(usersCollection);

      if (usersSnapshot.empty) {
        console.log('No users found in Firestore');
        setUsers([]);
        return;
      }

      // Process the data
      const firebaseUsers = [];
      usersSnapshot.forEach(doc => {
        const userData = doc.data();
        let createdAtDate = null;

        // Safely handle createdAt conversion
        if (userData.createdAt) {
          if (typeof userData.createdAt.toDate === 'function') {
            createdAtDate = userData.createdAt.toDate();
          } else if (userData.createdAt instanceof Date) {
            createdAtDate = userData.createdAt;
          } else if (typeof userData.createdAt === 'string') {
            createdAtDate = new Date(userData.createdAt);
          }
        }

        firebaseUsers.push({
          uid: userData.uid || doc.id,
          id: userData.uid || doc.id, // For compatibility with existing code
          firstName: userData.firstName || '',
          lastName: userData.lastName || '',
          displayName: userData.displayName || '',
          email: userData.email || '',
          role: userData.role || '',
          speciality: userData.speciality || '',
          location: userData.location || { city: '', country: '' },
          country: userData.location?.country || '',
          city: userData.location?.city || '',
          status: userData.status || 'active',
          createdAt: createdAtDate,
          profilePicture: userData.profilePicture || defaultAvatar // Changed to use imported image
        });
      });

      console.log('Successfully retrieved users from Firestore:', firebaseUsers.length);
      setUsers(firebaseUsers);
    } catch (error) {
      console.error("Error fetching users from Firebase:", error);
      setUsers([]);
    } finally {
      setLoading(false);
    }
  };

  // Add a new user
  const addUser = async (user) => {
    try {
      // Add to Firestore
      if (user.uid) {
        await setDoc(doc(db, 'users', user.uid), user);
        console.log('User added to Firestore');
      } else {
        console.error('Cannot add user: Missing UID');
      }

      // Update local state
      setUsers([...users, user]);
    } catch (error) {
      console.error('Error adding user:', error);
    }
  };

  // Delete a user
  const removeUser = async (id) => {
    try {
      // Delete from Firestore
      await deleteDoc(doc(db, 'users', id));
      console.log('User deleted from Firestore');

      // Update local state
      setUsers(users.filter((user) => user.uid !== id && user.id !== id));
    } catch (error) {
      console.error('Error deleting user:', error);
    }
  };

  // Update user status
  const updateUserStatus = async (id, status) => {
    try {
      // Update in Firestore
      await updateDoc(doc(db, 'users', id), { status });
      console.log('User status updated in Firestore');

      // Update local state
      setUsers(
        users.map((user) => (user.uid === id || user.id === id ? { ...user, status } : user))
      );
    } catch (error) {
      console.error('Error updating user status:', error);
    }
  };

  // Ban a user
  const banUser = async (id) => {
    try {
      // Update in Firestore
      await updateDoc(doc(db, 'users', id), { status: 'banned' });
      console.log('User banned in Firestore');

      // Update local state
      setUsers(
        users.map((user) =>
          user.uid === id || user.id === id ? { ...user, status: "banned" } : user
        )
      );
    } catch (error) {
      console.error('Error banning user:', error);
    }
  };

  // Fetch users on component mount
  useEffect(() => {
    fetchUsers();
  }, []);

  // Get user statistics from Firebase data
  const getUserStats = () => {
    const activeUsers = users.filter((user) => user.status === "active").length;
    const newPatients = users.filter(
      (user) => {
        // Check if role is patient (case insensitive)
        const isPatient = user.role && user.role.toLowerCase() === "patient";

        // Check if created within last 30 days
        const isNew = user.createdAt &&
          new Date(user.createdAt) > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

        return isPatient && isNew;
      }
    ).length;

    return {
      activeUsers: activeUsers.toString(),
      newPatients: newPatients.toString(),
    };
  };

  return (
    <UserContext.Provider
      value={{
        users,
        loading,
        fetchUsers,
        addUser,
        removeUser,
        updateUserStatus,
        banUser,
        getUserStats,
      }}
    >
      {children}
    </UserContext.Provider>
  );
};

export const useUsers = () => useContext(UserContext);
