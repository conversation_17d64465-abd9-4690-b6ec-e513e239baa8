/* Navigation styles */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 5%;
  background-color: var(--background-white);
  box-shadow: var(--shadow-md);
  position: sticky;
  top: 0;
  z-index: 100;
}

.logo-container {
  display: flex;
  flex-direction: column;
}

.logo, .navigation-h1-logo {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--primary);
  margin: 0;
}

.tagline, .navigation-p-tagline {
  font-size: 0.9rem;
  color: var(--text-medium);
  margin: 0;
}

.navigation {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.nav-button {
  background: none;
  border: none;
  padding: 0.5rem 1rem;
  font-size: 1rem;
  color: var(--text-dark-nav);
  cursor: pointer;
  transition: color 0.3s;
}

.nav-button:hover {
  color: var(--accent-nav);
}

.login-button-nav {
  background-color: var(--primary);
  color: white;
  border: none;
  border-radius: 20px;
  padding: 0.5rem 1.5rem;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.3s;
  text-decoration: none;
  display: inline-block;
}

.login-button-nav:hover {
  background-color: var(--primary-dark);
}

.mobile-menu-button {
  display: none;
  flex-direction: column;
  justify-content: space-between;
  width: 30px;
  height: 21px;
  background: transparent;
  border: none;
  cursor: pointer;
}

.mobile-menu-button span {
  width: 100%;
  height: 3px;
  background-color: var(--primary);
  transition: all 0.3s;
}

.mobile-menu-button.active span:nth-child(1) {
  transform: translateY(9px) rotate(45deg);
}

.mobile-menu-button.active span:nth-child(2) {
  opacity: 0;
}

.mobile-menu-button.active span:nth-child(3) {
  transform: translateY(-9px) rotate(-45deg);
}

/* Responsive design for navigation */
@media (max-width: 768px) {
  .navigation {
    display: none;
  }

  .navigation.mobile-menu-open {
    display: flex;
    flex-direction: column;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: var(--background-white);
    padding: 1rem;
    box-shadow: var(--shadow-md);
    z-index: 99;
  }

  .mobile-menu-button {
    display: flex;
  }
}

@media (max-width: 480px) {
  .header {
    padding: 1rem 5%;
  }

  .logo, .navigation-h1-logo {
    font-size: 1.5rem;
  }
}

/* Accessibility improvements */
.nav-button:focus,
.login-button-nav:focus {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}
