.container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #121212;
  padding: 20px;
  box-sizing: border-box;
}

.form-container {
  width: 90%;
  max-width: 400px;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #fff;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.camera-container {
  width: 100%;
  height: 70vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: #333;
  position: relative;
}

.scan-area {
  width: 70vw;
  height: 70vw;
  max-width: 300px;
  max-height: 300px;
  border: 2px solid #fff;
  border-radius: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.scanning-animation {
  background-color: rgba(0, 0, 0, 0.7);
  padding: 20px;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.scanning-text {
  color: #fff;
  font-weight: bold;
  margin-top: 10px;
}

.capture-button {
  position: absolute;
  bottom: 30px;
  width: 70px;
  height: 70px;
  border-radius: 35px;
  background-color: #007bff;
  border: none;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  cursor: pointer;
  transition: background-color 0.3s;
}

.capture-button:hover {
  background-color: #0056b3;
}

.capture-button:disabled {
  background-color: #666;
  cursor: not-allowed;
}

.camera-icon {
  font-size: 32px;
  color: #fff;
}

.info-box {
  position: absolute;
  top: 100px;
  left: 20px;
  right: 20px;
  background-color: rgba(0, 0, 0, 0.7);
  padding: 15px;
  border-radius: 10px;
}

.info-text {
  color: #fff;
  font-size: 16px;
  margin-bottom: 10px;
  text-align: center;
}

.footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20px;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.footer-text {
  color: #fff;
  font-size: 16px;
  margin-bottom: 20px;
  text-align: center;
}

.button-row {
  display: flex;
  justify-content: space-around;
  width: 100%;
  gap: 10px;
  flex-wrap: wrap;
}

.qr-icon {
  font-size: 80px;
  margin-bottom: 20px;
}

.title {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
  text-align: center;
}

.subtitle {
  font-size: 16px;
  color: #666;
  text-align: center;
  margin-bottom: 24px;
}

.input {
  width: 100%;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 16px;
  font-size: 18px;
  text-align: center;
  letter-spacing: 2px;
  box-sizing: border-box;
  text-transform: uppercase;
}

.input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.helper-container {
  width: 100%;
  margin-top: 8px;
  margin-bottom: 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.helper-text {
  color: #666;
  font-size: 14px;
  text-align: center;
}

.validation-text {
  font-size: 14px;
  font-weight: bold;
  margin-top: 8px;
}

.valid-text {
  color: #4CAF50;
}

.invalid-text {
  color: #F44336;
}

.button {
  background-color: #fff;
  padding: 14px 24px;
  border-radius: 8px;
  margin-bottom: 8px;
  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.3);
  width: 100%;
  border: 1px solid #ddd;
  cursor: pointer;
  font-size: 18px;
  font-weight: bold;
  text-align: center;
  transition: all 0.3s;
  color: #000;
}

.button:hover {
  background-color: #f8f9fa;
}

.button-disabled {
  background-color: #f0f0f0;
  border-color: #ddd;
  color: #999;
  cursor: not-allowed;
}

.button-valid {
  background-color: #fff;
  border: 2px solid #4CAF50;
  color: #4CAF50;
}

.button-valid:hover {
  background-color: #4CAF50;
  color: #fff;
}

.primary-button {
  background-color: #007bff;
  color: #fff;
  border-color: #007bff;
}

.primary-button:hover {
  background-color: #0056b3;
}

.cancel-button {
  margin-top: 16px;
  padding: 12px;
  background: none;
  border: none;
  color: #666;
  font-size: 16px;
  cursor: pointer;
  transition: color 0.3s;
}

.cancel-button:hover {
  color: #333;
}

.cancel-button:disabled {
  color: #ccc;
  cursor: not-allowed;
}

.loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #121212;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #333;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 12px;
  font-size: 16px;
  color: #fff;
}

.permission-text {
  font-size: 18px;
  color: #fff;
  text-align: center;
  padding: 20px;
}

.message {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  padding: 15px 20px;
  border-radius: 8px;
  color: #fff;
  font-weight: bold;
  z-index: 1000;
  max-width: 90%;
  text-align: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.message.success {
  background-color: #4CAF50;
}

.message.danger {
  background-color: #F44336;
}

.message.info {
  background-color: #2196F3;
}

.message p {
  margin: 5px 0 0 0;
  font-weight: normal;
  font-size: 14px;
}

/* Responsive design */
@media (max-width: 768px) {
  .form-container {
    width: 95%;
    padding: 20px;
  }
  
  .button-row {
    flex-direction: column;
  }
  
  .button-row .button {
    margin-bottom: 10px;
  }
  
  .scan-area {
    width: 80vw;
    height: 80vw;
  }
}
