import React, { useRef, useEffect, useState } from 'react';
import { IoIosArrowUp, IoIosArrowDown, IoIosCheckmarkCircle } from 'react-icons/io';
import '../../styles/dashboards/dashboardCard.css';

const DashboardCard = ({
  title,
  value,
  icon,
  iconColor = '#4285F4',
  backgroundColor = '#fff',
  onClick,
  width = '100%',
  height,
  subtitle,
  trend,
  trendValue,
  unit,
  status,
  gradientStart = '#f5f5f5',
  gradientEnd = '#e0e0e0',
}) => {
  // Animation state
  const [scale, setScale] = useState(1);
  
  const renderTrend = () => {
    if (!trend) return null;

    const isPositive = trend === 'up';
    const TrendIcon = isPositive ? IoIosArrowUp : IoIosArrowDown;
    const trendColor = isPositive ? '#4CAF50' : '#F44336';

    return (
      <div className="trendContainer" style={{ backgroundColor: `${trendColor}20` }}>
        <TrendIcon size={16} color={trendColor} />
        <span className="trendValue" style={{ color: trendColor }}>
          {trendValue || ''}
        </span>
      </div>
    );
  };

  const getStatusInfo = () => {
    if (!status) return null;

    let statusColor, StatusIcon, statusText;

    switch(status) {
      case 'high':
        statusColor = '#F44336';
        StatusIcon = IoIosArrowUp;
        statusText = 'High';
        break;
      case 'low':
        statusColor = '#FFC107';
        StatusIcon = IoIosArrowDown;
        statusText = 'Low';
        break;
      case 'normal':
      default:
        statusColor = '#4CAF50';
        StatusIcon = IoIosCheckmarkCircle;
        statusText = 'Normal';
        break;
    }

    return { statusColor, StatusIcon, statusText };
  };

  // Handle press animation
  const handleMouseDown = () => {
    if (onClick) {
      setScale(0.97);
    }
  };

  const handleMouseUp = () => {
    if (onClick) {
      setScale(1);
    }
  };

  // Get the appropriate icon component
  const IconComponent = icon;

  return (
    <div 
      className="cardWrapper"
      style={{
        width: width,
        transform: `scale(${scale})`,
        transition: 'transform 0.2s ease'
      }}
    >
      <div
        className="card"
        style={{
          width: '100%',
          backgroundColor: '#ffffff',
          borderLeftColor: iconColor,
          cursor: onClick ? 'pointer' : 'default'
        }}
        onClick={onClick}
        onMouseDown={handleMouseDown}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
      >
        <div 
          className="cardGradient"
          style={{
            background: `linear-gradient(to bottom right, ${gradientStart}40, #ffffff)`
          }}
        >
          <div className="cardContent">
            <div
              className="iconContainer"
              style={{
                backgroundColor: gradientStart
              }}
            >
              {React.createElement(IconComponent, { size: 28, color: iconColor })}
            </div>
            <div className="cardTextContainer">
              <h3 className="cardTitle">{title}</h3>
              <div className="valueContainer">
                <span className="cardValue">{value}</span>
                {unit && <span className="unitText">{unit}</span>}
              </div>
              {status && getStatusInfo() && (
                <div 
                  className="statusContainer" 
                  style={{ backgroundColor: `${getStatusInfo().statusColor}20` }}
                >
                  {React.createElement(getStatusInfo().StatusIcon, { 
                    size: 12, 
                    color: getStatusInfo().statusColor 
                  })}
                  <span 
                    className="statusText" 
                    style={{ color: getStatusInfo().statusColor }}
                  >
                    {getStatusInfo().statusText}
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardCard;
