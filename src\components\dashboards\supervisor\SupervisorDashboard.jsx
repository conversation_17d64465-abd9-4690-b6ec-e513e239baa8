import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card } from 'react-bootstrap';
import { IoHome, IoPeople, IoMedkit, IoPeopleCircle, IoCalendar, IoStatsChart, 
  IoDocument, IoChatbubbles, IoQrCode, IoPerson, IoSettings, 
  IoNotifications, IoPulse, IoNavigate } from 'react-icons/io5';
import { collection, getDocs } from 'firebase/firestore';
import { db } from '../../../config/firebase';
import { useAuth } from '../../../contexts/AuthContext';
import { getThemeForRole, ROLE_COLORS } from '../../../config/theme';
import DashboardLayout from '../DashboardLayout';
import DashboardCard from '../DashboardCard';
import Dashboard<PERSON>hart from '../DashboardChart';
import UpcomingList from '../UpcomingList';
import AddPatientButton from './button/AddPatientButton';
import '../../../styles/dashboards/supervisor/supervisorDashboard.css';

const SupervisorDashboard = ({ notifications = [] }) => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const theme = getThemeForRole('supervisor');
  const supervisorColors = ROLE_COLORS.supervisor; // Using supervisor-specific colors
  const [selectedSection, setSelectedSection] = useState('overview');
  const [refreshing, setRefreshing] = useState(false);

  // State for dashboard data
  const [statsData, setStatsData] = useState({
    staff: 0,
    patients: 0,
    doctors: 0,
    caregivers: 0,
    activities: [0, 0, 0, 0, 0, 0, 0]
  });

  const [activityData, setActivityData] = useState([]);

  const menuItems = [
    { label: 'Dashboard', icon: <IoHome />, screen: 'Dashboard' },
    { label: 'Staff', icon: <IoPeople />, screen: 'Patients' },
    { label: 'Patients', icon: <IoMedkit />, screen: 'SupervisorPatientHealth' },
    { label: 'Caregivers', icon: <IoPeopleCircle />, screen: 'SupervisorCaregiverManagement' },
    { label: 'Appointments', icon: <IoCalendar />, screen: 'SupervisorAppointments' },
    { label: 'Analytics', icon: <IoStatsChart />, screen: 'HealthRecords' },
    { label: 'Reports', icon: <IoDocument />, screen: 'HealthRecords' },
    { label: 'Messages', icon: <IoChatbubbles />, screen: 'Chatroom' },
    { label: 'My QR Code', icon: <IoQrCode />, screen: 'UserQRCode' },
    { label: 'Profile', icon: <IoPerson />, screen: 'Profile' },
    { label: 'Settings', icon: <IoSettings />, screen: 'Settings' },
  ];

  // Function to fetch dashboard data from Firebase
  const fetchDashboardData = async () => {
    try {
      // Fetch users data to count staff, patients, doctors, and caregivers
      const usersCollection = collection(db, 'users');
      const usersSnapshot = await getDocs(usersCollection);

      // Count users by role
      let staffCount = 0;
      let patientCount = 0;
      let doctorCount = 0;
      let caregiverCount = 0;

      usersSnapshot.docs.forEach(doc => {
        const userData = doc.data();
        const role = userData.role?.toLowerCase();

        if (role === 'patient') {
          patientCount++;
        } else if (role === 'doctor') {
          doctorCount++;
          staffCount++;
        } else if (role === 'caregiver' || role === 'nurse') {
          caregiverCount++;
          staffCount++;
        } else if (role && role !== 'patient') {
          // Count any other non-patient role as staff
          staffCount++;
        }
      });

      // Generate random activity data for now (this would be replaced with real data)
      const activityValues = Array(7).fill(0).map(() => Math.floor(Math.random() * 30) + 10);

      // Update stats data
      setStatsData({
        staff: staffCount,
        patients: patientCount,
        doctors: doctorCount,
        caregivers: caregiverCount,
        activities: activityValues
      });

      // Try to fetch activities from Firebase if available
      try {
        const activitiesCollection = collection(db, 'activities');
        const activitiesSnapshot = await getDocs(activitiesCollection);

        if (!activitiesSnapshot.empty) {
          const activities = activitiesSnapshot.docs.map(doc => {
            const data = doc.data();
            return {
              id: doc.id,
              name: data.name || 'Activity',
              time: data.time || 'No time specified',
              status: data.status || 'upcoming'
            };
          });

          setActivityData(activities);
        } else {
          // If no activities found, set empty array
          setActivityData([]);
        }
      } catch (error) {
        console.log('No activities collection available:', error);
        setActivityData([]);
      }

    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    }
  };

  // Load data when component mounts
  useEffect(() => {
    fetchDashboardData();
  }, []);

  // Function to handle patient added
  const handlePatientAdded = (patient) => {
    // Use a toast notification library here instead of react-native-flash-message
    // For example: toast.success(`Patient ${patient.displayName} added successfully`);
    
    // Refresh dashboard data
    fetchDashboardData();
  };

  // Function to handle refresh
  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchDashboardData();
    setRefreshing(false);
  };

  return (
    <DashboardLayout
      title="Supervisor Dashboard"
      menuItems={menuItems}
      userRole="supervisor"
      notifications={notifications}
    >
      <div className="scrollView">
        <div className="headerSection">
          <div className="headerGradient">
            <div className="headerContent">
              <div className="welcomeContainer">
                <h2 className="welcomeText">Welcome, {user?.firstName || 'Supervisor'}</h2>
                <p className="dateText">{new Date().toLocaleDateString('en-US', {
                  weekday: 'long',
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })}</p>
              </div>
              <div className="headerButtonContainer">
                <AddPatientButton onPatientAdded={handlePatientAdded} headerStyle={true} />
              </div>
            </div>
          </div>
        </div>

        <div className="contentSection">
          <div className="sectionHeader">
            <h3 className="sectionTitle">Facility Overview</h3>
          </div>

          <div className="statsContainer">
            <DashboardCard
              title="Total Staff"
              value={statsData.staff.toString()}
              icon={<IoPeople />}
              color="#4A148C"
              gradientStart="#E1BEE7"
              gradientEnd="#ffffff"
              width="48%"
            />
            <DashboardCard
              title="Total Patients"
              value={statsData.patients.toString()}
              icon={<IoPeople />}
              color="#4A148C"
              gradientStart="#E1BEE7"
              gradientEnd="#ffffff"
              width="48%"
            />
            <DashboardCard
              title="Doctors"
              value={statsData.doctors.toString()}
              icon={<IoMedkit />}
              color="#4A148C"
              gradientStart="#E1BEE7"
              gradientEnd="#ffffff"
              width="48%"
            />
            <DashboardCard
              title="Caregivers"
              value={statsData.caregivers.toString()}
              icon={<IoPeopleCircle />}
              color="#4A148C"
              gradientStart="#E1BEE7"
              gradientEnd="#ffffff"
              width="48%"
            />
          </div>

          <Card className="chartCard">
            <Card.Header>
              <div className="chartTitle">Facility Activity</div>
              <div className="chartSubtitle">Last 7 Days</div>
            </Card.Header>
            <Card.Body className="chartContent">
              <DashboardChart
                data={statsData.activities}
                labels={['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']}
                color="#4A148C"
                type="line"
                height={240}
                backgroundColor="#ffffff"
              />
            </Card.Body>
          </Card>

          <div className="reportContainer">
            <h3 className="sectionTitle">Quick Actions</h3>

            <div className="quickActions">
              <a 
                className="actionButton"
                onClick={() => navigate('/profile')}
              >
                <div className="actionIconContainer" style={{ backgroundColor: "#E1BEE7" }}>
                  <IoDocument size={24} color="#4A148C" />
                </div>
                <span className="actionText">Generate Reports</span>
              </a>

              <a 
                className="actionButton"
                onClick={() => navigate('/profile')}
              >
                <div className="actionIconContainer" style={{ backgroundColor: "#E1BEE7" }}>
                  <IoCalendar size={24} color="#4A148C" />
                </div>
                <span className="actionText">Schedule Meeting</span>
              </a>

              <a 
                className="actionButton"
                onClick={() => navigate('/notifications')}
              >
                <div className="actionIconContainer" style={{ backgroundColor: "#E1BEE7" }}>
                  <IoNotifications size={24} color="#4A148C" />
                </div>
                <span className="actionText">Send Announcements</span>
              </a>

              <a 
                className="actionButton"
                onClick={() => navigate('/supervisor-patient-health')}
              >
                <div className="actionIconContainer" style={{ backgroundColor: "#E1BEE7" }}>
                  <IoPulse size={24} color="#4A148C" />
                </div>
                <span className="actionText">Patient Health</span>
              </a>

              <a 
                className="actionButton"
                onClick={() => navigate('/supervisor-patient-tracking')}
              >
                <div className="actionIconContainer" style={{ backgroundColor: "#E1BEE7" }}>
                  <IoNavigate size={24} color="#4A148C" />
                </div>
                <span className="actionText">Track & Guide Patients</span>
              </a>

              <a 
                className="actionButton"
                onClick={() => navigate('/user-qr-code')}
              >
                <div className="actionIconContainer" style={{ backgroundColor: "#E1BEE7" }}>
                  <IoQrCode size={24} color="#4A148C" />
                </div>
                <span className="actionText">My QR Code</span>
              </a>

              <a 
                className="actionButton"
                onClick={() => navigate('/supervisor-caregiver-management')}
              >
                <div className="actionIconContainer" style={{ backgroundColor: "#E1BEE7" }}>
                  <IoPeopleCircle size={24} color="#4A148C" />
                </div>
                <span className="actionText">Manage Caregivers</span>
              </a>
            </div>
          </div>

          <div className="upcomingSection">
            <h3 className="sectionTitle">Today's Schedule</h3>
            <UpcomingList
              data={activityData}
              type="activities"
              emptyText="No activities scheduled for today"
              backgroundColor="#ffffff"
              maxItems={3}
              className="scheduleList"
            />
          </div>

          <div className="spacer"></div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default SupervisorDashboard;
