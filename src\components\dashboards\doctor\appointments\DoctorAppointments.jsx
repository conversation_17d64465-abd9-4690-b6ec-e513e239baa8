import React, { useState, useEffect } from 'react';
import { format, parse } from 'date-fns';
import { 
  Card, 
  Button, 
  Modal, 
  Divider, 
  Avatar, 
  TextField,
  CircularProgress
} from '@mui/material';
import { 
  CalendarToday, 
  AccessTime, 
  LocalHospital, 
  Person, 
  Description, 
  Close, 
  ChevronRight,
  Check,
  CheckCircle,
  Cancel,
  EventAvailable
} from '@mui/icons-material';
import { firebaseAppointmentsService } from '../../../../services/firebaseAppointmentsService';
import '../../../../styles/dashboards/doctor/appointments/doctorAppointments.css';

const ITEMS_PER_PAGE = 10;

const DoctorAppointments = () => {
  const [appointments, setAppointments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedAppointment, setSelectedAppointment] = useState(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [page, setPage] = useState(0);
  const [totalPages, setTotalPages] = useState(0);

  // États pour la reprogrammation
  const [showRescheduleModal, setShowRescheduleModal] = useState(false);
  const [newDate, setNewDate] = useState(new Date());
  const [newTime, setNewTime] = useState(new Date());
  const [notes, setNotes] = useState('');

  useEffect(() => {
    fetchAppointments();
  }, [page]);

  const fetchAppointments = async () => {
    try {
      setLoading(true);

      // Use Firebase service to get doctor appointments
      const options = {
        includePatientDetails: true,
        dateOrder: 'desc',  // Most recent appointments first
        timeOrder: 'asc'    // Earlier times first for same day
      };

      // Get appointments from Firebase
      const fetchedAppointments = await firebaseAppointmentsService.getDoctorAppointments(options);
      console.log(`Fetched ${fetchedAppointments.length} total appointments`);

      // Apply pagination manually
      const startIndex = page * ITEMS_PER_PAGE;
      const endIndex = startIndex + ITEMS_PER_PAGE;
      const paginatedAppointments = fetchedAppointments.slice(startIndex, endIndex);

      setAppointments(paginatedAppointments);

      // Calculate total pages
      const totalItems = fetchedAppointments.length;
      setTotalPages(Math.ceil(totalItems / ITEMS_PER_PAGE));

      console.log(`Showing appointments ${startIndex+1} to ${Math.min(endIndex, totalItems)} of ${totalItems}`);
    } catch (error) {
      console.error('Error fetching appointments from Firebase:', error);
      alert('Failed to fetch appointments');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    setPage(0);
    await fetchAppointments();
    setRefreshing(false);
  };

  const handleUpdateStatus = async (appointmentId, newStatus) => {
    try {
      setLoading(true);

      // Update appointment status in Firebase
      await firebaseAppointmentsService.updateAppointment(appointmentId, {
        status: newStatus,
        updatedAt: new Date().toISOString()
      });

      // Refresh the appointments list
      await fetchAppointments();

      alert('Appointment status updated successfully');
    } catch (error) {
      console.error('Error updating appointment status in Firebase:', error);
      alert('Failed to update appointment status');
    } finally {
      setLoading(false);
    }
  };

  // Fonction pour ouvrir le modal de reprogrammation
  const openRescheduleModal = (appointment) => {
    // Initialiser la date et l'heure avec les valeurs actuelles du rendez-vous
    if (appointment.date) {
      // Convertir la date du format YYYY-MM-DD en objet Date
      const [year, month, day] = appointment.date.split('-').map(num => parseInt(num));
      const appointmentDate = new Date(year, month - 1, day);
      setNewDate(appointmentDate);

      // Convertir l'heure en objet Date si disponible
      if (appointment.time) {
        try {
          const timeObj = parse(appointment.time, 'HH:mm', new Date());
          setNewTime(timeObj);
        } catch (error) {
          console.error('Error parsing time:', error);
          setNewTime(new Date());
        }
      }
    }

    // Initialiser les notes
    setNotes(`Rescheduled from ${appointment.date} at ${appointment.time || 'N/A'}`);

    // Ouvrir le modal
    setShowRescheduleModal(true);
  };

  // Fonction pour reprogrammer un rendez-vous
  const handleReschedule = async () => {
    if (!selectedAppointment) return;

    try {
      setLoading(true);

      // Formater la date au format YYYY-MM-DD
      const formattedDate = format(newDate, 'yyyy-MM-dd');

      // Formater l'heure au format HH:mm
      const formattedTime = format(newTime, 'HH:mm');

      // Mettre à jour le rendez-vous dans Firebase
      await firebaseAppointmentsService.updateAppointment(selectedAppointment.id, {
        date: formattedDate,
        time: formattedTime,
        status: 'pending', // Remettre le statut à "pending" après reprogrammation
        previousDate: selectedAppointment.date,
        previousTime: selectedAppointment.time,
        notes: notes,
        rescheduledAt: new Date().toISOString(),
        rescheduledBy: 'doctor', // Indiquer que c'est le médecin qui a reprogrammé
        updatedAt: new Date().toISOString()
      });

      // Fermer le modal
      setShowRescheduleModal(false);
      setShowDetailsModal(false);

      // Rafraîchir la liste des rendez-vous
      await fetchAppointments();

      // Afficher un message de succès
      alert('Appointment rescheduled successfully');
    } catch (error) {
      console.error('Error rescheduling appointment:', error);
      alert('Failed to reschedule appointment');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return '#FFA500';
      case 'confirmed':
        return '#4CAF50';
      case 'cancelled':
        return '#F44336';
      case 'completed':
        return '#2196F3';
      default:
        return '#757575';
    }
  };

  const renderAppointmentCard = (appointment) => (
    <div 
      key={appointment.id}
      className="card"
      onClick={() => {
        setSelectedAppointment(appointment);
        setShowDetailsModal(true);
      }}
    >
      <div className="cardGradient">
        <div className="cardHeader">
          <div className="dateContainer">
            <h2 className="dateTitle">{format(new Date(appointment.date), 'MMMM dd, yyyy')}</h2>
            <div className="statusContainer">
              <div className="statusDot" style={{ backgroundColor: getStatusColor(appointment.status) }}></div>
              <span className="statusText" style={{ color: getStatusColor(appointment.status) }}>
                {appointment.status.toUpperCase()}
              </span>
            </div>
          </div>
          <div 
            className="avatarIcon" 
            style={{ backgroundColor: getStatusColor(appointment.status) + '20' }}
          >
            <CalendarToday style={{ color: getStatusColor(appointment.status) }} />
          </div>
        </div>

        <div className="cardDivider"></div>

        <div className="cardDetails">
          <div className="detailRow">
            <AccessTime className="detailIcon" style={{ color: '#666', fontSize: 18 }} />
            <p className="detailText">
              {appointment.time || 'Time not specified'}
            </p>
          </div>

          <div className="detailRow">
            <LocalHospital className="detailIcon" style={{ color: '#666', fontSize: 18 }} />
            <p className="detailText">
              {appointment.type || 'In-person'}
            </p>
          </div>

          <div className="detailRow">
            <Person className="detailIcon" style={{ color: '#666', fontSize: 18 }} />
            <p className="detailText">
              {appointment.patient?.firstName && appointment.patient?.lastName
                ? `${appointment.patient.firstName} ${appointment.patient.lastName}`
                : appointment.patient?.displayName
                  ? appointment.patient.displayName
                  : appointment.patient?.name
                    ? appointment.patient.name
                    : 'Unknown Patient'}
            </p>
          </div>
        </div>

        <div className="cardFooter">
          <div className="viewDetailsButton">
            <span className="viewDetailsText">View Details</span>
            <ChevronRight style={{ color: '#4CAF50', fontSize: 16 }} />
          </div>
        </div>
      </div>
    </div>
  );

  // Rendu du modal de détails
  const renderDetailsModal = () => (
    <Modal
      open={showDetailsModal}
      onClose={() => setShowDetailsModal(false)}
      aria-labelledby="appointment-details-modal"
    >
      <div className="modal">
        {selectedAppointment && (
          <div>
            <div className="modalHeader">
              <div className="modalTitleContainer">
                <h2 className="modalTitle">Appointment Details</h2>
                <div 
                  className="modalStatusBadge" 
                  style={{ backgroundColor: getStatusColor(selectedAppointment.status) + '20' }}
                >
                  <span 
                    className="modalStatusText" 
                    style={{ color: getStatusColor(selectedAppointment.status) }}
                  >
                    {selectedAppointment.status.toUpperCase()}
                  </span>
                </div>
              </div>
              <div
                className="closeButton"
                onClick={() => setShowDetailsModal(false)}
              >
                <Close style={{ fontSize: 24, color: '#666' }} />
              </div>
            </div>

            <Divider className="divider" />

            <div className="modalDateTimeContainer">
              <div className="modalDateContainer">
                <CalendarToday style={{ color: '#4CAF50', fontSize: 24 }} className="modalIcon" />
                <div>
                  <div className="modalLabel">Date</div>
                  <div className="modalDateValue">
                    {format(new Date(selectedAppointment.date), 'MMMM dd, yyyy')}
                  </div>
                </div>
              </div>

              <div className="modalTimeContainer">
                <AccessTime style={{ color: '#2196F3', fontSize: 24 }} className="modalIcon" />
                <div>
                  <div className="modalLabel">Time</div>
                  <div className="modalTimeValue">
                    {selectedAppointment.time || 'Not specified'}
                  </div>
                </div>
              </div>
            </div>

            <div className="modalDetailsSection">
              <div className="modalDetailRow">
                <LocalHospital style={{ color: '#FF9800', fontSize: 20 }} className="modalDetailIcon" />
                <div className="modalDetailTextContainer">
                  <div className="modalDetailLabel">Type</div>
                  <div className="modalDetailValue">
                    {selectedAppointment.type || 'In-person'}
                  </div>
                </div>
              </div>

              <div className="modalDetailRow">
                <Person style={{ color: '#9C27B0', fontSize: 20 }} className="modalDetailIcon" />
                <div className="modalDetailTextContainer">
                  <div className="modalDetailLabel">Patient</div>
                  <div className="modalDetailValue">
                    {selectedAppointment.patient?.firstName && selectedAppointment.patient?.lastName
                      ? `${selectedAppointment.patient.firstName} ${selectedAppointment.patient.lastName}`
                      : selectedAppointment.patient?.displayName
                        ? selectedAppointment.patient.displayName
                        : selectedAppointment.patient?.name
                          ? selectedAppointment.patient.name
                          : 'Unknown Patient'}
                  </div>
                </div>
              </div>

              <div className="modalDetailRow">
                <Description style={{ color: '#607D8B', fontSize: 20 }} className="modalDetailIcon" />
                <div className="modalDetailTextContainer">
                  <div className="modalDetailLabel">Reason</div>
                  <div className="modalDetailValue">
                    {selectedAppointment.reason || 'Not specified'}
                  </div>
                </div>
              </div>
            </div>

            <Divider className="divider" />

            <div className="actionButtons">
              {selectedAppointment.status === 'pending' && (
                <>
                  <button
                    className="actionButton confirmButton"
                    onClick={() => handleUpdateStatus(selectedAppointment.id, 'confirmed')}
                  >
                    <Check style={{ marginRight: 5 }} /> Confirm
                  </button>
                  <button
                    className="actionButton cancelButton"
                    onClick={() => handleUpdateStatus(selectedAppointment.id, 'cancelled')}
                  >
                    <Cancel style={{ marginRight: 5 }} /> Cancel
                  </button>
                </>
              )}
              {selectedAppointment.status === 'confirmed' && (
                <>
                  <button
                    className="actionButton completeButton"
                    onClick={() => handleUpdateStatus(selectedAppointment.id, 'completed')}
                  >
                    <CheckCircle style={{ marginRight: 5 }} /> Mark as Completed
                  </button>
                  <button
                    className="actionButton rescheduleButton"
                    onClick={() => openRescheduleModal(selectedAppointment)}
                  >
                    <EventAvailable style={{ marginRight: 5 }} /> Reschedule
                  </button>
                </>
              )}
              {(selectedAppointment.status === 'pending' || selectedAppointment.status === 'cancelled') && (
                <button
                  className="actionButton rescheduleButton"
                  onClick={() => openRescheduleModal(selectedAppointment)}
                >
                  <EventAvailable style={{ marginRight: 5 }} /> Reschedule
                </button>
              )}
            </div>
          </div>
        )}
      </div>
    </Modal>
  );

  if (loading && !refreshing) {
    return (
      <div className="loadingContainer">
        <CircularProgress />
      </div>
    );
  }

  // Rendu du modal de reprogrammation
  const renderRescheduleModal = () => (
    <Modal
      open={showRescheduleModal}
      onClose={() => setShowRescheduleModal(false)}
      aria-labelledby="reschedule-appointment-modal"
    >
      <div className="modal">
        {selectedAppointment && (
          <div>
            <div className="modalHeader">
              <div className="modalTitleContainer">
                <h2 className="modalTitle">Reschedule Appointment</h2>
                <p className="modalSubtitle">
                  Current date: {selectedAppointment.date} at {selectedAppointment.time || 'N/A'}
                </p>
              </div>
              <div
                className="closeButton"
                onClick={() => setShowRescheduleModal(false)}
              >
                <Close style={{ fontSize: 24, color: '#666' }} />
              </div>
            </div>

            <Divider className="divider" />

            <div className="datePickerContainer">
              <div className="datePickerLabel">New Date:</div>
              <input
                type="date"
                value={format(newDate, 'yyyy-MM-dd')}
                onChange={(e) => setNewDate(new Date(e.target.value))}
                className="datePickerButton"
                min={format(new Date(), 'yyyy-MM-dd')}
              />
            </div>

            <div className="datePickerContainer">
              <div className="datePickerLabel">New Time:</div>
              <input
                type="time"
                value={format(newTime, 'HH:mm')}
                onChange={(e) => {
                  const [hours, minutes] = e.target.value.split(':');
                  const newTimeObj = new Date();
                  newTimeObj.setHours(parseInt(hours, 10));
                  newTimeObj.setMinutes(parseInt(minutes, 10));
                  setNewTime(newTimeObj);
                }}
                className="datePickerButton"
              />
            </div>

            <div className="notesContainer">
              <div className="datePickerLabel">Notes:</div>
              <TextField
                className="notesInput"
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                multiline
                rows={3}
                placeholder="Add notes about rescheduling"
                fullWidth
              />
            </div>

            <Divider className="divider" />

            <div className="actionButtons">
              <button
                className="actionButton"
                onClick={() => setShowRescheduleModal(false)}
                style={{ backgroundColor: '#9e9e9e' }}
              >
                Cancel
              </button>
              <button
                className="actionButton rescheduleButton"
                onClick={handleReschedule}
                disabled={loading}
              >
                {loading ? <CircularProgress size={20} color="inherit" /> : <EventAvailable style={{ marginRight: 5 }} />}
                Confirm Reschedule
              </button>
            </div>
          </div>
        )}
      </div>
    </Modal>
  );

  return (
    <div className="container">
      <div>
        {appointments.length === 0 ? (
          <p className="noAppointments">No appointments found</p>
        ) : (
          <>
            {appointments.map(renderAppointmentCard)}
            <div className="pagination">
              <div className="paginationControls">
                <button
                  className={`paginationButton ${page === 0 ? 'paginationButtonDisabled' : ''}`}
                  onClick={() => page > 0 && setPage(page - 1)}
                  disabled={page === 0}
                >
                  <span className="paginationButtonText">Previous</span>
                </button>
                <span className="paginationText">
                  Page {page + 1} of {Math.max(1, totalPages)}
                </span>
                <button
                  className={`paginationButton ${page >= totalPages - 1 ? 'paginationButtonDisabled' : ''}`}
                  onClick={() => page < totalPages - 1 && setPage(page + 1)}
                  disabled={page >= totalPages - 1}
                >
                  <span className="paginationButtonText">Next</span>
                </button>
              </div>
            </div>
          </>
        )}
      </div>
      {renderDetailsModal()}
      {renderRescheduleModal()}
    </div>
  );
};

export default DoctorAppointments;
