.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16px;
  z-index: 1000;
}

.modalContent {
  background-color: white;
  border-radius: 12px;
  padding: 20px;
  width: 100%;
  max-width: 500px;
  max-height: 80vh;
  overflow-y: auto;
}

.modalHeader {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e0e0e0;
}

.modalTitle {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.closeButton {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
}

.searchContainer {
  display: flex;
  flex-direction: row;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 0 12px;
  margin-bottom: 16px;
}

.searchIcon {
  margin-right: 8px;
}

.searchInput {
  flex: 1;
  padding: 12px 0;
  font-size: 16px;
  border: none;
  background: transparent;
  outline: none;
}

.clearButton {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px 0;
}

.loadingText {
  font-size: 16px;
  color: #757575;
  margin-top: 8px;
}

.spinner {
  width: 30px;
  height: 30px;
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: #4CAF50; /* Will be overridden by inline style */
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.patientList {
  padding-bottom: 16px;
}

.patientItem {
  display: flex;
  flex-direction: row;
  align-items: center;
  background-color: white;
  border-bottom: 1px solid #f0f0f0;
  padding: 12px 0;
  cursor: pointer;
}

.patientItem:hover {
  background-color: #f9f9f9;
}

.patientIcon {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  background-color: #E8F5E9;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 12px;
}

.patientInitials {
  font-size: 16px;
  font-weight: bold;
  color: #4CAF50;
}

.patientInfo {
  flex: 1;
}

.patientName {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.patientDetails {
  font-size: 14px;
  color: #757575;
}

.emptyContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px 0;
}

.emptyText {
  font-size: 16px;
  color: #757575;
  margin-top: 8px;
  text-align: center;
}
