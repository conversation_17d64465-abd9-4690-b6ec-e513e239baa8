.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #fff;
}

.camera-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #000;
  position: relative;
}

.capture-button {
  position: absolute;
  bottom: 40px;
  width: 70px;
  height: 70px;
  border-radius: 35px;
  background-color: #4A148C;
  border: none;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  font-size: 32px;
  cursor: pointer;
}

.capture-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.loading-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.loading-text {
  margin-top: 10px;
  font-size: 16px;
  color: #666;
}

.error-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.error-title {
  font-size: 20px;
  font-weight: bold;
  color: #333;
  margin-top: 20px;
  margin-bottom: 10px;
}

.error-text {
  font-size: 16px;
  color: #666;
  text-align: center;
  margin-bottom: 20px;
}

.camera-off-icon {
  font-size: 60px;
  color: #FF5252;
}

.scan-area {
  width: 70vw;
  max-width: 300px;
  height: 70vw;
  max-height: 300px;
  border: 2px solid #fff;
  border-radius: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.scanning-animation {
  position: absolute;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.scanning-text {
  color: #fff;
  margin-top: 10px;
  font-size: 16px;
}

.info-box {
  background-color: rgba(0, 0, 0, 0.7);
  padding: 16px;
  position: absolute;
  top: 40px;
  left: 20px;
  right: 20px;
  border-radius: 8px;
}

.info-text {
  color: #fff;
  text-align: center;
}

.footer {
  background-color: #fff;
  padding: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.button-row {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  width: 100%;
  gap: 10px;
}

.form-container {
  flex: 1;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.qr-icon {
  font-size: 80px;
  color: #4A148C;
  margin-bottom: 20px;
}

.title {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
  text-align: center;
}

.subtitle {
  font-size: 16px;
  color: #666;
  margin-bottom: 30px;
  text-align: center;
}

.input {
  width: 100%;
  height: 60px;
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 0 16px;
  font-size: 20px;
  letter-spacing: 2px;
  text-align: center;
  margin-bottom: 24px;
  border: none;
  outline: none;
}

.input::placeholder {
  color: #999;
}

.loader {
  margin: 20px 0;
  display: flex;
  justify-content: center;
}

.button {
  width: 100%;
  height: 56px;
  border-radius: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 16px;
  border: none;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  transition: all 0.2s ease;
}

.button-primary {
  background-color: #fff;
  border: 2px solid #4A148C;
  color: #4A148C;
}

.button-primary:hover {
  background-color: #4A148C;
  color: #fff;
}

.button-secondary {
  background-color: #f5f5f5;
  color: #333;
}

.button-secondary:hover {
  background-color: #e0e0e0;
}

.button-disabled {
  background-color: #f5f5f5;
  color: #999;
  cursor: not-allowed;
}

.button:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.cancel-button {
  padding: 12px;
  background: none;
  border: none;
  color: #666;
  font-size: 16px;
  cursor: pointer;
}

.cancel-button:hover {
  color: #333;
}

.cancel-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #4A148C;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.message {
  position: fixed;
  top: 20px;
  left: 20px;
  right: 20px;
  padding: 16px;
  border-radius: 8px;
  z-index: 1000;
  animation: slideDown 0.3s ease-out;
}

.message-success {
  background-color: #4CAF50;
  color: white;
}

.message-danger {
  background-color: #4A148C;
  color: white;
}

.message-warning {
  background-color: #FF9800;
  color: white;
}

.message-info {
  background-color: #2196F3;
  color: white;
}

.message-title {
  font-weight: bold;
  margin-bottom: 4px;
}

.message-description {
  font-size: 14px;
}

@keyframes slideDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@media (max-width: 768px) {
  .button-row {
    flex-direction: column;
  }
  
  .button {
    width: 100%;
    margin-bottom: 10px;
  }
  
  .form-container {
    padding: 15px;
  }
  
  .title {
    font-size: 20px;
  }
  
  .subtitle {
    font-size: 14px;
  }
}
