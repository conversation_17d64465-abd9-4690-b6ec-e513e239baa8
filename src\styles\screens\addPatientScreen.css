.container {
  display: flex;
  flex: 1;
  background-color: #fff;
  padding: 20px;
  justify-content: center;
  align-items: center;
}

.formContainer {
  width: 100%;
  max-width: 400px;
  align-items: center;
  padding: 30px;
  border-radius: 12px;
  background-color: #f9f9f9;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.icon {
  margin-bottom: 20px;
}

.title {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 12px;
  text-align: center;
}

.subtitle {
  font-size: 16px;
  color: #666;
  text-align: center;
  margin-bottom: 30px;
}

.input {
  width: 100%;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 16px;
  font-size: 18px;
  text-align: center;
  letter-spacing: 2px;
  margin-bottom: 8px;
  box-sizing: border-box;
}

.inputError {
  border-color: #ff6b6b;
  border-width: 2px;
}

.errorText {
  color: #ff6b6b;
  font-size: 14px;
  margin-bottom: 24px;
  text-align: center;
}

.helperText {
  color: #666;
  font-size: 14px;
  margin-bottom: 24px;
  text-align: center;
}

.button {
  width: 100%;
  padding: 15px 0;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border: none;
}

.buttonDisabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.buttonText {
  color: #fff;
  font-size: 16px;
  font-weight: bold;
}

.loadingContainer {
  display: flex;
  flex: 1;
  justify-content: center;
  align-items: center;
  background-color: #fff;
  flex-direction: column;
}

.loadingText {
  margin-top: 16px;
  font-size: 16px;
  color: #555;
}
