import { initializeApp } from 'firebase/app';
import {
  getAuth,
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  onAuthStateChanged,
  signOut,
  sendPasswordResetEmail,
  sendEmailVerification,
  updateProfile
} from 'firebase/auth';
import {
  getFirestore,
  collection,
  doc,
  getDoc,
  getDocs,
  setDoc,
  addDoc,
  where,
  query,
  orderBy,
  deleteDoc,
  updateDoc,
  serverTimestamp
} from 'firebase/firestore';
import { getStorage, ref, uploadBytes, getDownloadURL } from 'firebase/storage';

// Your web app's Firebase configuration
// For Firebase JS SDK v7.20.0 and later, measurementId is optional
const firebaseConfig = {
  apiKey: "AIzaSyCTiwd8kYvXa8k8UF2-EwHccLCpGrwl7bA",
  authDomain: "neurocare-f672b.firebaseapp.com",
  projectId: "neurocare-f672b",
  storageBucket: "neurocare-f672b.appspot.com",
  messagingSenderId: "131118025473",
  appId: "1:131118025473:web:e9c0933977afeefb7ee900",
  measurementId: "G-6VKR2FEPGJ"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase Auth for web
const auth = getAuth(app);

// Initialize Firestore
const db = getFirestore(app);

// Initialize Storage
const storage = getStorage(app);

// Export Firebase services and functions
export {
  auth,
  db,
  storage,
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  onAuthStateChanged,
  signOut,
  sendPasswordResetEmail,
  sendEmailVerification,
  updateProfile,
  collection,
  doc,
  getDoc,
  getDocs,
  setDoc,
  addDoc,
  where,
  query,
  orderBy,
  deleteDoc,
  updateDoc,
  serverTimestamp,
  ref,
  uploadBytes,
  getDownloadURL
};

// Default export
export default app;
