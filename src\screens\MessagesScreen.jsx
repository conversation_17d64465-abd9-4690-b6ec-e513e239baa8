import React, { useState, useEffect } from 'react';
import '../styles/screens/messagesScreen.css';
import { useAuth } from '../contexts/AuthContext';
import { ROLE_COLORS } from '../config/theme';
import { db, collection, query, where, getDocs } from '../config/firebase';

const MessagesScreen = () => {
  const { user } = useAuth();
  const [patients, setPatients] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedPatient, setSelectedPatient] = useState(null);
  const [isStartingCall, setIsStartingCall] = useState(false);

  const doctorColors = ROLE_COLORS.doctor;

  // Fetch patients linked to the doctor
  useEffect(() => {
    const fetchPatients = async () => {
      try {
        setLoading(true);
        console.log('Fetching patients for doctor:', user);

        // Query users collection for patients linked to this doctor
        const usersCollection = collection(db, 'users');
        const patientsQuery = query(
          usersCollection,
          where('role', '==', 'patient')
        );

        const querySnapshot = await getDocs(patientsQuery);
        console.log('Found', querySnapshot.size, 'patients in total');

        // Filter patients to get only those linked to this doctor
        const patientsList = [];
        querySnapshot.forEach((doc) => {
          const userData = doc.data();
          // Check if this patient is linked to the current doctor
          const linkedDoctors = userData.linkedDoctors || [];
          console.log('Patient:', doc.id, 'linkedDoctors:', linkedDoctors);
          console.log('Current doctor uid:', user.uid);

          if (linkedDoctors.includes(user.uid)) {
            patientsList.push({
              id: doc.id,
              firstName: userData.firstName || '',
              lastName: userData.lastName || '',
              email: userData.email || '',
              phone: userData.phone || '',
              lastConsultation: userData.lastVisit || new Date().toISOString().split('T')[0],
              status: Math.random() > 0.5 ? 'online' : 'offline', // Simulate online status randomly
              profileImage: userData.profileImage || null
            });
          }
        });

        console.log('Filtered patients linked to doctor:', patientsList.length);
        setPatients(patientsList);
      } catch (error) {
        console.error('Error fetching patients:', error);
        alert('Failed to load patients. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchPatients();
  }, [user.uid]);

  // Filter patients based on search query
  const filteredPatients = patients.filter(patient => {
    const fullName = `${patient.firstName} ${patient.lastName}`.toLowerCase();
    return fullName.includes(searchQuery.toLowerCase());
  });

  // Start video call with selected patient
  const startVideoCall = async (patient) => {
    if (isStartingCall) return;

    try {
      setIsStartingCall(true);

      // Generate a unique room name
      const roomName = `neurocare_${Date.now()}_${patient.id}`;

      // Navigate to the ChatRoom screen with the patient info and room name
      // Note: In React web, you would use React Router for navigation
      console.log('Starting video call with:', patient, 'Room:', roomName);
      // navigation.navigate('Chatroom', {
      //   patientInfo: patient,
      //   startVideoCall: true,
      //   roomName: roomName
      // });
    } catch (error) {
      console.error('Error starting video call:', error);
      alert('Failed to start video call. Please try again.');
    } finally {
      setIsStartingCall(false);
    }
  };

  // Start chat with selected patient
  const startChat = (patient) => {
    // Note: In React web, you would use React Router for navigation
    console.log('Starting chat with:', patient);
    // navigation.navigate('Chatroom', {
    //   patientInfo: patient
    // });
  };

  // Render patient item
  const renderPatientItem = (item) => {
    const isSelected = selectedPatient && selectedPatient.id === item.id;

    return (
      <div
        key={item.id}
        className={`patient-card ${isSelected ? 'selected-patient-card' : ''}`}
        onClick={() => setSelectedPatient(item)}
      >
        <div className="patient-avatar-container">
          <div className="patient-avatar-text">
            {item.firstName.charAt(0)}{item.lastName.charAt(0)}
          </div>
          <div className={`status-indicator ${item.status === 'online' ? 'status-online' : 'status-offline'}`} />
        </div>

        <div className="patient-info">
          <div className="patient-name">{item.firstName} {item.lastName}</div>
          <div className="patient-email">{item.email}</div>
          <div className="patient-phone">{item.phone || 'No phone number'}</div>
        </div>
      </div>
    );
  };

  return (
    <div className="container">
      <div className="search-container">
        <i className="search-icon">🔍</i>
        <input
          className="search-input"
          type="text"
          placeholder="Search patients..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
        />
      </div>

      {loading ? (
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <div className="loading-text">Loading patients...</div>
        </div>
      ) : (
        <div className="content-container">
          {filteredPatients.length > 0 ? (
            <div className="patient-list">
              {filteredPatients.map(renderPatientItem)}
            </div>
          ) : (
            <div className="empty-container">
              <i className="empty-icon">👥</i>
              <div className="empty-text">No patients found</div>
              <div className="empty-subtext">
                {searchQuery ? 'Try a different search term' : 'You don\'t have any patients linked to your account yet'}
              </div>
            </div>
          )}

          {selectedPatient && (
            <div className="selected-patient-overlay">
              <div className="patient-detail-container">
                <button
                  className="close-button"
                  onClick={() => setSelectedPatient(null)}
                >
                  ✕
                </button>

                <div className="patient-detail-header">
                  <div className="patient-detail-avatar">
                    <div className="patient-detail-avatar-text">
                      {selectedPatient.firstName.charAt(0)}{selectedPatient.lastName.charAt(0)}
                    </div>
                  </div>
                  <div>
                    <div className="patient-detail-name">
                      {selectedPatient.firstName} {selectedPatient.lastName}
                    </div>
                    <div className="patient-detail-info">{selectedPatient.email}</div>
                    <div className="patient-detail-info">
                      {selectedPatient.phone || 'No phone number'}
                    </div>
                    <div className="status-container">
                      <div className={`status-dot ${selectedPatient.status === 'online' ? 'online-dot' : 'offline-dot'}`} />
                      <div className="status-text">
                        {selectedPatient.status === 'online' ? 'Online' : 'Offline'}
                      </div>
                    </div>
                  </div>
                </div>

                <div className="action-buttons-container">
                  <button
                    className="action-button video-call-button"
                    onClick={() => startVideoCall(selectedPatient)}
                    disabled={isStartingCall}
                  >
                    <i className="button-icon">📹</i>
                    <span className="action-button-text">Start Video Call</span>
                  </button>

                  <button
                    className="action-button chat-button"
                    onClick={() => startChat(selectedPatient)}
                  >
                    <i className="button-icon">💬</i>
                    <span className="action-button-text">Start Chat</span>
                  </button>
                </div>

                <div className="info-container">
                  <div className="info-title">Last Consultation</div>
                  <div className="info-value">
                    {new Date(selectedPatient.lastConsultation).toLocaleDateString()}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default MessagesScreen;
