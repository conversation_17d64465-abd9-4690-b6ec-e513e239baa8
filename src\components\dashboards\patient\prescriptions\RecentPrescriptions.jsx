import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../../../../contexts/AuthContext';
import { firebasePrescriptionsService } from '../../../../services/firebasePrescriptionsService';
import '../../../../styles/dashboards/patient/prescriptions/recentPrescriptions.css';

const RecentPrescriptions = () => {
  const { user } = useAuth();
  const navigate = useNavigate(); // React Router's navigation hook
  const [prescriptions, setPrescriptions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    loadRecentPrescriptions();
  }, [user]);

  const loadRecentPrescriptions = async () => {
    if (!user) return;

    setLoading(true);
    setError(null);

    try {
      const patientId = user.uid;
      // Retrieve only prescriptions with "sent" status
      const allPrescriptions = await firebasePrescriptionsService.getPatientPrescriptions(patientId, 'sent');

      // Take only the 3 most recent prescriptions
      const recentPrescriptions = allPrescriptions.slice(0, 3);
      setPrescriptions(recentPrescriptions);
    } catch (error) {
      console.error('Error loading recent prescriptions:', error);
      setError('Failed to load prescriptions');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'Unknown date';

    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const renderPrescriptionItem = (item) => (
    <div 
      className="prescriptionItem"
      key={item.id}
      onClick={() => navigate('/patient/prescriptions', { state: { selectedPrescription: item } })}
    >
      <div className="prescriptionHeader">
        <div className="prescriptionIcon">
          <Ionicons name="document-text" size={20} color="#fff" />
        </div>
        <div className="prescriptionInfo">
          <div className="prescriptionTitle">Dr. {item.doctorName}</div>
          <div className="prescriptionMedication">
            {item.medications.length > 0
              ? `${item.medications.length} medication${item.medications.length > 1 ? 's' : ''}: ${item.medications.map(med => med.name).join(', ')}`
              : 'No medications'
            }
          </div>
          <div className="prescriptionDate">{formatDate(item.createdAt)}</div>
        </div>
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className="loadingContainer">
        <div className="spinner"></div>
        <div className="loadingText">Loading prescriptions...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="errorContainer">
        <Ionicons name="alert-circle-outline" size={24} color="#F44336" />
        <div className="errorText">{error}</div>
      </div>
    );
  }

  if (prescriptions.length === 0) {
    return (
      <div className="emptyContainer">
        <Ionicons name="document-text-outline" size={48} color="#BDBDBD" />
        <div className="emptyText">No prescriptions yet</div>
        <div className="emptySubText">
          Prescriptions sent by your doctors will appear here
        </div>
      </div>
    );
  }

  return (
    <div className="listContainer">
      {prescriptions.map(item => renderPrescriptionItem(item))}
    </div>
  );
};

export default RecentPrescriptions;
