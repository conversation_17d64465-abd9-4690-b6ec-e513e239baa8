import React, { useState } from "react";
import '../../styles/navigation/navigation.css';

function Navigation() {
  // Mobile menu functionality
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  return (
    <header className="header">
      <div className="logo-container">
        <h1 className="logo navigation-h1-logo">Neuro Care</h1>
        <p className="tagline navigation-p-tagline">Your neurological care companion</p>
      </div>

      <nav className={`navigation ${isMobileMenuOpen ? "mobile-menu-open" : ""}`}>
        <button className="nav-button">Home</button>
        <button className="nav-button">Features</button>
        <button className="nav-button">About</button>
        <button className="nav-button">Contact</button>
        <a href="/login" className="login-button-nav">
          Login
        </a>
      </nav>

      <button
        className={`mobile-menu-button ${isMobileMenuOpen ? "active" : ""}`}
        aria-label="Menu"
        onClick={toggleMobileMenu}
      >
        <span></span>
        <span></span>
        <span></span>
      </button>
    </header>
  );
}

export default Navigation;
