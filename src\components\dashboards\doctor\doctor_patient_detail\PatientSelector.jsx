import React, { useState, useEffect } from 'react';
import { IoIosSearch, IoIosClose, IoIosPerson, IoIosPeople, IoIosArrowDown, IoIosArrowForward } from 'react-icons/io';
import { collection, getDocs, query, where } from 'firebase/firestore';
import { db } from '../../../../../config/firebase';
import { ROLE_COLORS } from '../../../../../config/theme';
import '../../../../styles/dashboards/doctor/doctor_patient_detail/patientSelector.css';

// Function to get patients from Firebase
const getPatients = async () => {
  try {
    // Query users collection for patients
    const usersCollection = collection(db, 'users');
    const patientsQuery = query(
      usersCollection,
      where('role', '==', 'patient')
    );

    const querySnapshot = await getDocs(patientsQuery);

    // Format patient data
    const patients = querySnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        firstName: data.firstName || '',
        lastName: data.lastName || '',
        age: data.age || 0,
        condition: data.condition || 'Not specified',
        lastVisit: data.lastVisit || new Date().toISOString().split('T')[0],
        profileImage: data.profileImage || null
      };
    });

    return patients;
  } catch (error) {
    console.error('Error fetching patients from Firebase:', error);
    return [];
  }
};

const PatientSelector = ({ onSelectPatient }) => {
  const [modalVisible, setModalVisible] = useState(false);
  const [patients, setPatients] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedPatient, setSelectedPatient] = useState(null);
  const doctorColors = ROLE_COLORS.doctor;

  useEffect(() => {
    if (modalVisible) {
      fetchPatients();
    }
  }, [modalVisible]);

  const fetchPatients = async () => {
    setLoading(true);
    try {
      const patientsData = await getPatients();
      setPatients(patientsData);
    } catch (error) {
      console.error('Error fetching patients:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSelectPatient = (patient) => {
    setSelectedPatient(patient);
    setModalVisible(false);
    if (onSelectPatient) {
      onSelectPatient(patient);
    }
  };

  const filteredPatients = patients.filter(patient => {
    const fullName = `${patient.firstName} ${patient.lastName}`.toLowerCase();
    return fullName.includes(searchQuery.toLowerCase());
  });

  const renderPatientItem = (item) => (
    <div
      className="patientItem"
      key={item.id}
      onClick={() => handleSelectPatient(item)}
    >
      <div className="avatarContainer">
        <span className="avatarText">
          {item.firstName.charAt(0)}{item.lastName.charAt(0)}
        </span>
      </div>
      <div className="patientInfo">
        <div className="patientName">{item.firstName} {item.lastName}</div>
        <div className="patientDetails">
          {item.age} years • {item.condition}
        </div>
        <div className="lastVisit">
          Last visit: {new Date(item.lastVisit).toLocaleDateString()}
        </div>
      </div>
      <IoIosArrowForward size={24} color="#ccc" />
    </div>
  );

  return (
    <div className="container">
      <div
        className="selectorButton"
        onClick={() => setModalVisible(true)}
      >
        {selectedPatient ? (
          <div className="selectedPatientContainer">
            <div className="miniAvatarContainer">
              <span className="miniAvatarText">
                {selectedPatient.firstName.charAt(0)}{selectedPatient.lastName.charAt(0)}
              </span>
            </div>
            <span className="selectedPatientName">
              {selectedPatient.firstName} {selectedPatient.lastName}
            </span>
          </div>
        ) : (
          <div className="placeholderContainer">
            <IoIosPerson size={20} color={doctorColors.primary} />
            <span className="placeholderText">Select a patient</span>
          </div>
        )}
        <IoIosArrowDown size={20} color="#666" />
      </div>

      {modalVisible && (
        <div className="modalOverlay">
          <div className="modalContainer">
            <div className="modalHeader">
              <div className="modalTitle">Select Patient</div>
              <div
                className="closeButton"
                onClick={() => setModalVisible(false)}
              >
                <IoIosClose size={24} color="#333" />
              </div>
            </div>

            <div className="searchContainer">
              <IoIosSearch size={20} color="#999" className="searchIcon" />
              <input
                className="searchInput"
                placeholder="Search patients..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>

            {loading ? (
              <div className="loadingContainer">
                <div className="spinner" style={{ color: doctorColors.primary }}></div>
                <div className="loadingText">Loading patients...</div>
              </div>
            ) : (
              <div className="patientList">
                {filteredPatients.length > 0 ? (
                  filteredPatients.map(patient => renderPatientItem(patient))
                ) : (
                  <div className="emptyContainer">
                    <IoIosPeople size={48} color="#ccc" />
                    <div className="emptyText">
                      {searchQuery ? 'No patients match your search' : 'No patients found'}
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default PatientSelector;
