import React, { useState, useEffect } from 'react';
import { useNavigate, useParams, useLocation } from 'react-router-dom';
import { useAuth } from '../../../../contexts/AuthContext';
import { useMedications } from '../../../../contexts/MedicationContext';
import '../../../../styles/dashboards/patient/medications/medicationDetail.css';

// Import icons from a web-compatible icon library
import { IoMedkit, IoArrowBack, IoTimeOutline, IoCheckmarkCircle, IoCloseCircle, IoTime, IoAdd, IoCheckmark, IoClose, IoAlertCircle } from 'react-icons/io5';

const MedicationDetail = () => {
  const { user } = useAuth();
  const {
    medications,
    reminders,
    loading,
    error,
    deleteMedication,
    updateReminderStatus
  } = useMedications();
  
  const navigate = useNavigate();
  const { id } = useParams();
  
  const [medication, setMedication] = useState(null);
  const [medicationReminders, setMedicationReminders] = useState([]);
  const [localLoading, setLocalLoading] = useState(false);

  useEffect(() => {
    // Find the medication in the context
    if (medications && medications.length > 0) {
      const med = medications.find(m => m.id === id);
      if (med) {
        setMedication(med);
      }
    }
  }, [medications, id]);

  useEffect(() => {
    // Filter reminders for this medication
    if (reminders && reminders.length > 0 && id) {
      const medReminders = reminders.filter(r => r.medicationId === id);
      setMedicationReminders(medReminders);
    } else {
      setMedicationReminders([]);
    }
  }, [reminders, id]);

  const handleDeleteMedication = async () => {
    if (window.confirm("Are you sure you want to delete this medication? This will also delete all reminders for this medication.")) {
      setLocalLoading(true);
      try {
        await deleteMedication(id);
        navigate(-1); // Go back to previous page
      } catch (error) {
        console.error('Error deleting medication:', error);
        alert('Failed to delete medication');
      } finally {
        setLocalLoading(false);
      }
    }
  };

  const handleAddReminder = () => {
    // Navigate to add reminder page
    navigate(`/medications/add-reminder/${id}`);
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  const handleGoBack = () => {
    navigate(-1); // Go back to previous page
  };

  if (loading || localLoading) {
    return (
      <div className="loadingContainer">
        <div className="spinner"></div>
        <p className="loadingText">Loading medication details...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="errorContainer">
        <IoAlertCircle size={48} color="#F44336" />
        <p className="errorText">{error}</p>
        <button className="backButton" onClick={handleGoBack}>
          <span className="backButtonText">Go Back</span>
        </button>
      </div>
    );
  }

  if (!medication) {
    return (
      <div className="errorContainer">
        <IoAlertCircle size={48} color="#F44336" />
        <p className="errorText">Medication not found</p>
        <button className="backButton" onClick={handleGoBack}>
          <span className="backButtonText">Go Back</span>
        </button>
      </div>
    );
  }

  return (
    <div className="container">
      <div className="header">
        <button className="backButton" onClick={handleGoBack}>
          <IoArrowBack size={24} color="#333" />
        </button>
        <h1 className="headerTitle">Medication Details</h1>
        <div style={{ width: 24 }}></div>
      </div>

      <div className="content">
        <div className="medicationCard">
          <div className="medicationHeader">
            <IoMedkit size={32} color="#4285F4" />
            <div className="medicationTitleContainer">
              <h2 className="medicationName">{medication.name}</h2>
              <p className="medicationDosage">{medication.dosage}</p>
            </div>
          </div>

          <div className="detailsSection">
            <div className="detailRow">
              <p className="detailLabel">Frequency:</p>
              <p className="detailValue">{medication.frequency || 'Not specified'}</p>
            </div>

            <div className="detailRow">
              <p className="detailLabel">Instructions:</p>
              <p className="detailValue">{medication.instructions || 'No special instructions'}</p>
            </div>

            <div className="detailRow">
              <p className="detailLabel">Added on:</p>
              <p className="detailValue">{formatDate(medication.createdAt)}</p>
            </div>
          </div>
        </div>

        <div className="remindersSection">
          <div className="sectionHeader">
            <h3 className="sectionTitle">Reminders</h3>
            <button className="addButton" onClick={handleAddReminder}>
              <IoAdd size={20} color="#4285F4" />
              <span className="addButtonText">Add Reminder</span>
            </button>
          </div>

          {medicationReminders.length === 0 ? (
            <div className="emptyReminders">
              <IoTimeOutline size={48} color="#BDBDBD" />
              <p className="emptyRemindersText">No reminders set</p>
              <p className="emptyRemindersSubtext">
                Add reminders to help you remember when to take your medication
              </p>
            </div>
          ) : (
            medicationReminders.map((reminder) => (
              <div key={reminder.id} className="reminderItem">
                <div className="reminderHeader">
                  {reminder.status === 'completed' ? (
                    <IoCheckmarkCircle size={24} color="#4CAF50" />
                  ) : reminder.status === 'missed' ? (
                    <IoCloseCircle size={24} color="#F44336" />
                  ) : (
                    <IoTime size={24} color="#4285F4" />
                  )}
                  <p className="reminderTime">{formatDate(reminder.scheduledTime)}</p>
                  <div
                    className="statusBadge"
                    style={{
                      backgroundColor:
                        reminder.status === 'completed' ? '#E8F5E9' :
                        reminder.status === 'missed' ? '#FFEBEE' : '#E3F2FD'
                    }}
                  >
                    <span
                      className="statusText"
                      style={{
                        color:
                          reminder.status === 'completed' ? '#4CAF50' :
                          reminder.status === 'missed' ? '#F44336' : '#4285F4'
                      }}
                    >
                      {reminder.status.charAt(0).toUpperCase() + reminder.status.slice(1)}
                    </span>
                  </div>
                </div>

                {reminder.instructions && (
                  <p className="reminderInstructions">{reminder.instructions}</p>
                )}

                {reminder.status === 'scheduled' && (
                  <div className="reminderActions">
                    <button
                      className="reminderAction takenAction"
                      onClick={() => updateReminderStatus(reminder.id, 'completed')}
                    >
                      <IoCheckmark size={16} color="#fff" />
                      <span className="actionText">Mark as Taken</span>
                    </button>
                    <button
                      className="reminderAction missedAction"
                      onClick={() => updateReminderStatus(reminder.id, 'missed')}
                    >
                      <IoClose size={16} color="#fff" />
                      <span className="actionText">Skip</span>
                    </button>
                  </div>
                )}
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

export default MedicationDetail;
