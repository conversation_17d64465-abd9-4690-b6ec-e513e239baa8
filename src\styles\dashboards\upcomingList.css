/* Upcoming List CSS */
.upcoming-list-container {
  border-radius: 12px;
  overflow: hidden;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.upcoming-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.upcoming-list-title {
  font-size: 18px;
  font-weight: 600;
  color: #212121;
  margin: 0;
}

.view-all-button {
  display: flex;
  align-items: center;
  background: none;
  border: none;
  color: #1976D2;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.view-all-button:hover {
  background-color: rgba(25, 118, 210, 0.1);
}

.upcoming-list-items {
  display: flex;
  flex-direction: column;
}

/* List Item Styles */
.list-item {
  background-color: #f9f9f9;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
}

.list-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.list-item-content {
  padding: 16px;
}

.list-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.list-item-title {
  font-size: 16px;
  font-weight: 600;
  color: #212121;
  margin: 0;
}

.list-item-status {
  font-size: 12px;
  font-weight: 500;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
}

.list-item-description {
  font-size: 14px;
  color: #757575;
  margin: 0 0 12px 0;
}

.list-item-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.list-item-time {
  font-size: 12px;
  color: #757575;
}

.list-item-icon {
  color: #757575;
}

/* Empty State Styles */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px 16px;
  text-align: center;
}

.empty-text {
  font-size: 16px;
  color: #9e9e9e;
  margin: 16px 0;
}

.empty-action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #1976D2;
  color: white;
  border: none;
  border-radius: 20px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.empty-action-button:hover {
  background-color: #1565C0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .upcoming-list-container {
    padding: 12px;
  }
  
  .upcoming-list-title {
    font-size: 16px;
  }
  
  .list-item-title {
    font-size: 14px;
  }
  
  .list-item-description {
    font-size: 12px;
  }
}
