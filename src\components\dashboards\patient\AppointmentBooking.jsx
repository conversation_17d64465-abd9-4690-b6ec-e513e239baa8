import React, { useState, useEffect } from 'react';
import { format } from 'date-fns';
import axios from 'axios';
import { API_URL } from '../../../config/constants';
import '../../../styles/dashboards/patient/appointmentBooking.css';

// Importation des composants Material-UI (ou autre bibliothèque UI pour React web)
import { 
  Button, 
  TextField, 
  Card, 
  CardContent, 
  Typography, 
  CircularProgress, 
  Modal, 
  List, 
  ListItem, 
  Divider,
  Box
} from '@mui/material';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';

const AppointmentBooking = ({ doctorId, token, onBookingComplete }) => {
  const [loading, setLoading] = useState(false);
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [selectedTime, setSelectedTime] = useState(new Date());
  const [availableSlots, setAvailableSlots] = useState([]);
  const [reason, setReason] = useState('');
  const [type, setType] = useState('in-person');
  const [showTimeModal, setShowTimeModal] = useState(false);
  const [showTypeModal, setShowTypeModal] = useState(false);

  const appointmentTypes = [
    { id: 'in-person', label: 'In-Person Visit' },
    { id: 'video', label: 'Video Call' },
    { id: 'phone', label: 'Phone Call' },
  ];

  useEffect(() => {
    if (selectedDate) {
      fetchAvailableSlots();
    }
  }, [selectedDate]);

  const fetchAvailableSlots = async () => {
    try {
      setLoading(true);
      const formattedDate = format(selectedDate, 'yyyy-MM-dd');
      const response = await axios.get(
        `${API_URL}/api/appointments/available-slots/${doctorId}?date=${formattedDate}`,
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );
      setAvailableSlots(response.data);
    } catch (error) {
      console.error('Error fetching available slots:', error);
      alert('Failed to fetch available time slots');
    } finally {
      setLoading(false);
    }
  };

  const handleDateChange = (date) => {
    setSelectedDate(date);
  };

  const handleTimeSelect = (time) => {
    setSelectedTime(new Date(`2000-01-01T${time}`));
    setShowTimeModal(false);
  };

  const handleBooking = async () => {
    if (!selectedDate || !selectedTime || !type) {
      alert('Please fill in all required fields');
      return;
    }

    try {
      setLoading(true);
      const appointmentData = {
        doctorId,
        date: format(selectedDate, 'yyyy-MM-dd'),
        time: format(selectedTime, 'HH:mm'),
        reason,
        type,
      };

      const response = await axios.post(
        `${API_URL}/api/appointments/create`,
        appointmentData,
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );

      alert('Appointment request sent successfully. The doctor will confirm your appointment soon.');
      if (onBookingComplete) onBookingComplete();
    } catch (error) {
      console.error('Error booking appointment:', error);
      alert(error.response?.data?.error || 'Failed to book appointment');
    } finally {
      setLoading(false);
    }
  };

  const modalStyle = {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: 400,
    bgcolor: 'background.paper',
    boxShadow: 24,
    p: 4,
    borderRadius: 2,
    maxHeight: '80%',
    overflow: 'auto'
  };

  return (
    <div className="container">
      <Card className="card">
        <CardContent>
          <Typography variant="h5" component="div">Book an Appointment</Typography>

          <div className="dateTimeContainer">
            <DatePicker
              selected={selectedDate}
              onChange={handleDateChange}
              minDate={new Date()}
              className="dateButton"
              customInput={
                <Button variant="outlined" fullWidth>
                  {format(selectedDate, 'MMMM dd, yyyy')}
                </Button>
              }
            />

            <Button 
              variant="outlined" 
              onClick={() => setShowTimeModal(true)}
              className="timeButton"
            >
              {format(selectedTime, 'hh:mm a')}
            </Button>
          </div>

          <Modal
            open={showTimeModal}
            onClose={() => setShowTimeModal(false)}
          >
            <Box sx={modalStyle}>
              <Typography variant="h6">Select Time</Typography>
              <div className="timeSlotsContainer">
                <List>
                  {availableSlots.map((slot, index) => (
                    <React.Fragment key={slot}>
                      <ListItem button onClick={() => handleTimeSelect(slot)}>
                        <Typography>{slot}</Typography>
                      </ListItem>
                      {index < availableSlots.length - 1 && <Divider />}
                    </React.Fragment>
                  ))}
                </List>
              </div>
            </Box>
          </Modal>

          <Button
            variant="outlined"
            onClick={() => setShowTypeModal(true)}
            className="typeButton"
            fullWidth
          >
            {appointmentTypes.find(t => t.id === type)?.label || 'Select Type'}
          </Button>

          <Modal
            open={showTypeModal}
            onClose={() => setShowTypeModal(false)}
          >
            <Box sx={modalStyle}>
              <Typography variant="h6">Select Appointment Type</Typography>
              <List>
                {appointmentTypes.map((appointmentType, index) => (
                  <React.Fragment key={appointmentType.id}>
                    <ListItem 
                      button 
                      onClick={() => {
                        setType(appointmentType.id);
                        setShowTypeModal(false);
                      }}
                    >
                      <Typography>{appointmentType.label}</Typography>
                    </ListItem>
                    {index < appointmentTypes.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            </Box>
          </Modal>

          <TextField
            label="Reason for Visit"
            value={reason}
            onChange={(e) => setReason(e.target.value)}
            multiline
            rows={3}
            className="input"
            fullWidth
          />

          <Button
            variant="contained"
            onClick={handleBooking}
            className="bookButton"
            disabled={loading}
            fullWidth
          >
            {loading ? <CircularProgress size={24} /> : 'Book Appointment'}
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};

export default AppointmentBooking;
