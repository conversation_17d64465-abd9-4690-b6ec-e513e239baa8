import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { useAuth } from '../../../../contexts/AuthContext';
import { useAppointments } from '../../../../contexts/AppointmentContext';
import { usersAPI } from '../../../../config/api';
import { firebaseAppointmentsService } from '../../../../services/firebaseAppointmentsService';
import AsyncStorage from '@react-native-async-storage/async-storage';
import '../../../../styles/dashboards/patient/appointments/appointments.css';

// Import icons
import {
  IoPersonOutline,
  IoMedicalOutline,
  IoTimeOutline,
  IoDocumentTextOutline,
  IoRefreshCircle,
  IoCalendarOutline,
  IoCloseCircleOutline,
  IoChevronForward,
  IoEyeOutline,
  IoEyeOffOutline,
  IoAddCircle,
  IoClose,
  IoCheckmarkCircle,
  IoCloseCircle
} from 'react-icons/io5';

const Appointments = () => {
  const location = useLocation();
  const { user } = useAuth();
  const {
    appointments: contextAppointments,
    loading: appointmentsLoading,
    createAppointment,
    cancelAppointment,
    rescheduleAppointment,
    fetchAppointments,
    getAvailableTimeSlots
  } = useAppointments();

  // La référence à l'animation a été supprimée
  const [appointments, setAppointments] = useState([]);
  const [filteredAppointments, setFilteredAppointments] = useState([]);
  const [showCancelled, setShowCancelled] = useState(false);
  const [loading, setLoading] = useState(true);
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedDoctor, setSelectedDoctor] = useState(null);
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [selectedTime, setSelectedTime] = useState(null);
  const [availabilityStatus, setAvailabilityStatus] = useState(null);
  const [availabilityChecking, setAvailabilityChecking] = useState(false);
  const [doctors, setDoctors] = useState([]);
  const [isRescheduling, setIsRescheduling] = useState(false);
  const [appointmentToReschedule, setAppointmentToReschedule] = useState(null);
  const [reasonForVisit, setReasonForVisit] = useState('');
  const [suggestedTimeSlots, setSuggestedTimeSlots] = useState([]);

  // Pour le sélecteur de date personnalisé
  const [datePickerVisible, setDatePickerVisible] = useState(false);
  const [tempSelectedDate, setTempSelectedDate] = useState(new Date());

  // Pour les détails du rendez-vous
  const [detailsModalVisible, setDetailsModalVisible] = useState(false);
  const [selectedAppointment, setSelectedAppointment] = useState(null);
  const [cancelReasonModalVisible, setCancelReasonModalVisible] = useState(false);
  const [cancellationReason, setCancellationReason] = useState('');
  const [appointmentToCancel, setAppointmentToCancel] = useState(null);

  // Vérifier si nous devons ouvrir le modal de demande de rendez-vous
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const openRequestModal = params.get('openRequestModal') === 'true';
    const selectedDoctorId = params.get('selectedDoctorId');

    if (openRequestModal) {
      setModalVisible(true);

      // Si un docteur spécifique est sélectionné, le définir
      if (selectedDoctorId && doctors.length > 0) {
        const doctor = doctors.find(doc => doc.id === selectedDoctorId);
        if (doctor) {
          setSelectedDoctor(doctor);
        }
      }
    }
  }, [location, doctors]);

  // Load appointments and doctors from API
  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      try {
        // Get linked doctors from API
        const linkedDoctors = await usersAPI.getLinkedUsers('doctors');

        if (linkedDoctors && linkedDoctors.length > 0) {
          // Format doctors data for the component
          const formattedDoctors = linkedDoctors.map(doctor => ({
            id: doctor.uid,
            name: doctor.displayName ? `Dr. ${doctor.displayName}` : 'Dr.',
            specialty: doctor.specialty || 'General Practitioner'
          }));
          setDoctors(formattedDoctors);
        }

        // Fetch appointments if they haven't been loaded yet
        if (appointments.length === 0 && contextAppointments.length === 0) {
          await fetchAppointments();
        }

        // Initialize filtered appointments
        if (contextAppointments.length > 0) {
          filterAppointments(contextAppointments, showCancelled);
        }
      } catch (error) {
        console.error('Error loading doctors:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  // Use appointments from context and filter them
  useEffect(() => {
    if (contextAppointments) {
      setAppointments(contextAppointments);

      // Filter appointments based on showCancelled flag
      filterAppointments(contextAppointments, showCancelled);
    }
  }, [contextAppointments, showCancelled]);

  // Function to filter appointments
  const filterAppointments = (appointmentsList, includeCancelled) => {
    if (!appointmentsList) return;

    const filtered = appointmentsList.filter(appointment => {
      const status = appointment.status ? appointment.status.toLowerCase() : 'pending';

      // If includeCancelled is true, show all appointments
      // Otherwise, only show pending and confirmed appointments
      return includeCancelled || (status !== 'cancelled');
    });

    setFilteredAppointments(filtered);
  };

  // Toggle showing cancelled appointments
  const toggleShowCancelled = () => {
    setShowCancelled(prev => !prev);
  };

  // Show appointment request modal
  const handleRequestAppointment = () => {
    setModalVisible(true);
  };

  const handleCloseModal = () => {
    // Enregistrer si nous étions en train de replanifier
    const wasRescheduling = isRescheduling;

    // Réinitialiser tous les états
    setModalVisible(false);
    setSelectedDoctor(null);
    setSelectedDate(new Date());
    setSelectedTime(null);
    setAvailabilityStatus(null);
    setIsRescheduling(false);
    setAppointmentToReschedule(null);
    setReasonForVisit('');
    setSuggestedTimeSlots([]);

    // Force a complete refresh of appointments when closing the modal
    // Afficher un indicateur de chargement
    setLoading(true);

    // First, clear the current appointments to force a complete refresh
    setAppointments([]);
    setFilteredAppointments([]);

    // Attendre un peu plus longtemps si nous étions en train de replanifier
    const delay = wasRescheduling ? 1000 : 500;

    // Then fetch new appointments from Firebase
    setTimeout(async () => {
      try {
        // Forcer une nouvelle récupération des rendez-vous depuis Firebase
        await fetchAppointments();

        // Force another refresh after a short delay to ensure all updates are applied
        setTimeout(async () => {
          try {
            // Vider à nouveau les rendez-vous pour forcer une récupération complète
            setAppointments([]);
            setFilteredAppointments([]);

            // Récupérer à nouveau les rendez-vous
            await fetchAppointments();

            // Si nous étions en train de replanifier, faire une troisième tentative
            if (wasRescheduling) {
              setTimeout(async () => {
                try {
                  await fetchAppointments();
                } catch (error) {
                  console.error('Error in third refresh:', error);
                } finally {
                  setLoading(false);
                }
              }, 1500);
            } else {
              setLoading(false);
            }
          } catch (error) {
            console.error('Error in second refresh:', error);
            setLoading(false);
          }
        }, 1500);
      } catch (error) {
        console.error('Error refreshing appointments:', error);
        setLoading(false);
      }
    }, delay);
  };

  // Pour ouvrir le modal de sélection de date
  const openDatePicker = () => {
    setTempSelectedDate(selectedDate);
    setDatePickerVisible(true);
  };

  // Pour confirmer la date sélectionnée
  const confirmDate = () => {
    setSelectedDate(tempSelectedDate);
    setDatePickerVisible(false);
    setAvailabilityStatus(null); // Reset availability when date changes
    setSuggestedTimeSlots([]); // Reset suggested time slots
  };

  // Pour annuler la sélection de date
  const cancelDateSelection = () => {
    setDatePickerVisible(false);
  };

  // Pour générer les dates disponibles (les 30 prochains jours)
  const getAvailableDates = () => {
    const dates = [];
    const today = new Date();

    for (let i = 0; i < 30; i++) {
      const date = new Date(today);
      date.setDate(today.getDate() + i);
      dates.push(date);
    }

    return dates;
  };

  // Vérifier la disponibilité de la date et l'heure sélectionnées
  const checkAvailability = async () => {
    if (!selectedDoctor && !isRescheduling) {
      alert('Please select a doctor first');
      return;
    }

    if (!selectedDate || !selectedTime) {
      alert('Please select a date and time');
      return;
    }

    setAvailabilityChecking(true);
    setAvailabilityStatus(null);
    setSuggestedTimeSlots([]);

    try {
      // Get the doctor ID
      const doctorId = selectedDoctor ? selectedDoctor.id :
                      (appointmentToReschedule ? appointmentToReschedule.doctorId : null);

      if (!doctorId) {
        throw new Error('Doctor ID not found');
      }

      // Format the date for the API call
      const formattedDate = formatDateForList(selectedDate);

      // Get available time slots from the API
      const availableSlots = await getAvailableTimeSlots(doctorId, formattedDate);

      // Check if the selected time is in the available slots
      const isTimeAvailable = availableSlots.includes(selectedTime);

      if (isTimeAvailable) {
        // Time slot is available
        setAvailabilityStatus('available');
        alert('The selected time slot is available. You can proceed with your appointment request.');
      } else {
        // Time slot is not available
        setAvailabilityStatus('unavailable');

        // Set suggested time slots
        setSuggestedTimeSlots(availableSlots);

        alert('The selected time slot is not available. Please choose from the suggested available times.');
      }
    } catch (error) {
      console.error('Error in checkAvailability:', error);

      // In case of network error, try to generate some default time slots
      const defaultSlots = [];
      const startHour = 9; // 9 AM
      const endHour = 17; // 5 PM

      for (let hour = startHour; hour < endHour; hour++) {
        defaultSlots.push(`${hour.toString().padStart(2, '0')}:00`);
        defaultSlots.push(`${hour.toString().padStart(2, '0')}:30`);
      }

      // Filter out the selected time from default slots
      const filteredDefaultSlots = defaultSlots.filter(slot => slot !== selectedTime);

      setSuggestedTimeSlots(filteredDefaultSlots);
      setAvailabilityStatus('unavailable');

      alert('Could not check availability due to a connection error. Please choose from the suggested time slots or try again later.');
    } finally {
      setAvailabilityChecking(false);
    }
  };

  // Soumettre la demande de rendez-vous
  const submitAppointmentRequest = async () => {
    if (!selectedDoctor) {
      alert('Please select a doctor');
      return;
    }

    if (!selectedTime) {
      alert('Please select an appointment time');
      return;
    }

    // Check availability before submitting
    if (availabilityStatus !== 'available') {
      // If we haven't checked availability yet, do it now
      await checkAvailability();

      // After checking, if still not available, don't proceed
      if (availabilityStatus !== 'available') {
        alert('Please check the availability of your selected time slot before proceeding.');
        return;
      }
    }

    // Use the selected time
    const timeString = selectedTime;

    try {
      if (isRescheduling && appointmentToReschedule) {
        setLoading(true);

        try {
          // Use the dedicated reschedule function
          try {
            await rescheduleAppointment(
              appointmentToReschedule.id,
              formatDateForList(selectedDate),
              timeString,
              `Rescheduled from ${appointmentToReschedule.date} at ${appointmentToReschedule.time}`
            );

            // Refresh appointments from server
            await fetchAppointments();

            // Create an updated appointment object to ensure UI is updated immediately
            const updatedAppointment = {
              ...appointmentToReschedule,
              date: formatDateForList(selectedDate),
              time: timeString,
              status: 'pending',
              previousDate: appointmentToReschedule.date,
              previousTime: appointmentToReschedule.time,
              notes: `Rescheduled from ${appointmentToReschedule.date} at ${appointmentToReschedule.time} to ${formatDateForList(selectedDate)} at ${timeString}`,
              rescheduledAt: new Date().toISOString(),
              updatedAt: new Date().toISOString()
            };

            // Update the appointments in state directly to ensure immediate UI update
            setAppointments(prev =>
              prev.map(app => app.id === appointmentToReschedule.id ? updatedAppointment : app)
            );

            // Update filtered appointments directly
            setFilteredAppointments(prev =>
              prev.map(app => app.id === appointmentToReschedule.id ? updatedAppointment : app)
            );

            // If we're not showing cancelled appointments, filter them out again
            if (!showCancelled && appointments.length > 0) {
              filterAppointments(appointments, showCancelled);
            }
          } catch (apiError) {
            console.error('Error rescheduling appointment:', apiError);

            // If the reschedule function fails, try a direct update to Firebase
            await firebaseAppointmentsService.updateAppointment(appointmentToReschedule.id, {
              ...appointmentToReschedule,
              date: formatDateForList(selectedDate),
              time: timeString,
              status: 'pending',
              previousDate: appointmentToReschedule.date,
              previousTime: appointmentToReschedule.time,
              rescheduledAt: new Date().toISOString(),
              rescheduledBy: user?.uid || 'local-user',
              updatedAt: new Date().toISOString()
            });

            // Update the appointments in state directly
            const updatedAppointment = {
              ...appointmentToReschedule,
              date: formatDateForList(selectedDate),
              time: timeString,
              status: 'pending',
              previousDate: appointmentToReschedule.date,
              previousTime: appointmentToReschedule.time,
              notes: `Rescheduled from ${appointmentToReschedule.date} at ${appointmentToReschedule.time} to ${formatDateForList(selectedDate)} at ${timeString}`,
              rescheduledAt: new Date().toISOString(),
              updatedAt: new Date().toISOString()
            };

            setAppointments(prev =>
              prev.map(app => app.id === appointmentToReschedule.id ? updatedAppointment : app)
            );

            // Update filtered appointments if needed
            if (filteredAppointments.length > 0) {
              setFilteredAppointments(prev =>
                prev.map(app => app.id === appointmentToReschedule.id ? updatedAppointment : app)
              );
            }
          }

          // Force a complete refresh of appointments from Firebase
          await fetchAppointments();

          // Afficher un message de confirmation
          alert(`Your appointment with ${selectedDoctor.name} has been rescheduled:\n\n` +
                `FROM: ${appointmentToReschedule.date} at ${appointmentToReschedule.time}\n` +
                `TO: ${formatDateForList(selectedDate)} at ${timeString}\n\n` +
                `Your new appointment is now pending confirmation.`);

          // Fermer le modal
          handleCloseModal();
        } catch (error) {
          console.error('Error in rescheduling process:', error);
          alert('There was a problem rescheduling your appointment. Please try again later.');
        } finally {
          setLoading(false);
        }
      } else {
        // Create a new appointment
        const appointmentData = {
          doctorId: selectedDoctor.id,
          doctor: selectedDoctor.name, // Add doctor name for Firebase
          specialty: selectedDoctor.specialty, // Add specialty for Firebase
          date: formatDateForList(selectedDate),
          time: timeString,
          reason: reasonForVisit, // Include reason for visit
          type: 'in-person', // Default type
          duration: 30 // Default duration in minutes
        };

        // Create appointment using the context
        try {
          const savedAppointment = await createAppointment(appointmentData);
        } catch (apiError) {
          console.error('Error creating appointment:', apiError);

          // If API fails, create a local appointment
          const localAppointment = {
            id: Date.now().toString(),
            doctor: selectedDoctor.name,
            specialty: selectedDoctor.specialty,
            date: formatDateForList(selectedDate),
            time: timeString,
            reason: reasonForVisit,
            status: 'Pending',
            patientId: user?.uid || 'local-user',
            createdAt: new Date().toISOString()
          };

          await firebaseAppointmentsService.saveAppointment(localAppointment);
          setAppointments(prev => [...prev, localAppointment]);
        }

        // Show success message for new appointment
        alert(`Your appointment request with ${selectedDoctor.name} on ${formatDate(selectedDate)} has been submitted.`);

        // Close the modal
        handleCloseModal();
      }

      // Refresh appointments from server
      fetchAppointments();
    } catch (error) {
      console.error('Error saving appointment:', error);
      alert('Failed to save appointment: ' + error.message);
    }
  };

  const formatDate = (date) => {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const formatDateForList = (date) => {
    // Format pour l'affichage dans la liste: YYYY-MM-DD
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  // Afficher les détails du rendez-vous
  const handleViewAppointmentDetails = (appointment) => {
    setSelectedAppointment(appointment);
    setDetailsModalVisible(true);
  };

  const handleCloseDetailsModal = () => {
    setDetailsModalVisible(false);
    setSelectedAppointment(null);
  };

  const handleRescheduleAppointment = (appointment) => {
    // Check if the appointment is already cancelled
    if (appointment.status && appointment.status.toLowerCase() === 'cancelled') {
      alert('This appointment has been cancelled and cannot be rescheduled.');
      return;
    }

    // Check if the appointment is already completed
    if (appointment.status && appointment.status.toLowerCase() === 'completed') {
      alert('This appointment has been completed and cannot be rescheduled.');
      return;
    }

    // Check if the appointment date has passed
    const appointmentDate = new Date(`${appointment.date}T${appointment.time}`);
    const now = new Date();

    if (appointmentDate < now) {
      alert('You cannot reschedule an appointment that has already passed.');
      return;
    }

    if (window.confirm(`Would you like to reschedule your appointment with ${appointment.doctor} on ${appointment.date} at ${appointment.time}?`)) {
      // Set the appointment to reschedule and open the modal
      setAppointmentToReschedule(appointment);
      setIsRescheduling(true);
      setReasonForVisit(appointment.reason || ''); // Preserve the original reason

      // Find the doctor object that matches the appointment
      const doctor = doctors.find(doc => doc.name === appointment.doctor);
      if (doctor) {
        setSelectedDoctor(doctor);
      }

      // Open the modal first
      setModalVisible(true);

      // Then open the date picker
      setTimeout(() => {
        // Convert the appointment date string to a Date object
        const [year, month, day] = appointment.date.split('-').map(num => parseInt(num));
        const appointmentDate = new Date(year, month - 1, day);

        setTempSelectedDate(appointmentDate);
        setSelectedTime(null); // Reset time selection
        setDatePickerVisible(true);
      }, 300);
    }
  };

  // Function to handle the initial cancel appointment request
  const handleCancelAppointment = (appointment) => {
    // Check if the appointment is already cancelled
    if (appointment.status && appointment.status.toLowerCase() === 'cancelled') {
      alert('This appointment has already been cancelled.');
      return;
    }

    // Check if the appointment date has passed
    const appointmentDate = new Date(`${appointment.date}T${appointment.time}`);
    const now = new Date();

    if (appointmentDate < now) {
      alert('You cannot cancel an appointment that has already passed.');
      return;
    }

    if (window.confirm(`Are you sure you want to cancel your appointment with ${appointment.doctor} on ${appointment.date} at ${appointment.time}?`)) {
      processCancelAppointment(appointment.id, 'Cancelled by patient');
    }
  };

  // Function to process the appointment cancellation
  const processCancelAppointment = async (appointmentId, reason) => {
    try {
      setLoading(true);

      // Cancel appointment using the context
      await cancelAppointment(appointmentId, reason || 'Cancelled by patient');

      // Refresh appointments from server
      await fetchAppointments();

      // If we're not showing cancelled appointments, filter them out again
      if (!showCancelled && appointments.length > 0) {
        filterAppointments(appointments, showCancelled);
      }

      alert('Your appointment has been cancelled.');
      return true;
    } catch (error) {
      console.error('Error cancelling appointment:', error);
      alert('Failed to cancel appointment. Please try again later.');
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Rendu d'une date dans le sélecteur personnalisé
  const renderDateOption = (date) => {
    const isSelected =
      tempSelectedDate.getDate() === date.getDate() &&
      tempSelectedDate.getMonth() === date.getMonth() &&
      tempSelectedDate.getFullYear() === date.getFullYear();

    const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

    return (
      <button
        key={date.toISOString()}
        className={`date-option ${isSelected ? 'selected-date-option' : ''}`}
        onClick={() => setTempSelectedDate(date)}
      >
        <span className={`date-option-day ${isSelected ? 'selected-date-text' : ''}`}>
          {dayNames[date.getDay()]}
        </span>
        <span className={`date-option-date ${isSelected ? 'selected-date-text' : ''}`}>
          {date.getDate()}
        </span>
        <span className={`date-option-month ${isSelected ? 'selected-date-text' : ''}`}>
          {monthNames[date.getMonth()]}
        </span>
      </button>
    );
  };

  return (
    <div className="container">
      <div className="header">
        <h1 className="header-title">My Appointments</h1>
      </div>

      <div className="action-buttons-row">
        <button
          className="filter-button"
          onClick={toggleShowCancelled}
        >
          {showCancelled ? <IoEyeOffOutline size={18} color="#3f51b5" /> : <IoEyeOutline size={18} color="#3f51b5" />}
          <span className="filter-button-text">
            {showCancelled ? "Hide Cancelled" : "Show All"}
          </span>
        </button>

        <button
          className="request-button"
          onClick={handleRequestAppointment}
        >
          <IoAddCircle size={18} color="#fff" />
          <span className="request-button-text">Request Appointment</span>
        </button>
      </div>

      {loading || appointmentsLoading ? (
        <div className="loading-container">
          <div className="spinner"></div>
          <p className="loading-text">Loading appointments...</p>
        </div>
      ) : appointments.length === 0 ? (
        <div className="empty-container">
          <IoCalendarOutline size={64} color="#ccc" />
          <h2 className="empty-text">No appointments scheduled</h2>
          <p className="empty-sub-text">
            Request an appointment with one of our specialists
          </p>
        </div>
      ) : filteredAppointments.length === 0 && !showCancelled ? (
        <div className="empty-container">
          <IoCalendarOutline size={64} color="#ccc" />
          <h2 className="empty-text">No active appointments</h2>
          <p className="empty-sub-text">
            All your appointments have been cancelled or completed.
            Tap "Show All" to view your appointment history.
          </p>
        </div>
      ) : filteredAppointments.length === 0 ? (
        <div className="empty-container">
          <IoCalendarOutline size={64} color="#ccc" />
          <h2 className="empty-text">No appointments found</h2>
          <p className="empty-sub-text">
            Request an appointment with one of our specialists
          </p>
        </div>
      ) : (
        <>
          <div className="appointment-guide">
            <h3 className="guide-title">Managing Your Appointments</h3>
            <div className="guide-item">
              <IoCalendarOutline size={18} color="#3f51b5" />
              <p className="guide-text">
                To <span className="guide-highlight">reschedule</span> an appointment, tap the "Reschedule" button on any active appointment
              </p>
            </div>
            <div className="guide-item">
              <IoCloseCircleOutline size={18} color="#f44336" />
              <p className="guide-text">
                To <span className="guide-highlight">cancel</span> an appointment, tap the "Cancel" button and provide a reason
              </p>
            </div>
          </div>

          <div className="list-container">
            {filteredAppointments.map((item) => {
              // Determine status color based on appointment status
              let statusColor;
              const status = item.status ? item.status.toLowerCase() : 'pending';

              switch (status) {
                case 'confirmed':
                  statusColor = '#4CAF50'; // Green
                  break;
                case 'cancelled':
                  statusColor = '#F44336'; // Red
                  break;
                case 'completed':
                  statusColor = '#2196F3'; // Blue
                  break;
                case 'pending':
                default:
                  statusColor = '#FFC107'; // Yellow/Amber
                  break;
              }

              // Format the status text for display
              const statusText = item.status ?
                item.status.charAt(0).toUpperCase() + item.status.slice(1).toLowerCase() :
                'Pending';

              // Check if this appointment has been rescheduled
              const isRescheduled = item.notes && item.notes.includes('Rescheduled from');

              // Extraire la nouvelle date et heure à partir des notes si c'est un rendez-vous replanifié
              let displayDate = item.date;
              let displayTime = item.time;

              return (
                <div
                  key={item.id + (item.updatedAt || '')}
                  className="card"
                  onClick={() => handleViewAppointmentDetails(item)}
                >
                  <div className="card-gradient">
                    <div className="card-content">
                      <div className="card-header">
                        <div className="date-container">
                          <h3 className="date-title">{displayDate}</h3>
                          <div className="status-container">
                            <div className="status-dot" style={{ backgroundColor: statusColor }}></div>
                            <span className="status-text" style={{ color: statusColor }}>
                              {statusText.toUpperCase()}
                            </span>
                          </div>
                        </div>
                        <div className="avatar-icon" style={{ backgroundColor: statusColor + '20', color: statusColor }}>
                          <IoCalendarOutline size={24} />
                        </div>
                      </div>

                      <div className="card-divider"></div>

                      <div className="card-details">
                        <div className="detail-row">
                          <IoPersonOutline size={16} color="#555" className="detail-icon" />
                          <span className="detail-text">{item.doctor}</span>
                        </div>
                        <div className="detail-row">
                          <IoMedicalOutline size={16} color="#555" className="detail-icon" />
                          <span className="detail-text">{item.specialty}</span>
                        </div>
                        <div className="detail-row">
                          <IoTimeOutline size={16} color="#555" className="detail-icon" />
                          <span className="detail-text">{displayTime}</span>
                        </div>
                        {item.reason && (
                          <div className="detail-row">
                            <IoDocumentTextOutline size={16} color="#555" className="detail-icon" />
                            <span className="detail-text">{item.reason}</span>
                          </div>
                        )}
                      </div>

                      {isRescheduled && (
                        <div className="rescheduled-banner">
                          <IoRefreshCircle size={16} color="#ff9800" />
                          <span className="rescheduled-text">Rescheduled</span>
                        </div>
                      )}

                      <div className="card-footer">
                        <div className="action-buttons-container">
                          {/* Only show reschedule button if appointment is not cancelled or completed */}
                          {status !== 'cancelled' && status !== 'completed' && (
                            <button
                              className="action-button reschedule-button"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleRescheduleAppointment(item);
                              }}
                            >
                              <IoCalendarOutline size={14} color="#ff9800" className="button-icon" />
                              <span className="reschedule-button-text">Reschedule</span>
                            </button>
                          )}

                          {/* Only show cancel button if appointment is not cancelled or completed */}
                          {status !== 'cancelled' && status !== 'completed' && (
                            <button
                              className="action-button cancel-button"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleCancelAppointment(item);
                              }}
                            >
                              <IoCloseCircleOutline size={14} color="#f44336" className="button-icon" />
                              <span className="cancel-button-text">Cancel</span>
                            </button>
                          )}

                          <button
                            className="view-details-button"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleViewAppointmentDetails(item);
                            }}
                          >
                            <span className="view-details-text">View Details</span>
                            <IoChevronForward size={14} color="#4CAF50" />
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </>
      )}

      {/* Appointment Request Modal */}
      {modalVisible && (
        <div className="modal-overlay">
          <div className="modal-content">
            <div className="modal-header">
              <h2 className="modal-title">
                {isRescheduling ? 'Reschedule Appointment' : 'Request Appointment'}
              </h2>
              <button onClick={handleCloseModal}>
                <IoClose size={24} color="#333" />
              </button>
            </div>

            <div className="modal-body">
              {!isRescheduling && (
                <>
                  <label className="input-label">Doctor</label>
                  <div className="doctor-selection">
                    {doctors.map((doctor) => (
                      <div
                        key={doctor.id}
                        className={`doctor-option ${selectedDoctor?.id === doctor.id ? 'selected-doctor-option' : ''}`}
                        onClick={() => {
                          setSelectedDoctor(doctor);
                          setAvailabilityStatus(null);
                        }}
                      >
                        <div className={`doctor-option-text ${selectedDoctor?.id === doctor.id ? 'selected-doctor-text' : ''}`}>
                          {doctor.name}
                        </div>
                        <div className={`doctor-specialty ${selectedDoctor?.id === doctor.id ? 'selected-doctor-text' : ''}`}>
                          {doctor.specialty}
                        </div>
                      </div>
                    ))}
                  </div>
                </>
              )}

              <label className="input-label">{isRescheduling ? 'New Date & Time' : 'Preferred Date & Time'}</label>
              <div
                className="date-input"
                onClick={openDatePicker}
              >
                <IoCalendarOutline size={20} color={isRescheduling ? "#4CAF50" : "#3f51b5"} />
                <span className="date-text">
                  {formatDate(selectedDate)}{selectedTime ? ` at ${selectedTime}` : ''}
                </span>
              </div>

              {/* Reason for Visit Field */}
              <label className="input-label">Reason for Visit</label>
              <textarea
                className="reason-input"
                placeholder="Please describe the reason for your appointment"
                value={reasonForVisit}
                onChange={(e) => setReasonForVisit(e.target.value)}
              />

              <div className="availability-section">
                <button
                  className={`check-availability-button ${availabilityChecking ? 'checking-button' : ''}`}
                  onClick={checkAvailability}
                  disabled={!selectedDoctor || availabilityChecking || availabilityStatus === 'available'}
                >
                  {availabilityChecking ? (
                    <div className="checking-container">
                      <div className="spinner"></div>
                      <span className="check-availability-text">Checking...</span>
                    </div>
                  ) : availabilityStatus === 'available' ? (
                    <div className="checking-container">
                      <IoCheckmarkCircle size={20} color="#fff" />
                      <span className="check-availability-text">
                        Availability Confirmed
                      </span>
                    </div>
                  ) : (
                    <span className="check-availability-text">
                      Check Availability
                    </span>
                  )}
                </button>

                {availabilityStatus === 'available' && (
                  <div className="availability-result">
                    <IoCheckmarkCircle
                      size={20}
                      color="#4CAF50"
                    />
                    <span
                      className="availability-text"
                      style={{ color: '#4CAF50' }}
                    >
                      Time slot is available for booking
                    </span>
                  </div>
                )}

                {availabilityStatus === 'unavailable' && (
                  <div className="availability-result">
                    <IoCloseCircle
                      size={20}
                      color="#F44336"
                    />
                    <span
                      className="availability-text"
                      style={{ color: '#F44336' }}
                    >
                      Selected time slot is not available
                    </span>
                  </div>
                )}
              </div>

              <button
                className={`submit-button ${((!selectedDoctor && !isRescheduling) || !selectedTime) ? 'disabled-button' : ''}`}
                onClick={submitAppointmentRequest}
                disabled={(!selectedDoctor && !isRescheduling) || !selectedTime}
              >
                {isRescheduling ? 'Confirm Rescheduling' : 'Submit Request'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Date Picker Modal */}
      {datePickerVisible && (
        <div className="modal-overlay">
          <div className="date-picker-modal">
            <div className="date-picker-header">
              <h2 className="date-picker-title">Select a Date & Time</h2>
              <button onClick={cancelDateSelection}>
                <IoClose size={24} color="#333" />
              </button>
            </div>

            <div className="date-scroll-view">
              <div className="date-options-container">
                {getAvailableDates().map(renderDateOption)}
              </div>
            </div>

            <div className="time-selection-container">
              <h3 className="time-selection-title">Select Time</h3>
              <div className="time-scroll-view">
                <div className="time-options-container">
                  {['9:00 AM', '10:00 AM', '11:00 AM', '12:00 PM', '1:00 PM', '2:00 PM', '3:00 PM', '4:00 PM', '5:00 PM'].map(time => (
                    <button
                      key={time}
                      className={`time-option ${selectedTime === time ? 'selected-time-option' : ''}`}
                      onClick={() => setSelectedTime(time)}
                    >
                      <span className={`time-option-text ${selectedTime === time ? 'selected-time-text' : ''}`}>
                        {time}
                      </span>
                    </button>
                  ))}
                </div>
              </div>
            </div>

            <div className="date-picker-actions">
              <button
                className="cancel-date-button"
                onClick={cancelDateSelection}
              >
                <span className="cancel-date-text">Cancel</span>
              </button>
              <button
                className="confirm-date-button"
                onClick={confirmDate}
                disabled={!selectedTime}
              >
                <span className="confirm-date-text">Confirm</span>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Appointment Details Modal */}
      {detailsModalVisible && selectedAppointment && (
        <div className="modal-overlay">
          <div className="modal-content">
            <div className="modal-header">
              <h2 className="modal-title">Appointment Details</h2>
              <button onClick={handleCloseDetailsModal}>
                <IoClose size={24} color="#333" />
              </button>
            </div>

            <div className="modal-body">
              <div className="details-header">
                <div className="status-badge" style={{ backgroundColor: selectedAppointment.status === 'Confirmed' ? '#4CAF50' : '#FFC107' }}>
                  <span className="status-text">{selectedAppointment.status}</span>
                </div>
                <h2 className="details-title">{selectedAppointment.doctor}</h2>
                <p className="details-subtitle">{selectedAppointment.specialty}</p>
              </div>

              <div className="details-section">
                <h3 className="details-section-title">Appointment Information</h3>

                <div className="details-row">
                  <div className="details-icon-container">
                    <IoCalendarOutline size={20} color="#3f51b5" />
                  </div>
                  <div className="details-content">
                    <span className="details-label">Date</span>
                    <span className="details-value">{selectedAppointment.date}</span>
                  </div>
                </div>

                <div className="details-row">
                  <div className="details-icon-container">
                    <IoTimeOutline size={20} color="#3f51b5" />
                  </div>
                  <div className="details-content">
                    <span className="details-label">Time</span>
                    <span className="details-value">{selectedAppointment.time}</span>
                  </div>
                </div>

                <div className="details-row">
                  <div className="details-icon-container">
                    <IoMedicalOutline size={20} color="#3f51b5" />
                  </div>
                  <div className="details-content">
                    <span className="details-label">Reason for Visit</span>
                    <span className="details-value">{selectedAppointment.reason || 'General consultation'}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Appointments;
