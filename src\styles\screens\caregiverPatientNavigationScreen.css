/* CaregiverPatientNavigationScreen Styles */

.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-title {
  font-size: 20px;
  font-weight: bold;
  color: #333;
  margin: 0;
}

.header-buttons {
  display: flex;
  gap: 8px;
}

.header-button {
  padding: 8px;
  background: none;
  border: none;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.header-button:hover {
  background-color: #f0f0f0;
}

.header-button i {
  font-size: 24px;
  color: #4CAF50; /* caregiver primary color */
}

.loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #4CAF50;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 16px;
  font-size: 16px;
  color: #666;
}

.map-container {
  height: 50%;
  width: 100%;
  background-color: #e0e0e0;
  border-bottom: 1px solid #ccc;
  position: relative;
}

.custom-marker {
  background: transparent !important;
  border: none !important;
}

.route-type-container {
  display: flex;
  justify-content: space-around;
  padding: 16px;
  background-color: #fff;
  border-bottom: 1px solid #e0e0e0;
}

.route-type-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 20px;
  background-color: #f5f5f5;
  border: none;
  cursor: pointer;
  transition: all 0.2s;
}

.route-type-button:hover {
  background-color: #e0e0e0;
}

.route-type-button.active {
  background-color: #4CAF50;
  color: white;
}

.route-type-button i {
  font-size: 24px;
}

.route-type-button span {
  font-size: 14px;
  font-weight: 500;
}

.route-info-section {
  padding: 16px;
  background-color: #fff;
  margin: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.route-info-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin: 0 0 12px 0;
}

.route-info-row {
  display: flex;
  margin-bottom: 8px;
}

.route-info-label {
  width: 100px;
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.route-info-value {
  flex: 1;
  font-size: 16px;
  color: #333;
}

.send-guidance-button {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  background-color: #4CAF50;
  color: white;
  padding: 12px 20px;
  border-radius: 8px;
  border: none;
  margin-top: 16px;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.2s;
  width: 100%;
}

.send-guidance-button:hover {
  background-color: #45a049;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  width: 80%;
  max-width: 500px;
  background-color: #fff;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  max-height: 80vh;
  overflow-y: auto;
}

.patient-select-modal {
  max-height: 80%;
}

.modal-title {
  font-size: 20px;
  font-weight: bold;
  color: #333;
  margin: 0 0 16px 0;
  text-align: center;
}

.modal-body {
  margin-bottom: 16px;
}

.modal-field {
  margin-bottom: 16px;
}

.modal-label {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
  display: block;
}

.modal-value {
  font-size: 16px;
  color: #333;
  margin: 0;
}

.instructions-input {
  width: 100%;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 12px;
  font-size: 16px;
  min-height: 100px;
  resize: vertical;
  font-family: inherit;
}

.modal-buttons {
  display: flex;
  gap: 10px;
}

.modal-button {
  flex: 1;
  padding: 12px;
  border-radius: 8px;
  border: none;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  transition: background-color 0.2s;
}

.cancel-button {
  background-color: #f1f2f6;
  color: #666;
}

.cancel-button:hover {
  background-color: #e1e2e6;
}

.send-button {
  background-color: #4CAF50;
  color: white;
}

.send-button:hover {
  background-color: #45a049;
}

.patients-list {
  max-height: 400px;
  overflow-y: auto;
}

.patient-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border-bottom: 1px solid #eee;
  cursor: pointer;
  transition: background-color 0.2s;
}

.patient-item:hover {
  background-color: #f5f5f5;
}

.patient-item:last-child {
  border-bottom: none;
}

.patient-avatar {
  width: 50px;
  height: 50px;
  border-radius: 25px;
  background-color: #4CAF50;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 12px;
}

.patient-initials {
  color: white;
  font-size: 18px;
  font-weight: bold;
}

.patient-info {
  flex: 1;
}

.patient-name {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin: 0 0 2px 0;
}

.patient-email {
  font-size: 12px;
  color: #666;
  margin: 0;
}

.empty-patients-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  text-align: center;
}

.empty-patients-icon {
  font-size: 60px;
  color: #ccc;
  margin-bottom: 16px;
}

.empty-patients-text {
  font-size: 18px;
  font-weight: 600;
  color: #666;
  margin: 0 0 8px 0;
}

.empty-patients-subtext {
  font-size: 14px;
  color: #999;
  margin: 0;
  max-width: 80%;
}

.modal-footer {
  display: flex;
  justify-content: center;
  margin-top: 16px;
}

/* Responsive design */
@media (max-width: 768px) {
  .modal-content {
    width: 95%;
    margin: 10px;
  }
  
  .route-type-container {
    flex-direction: column;
    gap: 8px;
  }
  
  .route-type-button {
    justify-content: center;
  }
  
  .header-title {
    font-size: 18px;
  }
  
  .map-container {
    height: 40%;
  }
}
