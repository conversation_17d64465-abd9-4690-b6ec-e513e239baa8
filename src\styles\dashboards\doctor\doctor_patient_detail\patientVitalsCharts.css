/* Patient Vitals Charts CSS */
.patient-vitals-charts {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top: 4px solid #3498db;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.vitals-controls {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.vital-selector {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 10px;
}

.vital-button {
  padding: 8px 12px;
  border: 1px solid #e0e0e0;
  border-radius: 20px;
  background-color: white;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.vital-button.active {
  background-color: #aa56ff;
  color: white;
  border-color: #aa56ff;
}

.vital-button:hover:not(.active) {
  background-color: #f5f5f5;
}

.time-selector {
  display: flex;
  gap: 8px;
}

.time-button {
  padding: 8px 12px;
  border: 1px solid #e0e0e0;
  border-radius: 20px;
  background-color: white;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.time-button.active {
  background-color: #aa56ff;
  color: white;
  border-color: #aa56ff;
}

.time-button:hover:not(.active) {
  background-color: #f5f5f5;
}

.chart-container {
  flex: 1;
  min-height: 300px;
  position: relative;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .vitals-controls {
    flex-direction: column;
  }
  
  .vital-selector, .time-selector {
    width: 100%;
    justify-content: center;
  }
  
  .vital-button, .time-button {
    font-size: 12px;
    padding: 6px 10px;
  }
}
