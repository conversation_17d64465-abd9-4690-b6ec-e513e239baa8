.container {
  width: 100%;
  height: 100%;
}

.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.card {
  margin: 8px;
  cursor: pointer;
  transition: transform 0.2s;
}

.card:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.cardHeader {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.statusChip {
  height: 24px;
  padding: 4px 8px;
  border-radius: 16px;
  color: white;
  font-weight: bold;
  font-size: 12px;
}

.modal {
  background-color: white;
  padding: 20px;
  margin: 20px;
  border-radius: 8px;
  max-height: 80%;
  overflow-y: auto;
}

.divider {
  margin: 16px 0;
  border-bottom: 1px solid #e0e0e0;
}

.actionButtons {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  margin-top: 16px;
}

.actionButton {
  flex: 1;
  margin: 0 4px;
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  color: white;
  cursor: pointer;
}

.confirmButton {
  background-color: #4CAF50;
}

.cancelButton {
  background-color: #F44336;
}

.completeButton {
  background-color: #2196F3;
}

.noAppointments {
  text-align: center;
  margin-top: 20px;
  font-size: 16px;
}

.pagination {
  margin: 16px 0;
  display: flex;
  justify-content: center;
}
