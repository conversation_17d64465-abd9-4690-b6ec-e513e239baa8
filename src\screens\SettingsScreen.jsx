import React from 'react';
import Settings from '../components/settings/Settings';
import './settingsScreen.css';
import { useAuth } from '../contexts/AuthContext';

const SettingsScreen = () => {
  const { user } = useAuth();
  const theme = getThemeForRole(user?.role || 'default');

  const handleGoBack = () => {
    window.history.back();
  };

  return (
    <div className="settings-screen-container">
      <header 
        className="settings-header" 
        style={{ backgroundColor: theme.colors.primary }}
      >
        <button 
          className="back-button" 
          onClick={handleGoBack}
          aria-label="Go back"
        >
          <span className="back-icon">←</span>
        </button>
        <h1 className="header-title">Settings</h1>
      </header>
      <Settings />
    </div>
  );
};

export default SettingsScreen;
