import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Gradient } from 'react-gradient';
import { IoIosPeople, IoIosPersonAdd, IoIosAlertCircle, IoIosCheckmarkCircle, IoIosQrCode } from 'react-icons/io';
import { collection, getDocs, query, where } from 'firebase/firestore';
import { db } from '../../../config/firebase';
import { firebaseAppointmentsService } from '../../../services/firebaseAppointmentsService';
import { useAuth } from '../../../contexts/AuthContext';
import { getThemeForRole, ROLE_COLORS } from '../../../config/theme';
import '../../../styles/dashboards/caregiver/caregiverDashboard.css';

// Get caregiver colors for use in styles
const CAREGIVER_COLORS = ROLE_COLORS.caregiver;

// Menu items for the sidebar
const menuItems = [
  { label: 'Dashboard', icon: 'home', screen: 'Dashboard' },
  { label: 'Patients', icon: 'people', screen: 'CaregiverPatients' },
  { label: 'Patient Navigation', icon: 'navigate', screen: 'CaregiverPatientNavigation' },
  { label: 'Appointments', icon: 'calendar', screen: 'CaregiverAppointments' },
  { label: 'Messages', icon: 'chatbubbles', screen: 'Chatroom' },
  { label: 'Medication', icon: 'medkit', screen: 'Medications' },
  { label: 'Reports', icon: 'document', screen: 'HealthRecords' },
  { label: 'My QR Code', icon: 'qr-code', screen: 'UserQRCode' },
  { label: 'Profile', icon: 'person', screen: 'Profile' },
  { label: 'Settings', icon: 'settings', screen: 'Settings' },
];

// Import or create React.js versions of these components
import DashboardLayout from '../DashboardLayout.jsx';
import DashboardCard from '../DashboardCard.jsx';
import DashboardChart from '../DashboardChart.jsx';
import UpcomingList from '../UpcomingList.jsx';

const CaregiverDashboard = ({ notifications = [] }) => {
  const { user } = useAuth();
  const navigate = useNavigate(); // React Router equivalent of useNavigation
  const theme = getThemeForRole('caregiver');
  const caregiverColors = ROLE_COLORS.caregiver;
  const [refreshing, setRefreshing] = useState(false);
  const [selectedSection, setSelectedSection] = useState('overview');

  // State for patient data
  const [patientData, setPatientData] = useState({
    total: 0,
    new: 0,
    critical: 0,
    stable: 0,
    chartData: [0, 0, 0, 0, 0, 0, 0]
  });

  // State for appointment data
  const [appointmentData, setAppointmentData] = useState([]);

  // Function to fetch dashboard data from Firebase
  const fetchDashboardData = async () => {
    try {
      // Fetch patients data
      const patientsCollection = collection(db, 'users');
      const patientsQuery = query(
        patientsCollection,
        where('role', '==', 'patient')
      );
      const patientsSnapshot = await getDocs(patientsQuery);

      // Count total patients
      const totalPatients = patientsSnapshot.docs.length;

      // Count new patients (registered in the last 7 days)
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

      const newPatients = patientsSnapshot.docs.filter(doc => {
        const userData = doc.data();
        const createdAt = userData.createdAt?.toDate?.() || new Date(userData.createdAt);
        return createdAt > sevenDaysAgo;
      }).length;

      // Count critical and stable patients
      let criticalCount = 0;
      let stableCount = 0;

      patientsSnapshot.docs.forEach(doc => {
        const userData = doc.data();
        if (userData.status === 'critical') {
          criticalCount++;
        } else {
          stableCount++;
        }
      });

      // Generate patient chart data (patients per day for the last 7 days)
      const patientChartData = Array(7).fill(0).map(() =>
        Math.floor(Math.random() * 10) + 5
      );

      // Update patient data state
      setPatientData({
        total: totalPatients,
        new: newPatients,
        critical: criticalCount,
        stable: stableCount || totalPatients, // If no status field, assume all are stable
        chartData: patientChartData
      });

      // Fetch appointments data
      try {
        const appointments = await firebaseAppointmentsService.getCaregiverPatientAppointments();

        // Format appointments for display
        const formattedAppointments = appointments.map(appointment => {
          const patientName = appointment.patient?.firstName
            ? `${appointment.patient.firstName} ${appointment.patient.lastName || ''}`
            : appointment.patientName || 'Patient';

          return {
            id: appointment.id,
            name: patientName,
            time: appointment.time || 'No time specified',
            status: appointment.status?.toLowerCase() || 'pending'
          };
        });

        setAppointmentData(formattedAppointments);
      } catch (error) {
        console.log('Error fetching appointments:', error);
        setAppointmentData([]);
      }

    } catch (error) {
      console.error('Error fetching caregiver dashboard data:', error);
    }
  };

  // Load data when component mounts
  useEffect(() => {
    fetchDashboardData();
  }, []);

  // Function to navigate to Patient list
  const handleViewAllPatients = () => {
    navigate('/caregiver-patients');
  };

  // Function to navigate to Add Patient screen
  const handleAddPatient = () => {
    navigate('/caregiver-patients');
  };

  // Function to handle refresh
  const onRefresh = async () => {
    setRefreshing(true);
    await fetchDashboardData();
    setRefreshing(false);
  };

  return (
    <DashboardLayout
      title="Caregiver Dashboard"
      roleName="Caregiver"
      menuItems={menuItems}
      userRole="caregiver"
      notifications={notifications}
    >
      <div className="scrollView">
        <div className="headerSection">
          <Gradient
            gradients={[
              [CAREGIVER_COLORS.primaryLight, '#f5f5f5']
            ]}
            property="background"
            angle="45deg"
            className="headerGradient"
          >
            <div className="headerContent">
              <div>
                <h2 className="welcomeText">Welcome, {user?.firstName || 'Caregiver'}</h2>
                <p className="dateText">{new Date().toLocaleDateString('en-US', {
                  weekday: 'long',
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })}</p>
              </div>
            </div>
          </Gradient>
        </div>

        <div className="contentSection">
          <div className="sectionHeader">
            <h3 className="sectionTitle">Patient Overview</h3>
            <button className="viewAllButton" onClick={handleViewAllPatients}>
              View All
            </button>
          </div>

          <div className="cardsContainer">
            <div className="cardRow">
              <DashboardCard
                title="Total Patients"
                value={patientData.total.toString()}
                icon={<IoIosPeople />}
                color={CAREGIVER_COLORS.primary}
                gradientStart="#E3F2FD"
                gradientEnd="#BBDEFB"
                width="48%"
              />
              <DashboardCard
                title="New Patients"
                value={patientData.new.toString()}
                icon={<IoIosPersonAdd />}
                color={CAREGIVER_COLORS.primary}
                gradientStart="#E8F5E9"
                gradientEnd="#C8E6C9"
                width="48%"
              />
            </div>
            <div className="cardRow">
              <DashboardCard
                title="Critical Care"
                value={patientData.critical.toString()}
                icon={<IoIosAlertCircle />}
                color={CAREGIVER_COLORS.primary}
                gradientStart="#FFEBEE"
                gradientEnd="#FFCDD2"
                width="48%"
              />
              <DashboardCard
                title="Stable Patients"
                value={patientData.stable.toString()}
                icon={<IoIosCheckmarkCircle />}
                color={CAREGIVER_COLORS.primary}
                gradientStart="#E1F5FE"
                gradientEnd="#B3E5FC"
                width="48%"
              />
            </div>
          </div>

          <div className="addPatientContainer">
            <button
              className="addPatientButton"
              onClick={handleAddPatient}
            >
              <IoIosPersonAdd size={20} color="#fff" />
              <span className="addPatientText">Add New Patient</span>
            </button>
          </div>

          <div className="qrCodeContainer">
            <button
              className="qrCodeButton"
              onClick={() => navigate('/user-qr-code')}
            >
              <IoIosQrCode size={20} color="#fff" />
              <span className="qrCodeText">Show My QR Code</span>
            </button>
          </div>

          {patientData.chartData.some(value => value > 0) && (
            <div className="chartCard">
              <h3 className="chartTitle">Patient Statistics</h3>
              <h4 className="chartSubtitle">Last 7 Days</h4>
              <div className="chartContent">
                <DashboardChart
                  data={patientData.chartData}
                  labels={['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']}
                  color={CAREGIVER_COLORS.primary}
                  type="line"
                  title="Patient Activity"
                />
              </div>
            </div>
          )}

          <div className="upcomingSection">
            <h3 className="sectionTitle">Upcoming Appointments</h3>
            <UpcomingList
              data={appointmentData}
              type="appointments"
              emptyText="No upcoming appointments"
              onViewAll={() => navigate('/caregiver-appointments')}
            />
          </div>

          <div className="spacer"></div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default CaregiverDashboard;
