// Base components
import DashboardLayout from './DashboardLayout';
import DashboardCard from './DashboardCard';
import Dashboard<PERSON>hart from './DashboardChart';
import UpcomingList from './UpcomingList';

// Role-specific dashboards
import PatientDashboard from './patient/PatientDashboard';
import DoctorDashboard from './doctor/DoctorDashboard';
import AdminDashboard from './admin/AdminDashboard';
import CaregiverDashboard from './caregiver/CaregiverDashboard';
import SupervisorDashboard from './supervisor/SupervisorDashboard';

// Define default menu items for each dashboard
// This ensures they exist even if the dashboard component doesn't define them
// Removed defaultProps for PatientDashboard to fix warning

// Removed defaultProps for DoctorDashboard to fix warning

// In React.js, we use defaultProps directly on the component or use default parameters
// For AdminDashboard
if (AdminDashboard.defaultProps) {
  AdminDashboard.defaultProps = {
    menuItems: [
      { label: 'Dashboard', icon: 'home', path: '/admin' },
      { label: 'Profile', icon: 'person', path: '/profile' }
    ],
    ...AdminDashboard.defaultProps
  };
}

// For CaregiverDashboard
if (CaregiverDashboard.defaultProps) {
  CaregiverDashboard.defaultProps = {
    menuItems: [
      { label: 'Dashboard', icon: 'home', path: '/caregiver' },
      { label: 'Profile', icon: 'person', path: '/profile' }
    ],
    ...CaregiverDashboard.defaultProps
  };
}

// For SupervisorDashboard
if (SupervisorDashboard.defaultProps) {
  SupervisorDashboard.defaultProps = {
    menuItems: [
      { label: 'Dashboard', icon: 'home', path: '/supervisor' },
      { label: 'Profile', icon: 'person', path: '/profile' }
    ],
    ...SupervisorDashboard.defaultProps
  };
}

export {
  // Base components
  DashboardLayout,
  DashboardCard,
  DashboardChart,
  UpcomingList,

  // Role-specific dashboards
  PatientDashboard,
  DoctorDashboard,
  AdminDashboard,
  CaregiverDashboard,
  SupervisorDashboard
};
