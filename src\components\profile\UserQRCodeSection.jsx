import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../../contexts/AuthContext';
import { getThemeForRole } from '../../../config/theme';
import '../../styles/profile/userQRCodeSection.css';

const UserQRCodeSection = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const theme = getThemeForRole(user?.role || 'default');
  
  return (
    <div className="card">
      <div className="cardContent">
        <div className="titleContainer">
          <h2 className="title" style={{ color: theme.colors.primary }}>User Code</h2>
          <button 
            onClick={() => navigate('/user-qr-code')}
            className="viewQrButton"
            style={{ backgroundColor: theme.colors.primary }}
          >
            <span className="viewQrButtonText">View QR</span>
          </button>
        </div>

        <div className="userInfo">
          <div 
            className="avatar" 
            style={{ backgroundColor: theme.colors.primary }}
          >
            {user?.displayName?.substring(0, 2).toUpperCase() || "U"}
          </div>
          <div className="username" style={{ color: theme.colors.primary }}>
            {user?.displayName || 'User'}
          </div>
          <div className="userRole">
            {user?.role?.charAt(0).toUpperCase() + user?.role?.slice(1)}
          </div>
          {user?.email && <div className="userEmail">{user?.email}</div>}
        </div>
        
        <div className="codeContainer">
          <div className="codeLabel">Your unique code:</div>
          <div className="codeBlock" style={{ borderColor: theme.colors.primary }}>
            <div className="codeText" style={{ color: theme.colors.primary }}>
              {user?.userCode || 'CODE NOT FOUND'}
            </div>
          </div>
          <p className="codeDescription">
            Use this code to connect with healthcare providers. You can also share your QR code when visiting healthcare facilities.
          </p>
        </div>
      </div>
      
      <div className="cardActions">
        <button 
          className="button"
          onClick={() => navigate('/user-qr-code')}
          style={{ backgroundColor: theme.colors.primary }}
        >
          View Full QR Code
        </button>
      </div>
    </div>
  );
};

export default UserQRCodeSection;
