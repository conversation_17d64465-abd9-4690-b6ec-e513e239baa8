import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import DashboardLayout from '../DashboardLayout';
import DashboardCard from '../DashboardCard';
import UpcomingList from '../UpcomingList';
import { useAuth } from '../../../contexts/AuthContext';
import { useVitals } from '../../../contexts/VitalsContext';
import { useAppointments } from '../../../contexts/AppointmentContext';
import { localMedicationsService } from '../../../services/localStorageService';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faHome, faCalendar, faMedkit, faFileAlt, faUserMd,
  faMap, faCompass, faCommentAlt, faCreditCard, faQrcode,
  faUser, faCog, faHeart, faDum<PERSON>ll, faTint, faWeight,
  faHeartbeat, faChartLine, faClock, faList, faBell, faFileText,
  faLightbulb, faWalking, faAppleAlt, faGraduationCap, faSyncAlt
} from '@fortawesome/free-solid-svg-icons';
import MedicationReminders from './medications/MedicationReminders';
import RecentPrescriptions from './prescriptions/RecentPrescriptions';
import { ROLE_COLORS } from '../../../config/theme';
import '../../../styles/dashboards/patient/patientDashoard.css';

// Get patient colors for use in styles
const PATIENT_COLORS = ROLE_COLORS.patient;

const PatientDashboard = ({ notifications = [] }) => {
  const { user } = useAuth();
  const { getAllVitals } = useVitals();
  const { getUpcomingAppointments, fetchAppointments } = useAppointments();
  const navigate = useNavigate();
  const [refreshing, setRefreshing] = useState(false);
  const [healthStats, setHealthStats] = useState({
    heartRate: '--',
    bloodPressure: '--/--',
    bloodGlucose: '--',
    weight: '--'
  });
  const [loadingVitals, setLoadingVitals] = useState(true);
  const [vitalStatus, setVitalStatus] = useState({
    heartRate: 'normal',
    bloodPressure: 'normal',
    bloodGlucose: 'normal',
    weight: 'normal'
  });
  const [appointments, setAppointments] = useState([]);
  const [medications, setMedications] = useState([]);
  const [recentActivities, setRecentActivities] = useState([]);
  const fadeAnim = useRef(null);

  // Game states
  const [numberGameActive, setNumberGameActive] = useState(false);
  const [numberGameStep, setNumberGameStep] = useState('intro'); // 'intro', 'memorize', 'recall', 'result'
  const [numberSequence, setNumberSequence] = useState([4, 7, 2, 9, 5]);
  const [userNumberInput, setUserNumberInput] = useState([]);
  const [numberGameResult, setNumberGameResult] = useState(null);

  const [visualGameActive, setVisualGameActive] = useState(false);
  const [visualGameStep, setVisualGameStep] = useState('intro'); // 'intro', 'memorize', 'recall', 'result'
  const [highlightedCells, setHighlightedCells] = useState([2, 5, 7]);
  const [userCellsSelection, setUserCellsSelection] = useState([]);
  const [visualGameResult, setVisualGameResult] = useState(null);

  const menuItems = [
    { label: 'Dashboard', icon: faHome, screen: 'PatientDashboard' },
    { label: 'Appointments', icon: faCalendar, screen: 'Appointments' },
    { label: 'Medications', icon: faMedkit, screen: 'Medications' },
    { label: 'Prescriptions', icon: faFileAlt, screen: 'PatientPrescriptions' },
    { label: 'My Doctors', icon: faUserMd, screen: 'MyDoctors' },
    { label: 'My Locations', icon: faMap, screen: 'MapScreen' },
    { label: 'Guidance', icon: faCompass, screen: 'PatientGuidance' },
    { label: 'Messages', icon: faCommentAlt, screen: 'Messages' },
    { label: 'Payments', icon: faCreditCard, screen: 'Payments' },
    { label: 'My QR Code', icon: faQrcode, screen: 'UserQRCode' },
    { label: 'Profile', icon: faUser, screen: 'Profile' },
    { label: 'Settings', icon: faCog, screen: 'Settings' }
  ];

  const onRefresh = async () => {
    setRefreshing(true);
    try {
      // Reload vitals, medications, and appointments
      await loadLatestVitals();
      await loadMedications();
      await loadAppointments();
    } catch (error) {
      console.error('Error refreshing data:', error);
    } finally {
      setRefreshing(false);
    }
  };

  // Function to load medications
  const loadMedications = async () => {
    try {
      const patientId = user?.uid || 'local-user';
      const medsData = await localMedicationsService.getPatientMedications(patientId);

      // Format medications for the UpcomingList component
      const formattedMedications = medsData.map(med => ({
        id: med.id,
        title: med.name,
        description: `${med.dosage}, ${med.frequency}`,
        time: med.nextDose || 'As prescribed',
        type: 'medication',
        status: 'active'
      }));

      setMedications(formattedMedications);
    } catch (error) {
      console.error('Error loading medications:', error);
    }
  };

  // Function to load appointments from Firebase
  const loadAppointments = async () => {
    try {
      console.log('Loading appointments...');
      console.log('Current user:', user?.uid, user?.email);

      // Refresh appointments from Firebase
      const fetchedAppointments = await fetchAppointments(true);
      console.log('Appointments fetched directly:', fetchedAppointments?.length || 0);

      // Wait a moment to ensure the context is updated
      await new Promise(resolve => setTimeout(resolve, 500));

      // Get upcoming appointments from context after fetching
      let upcomingAppointments = getUpcomingAppointments();
      console.log('Upcoming appointments from context:', upcomingAppointments);

      // If context is empty but we have appointments, use directly fetched appointments
      if ((!upcomingAppointments || upcomingAppointments.length === 0) && fetchedAppointments && fetchedAppointments.length > 0) {
        console.log('Context is empty but we have fetched appointments, using them directly');
        // Filter upcoming appointments (not cancelled and in the future)
        const now = new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

        upcomingAppointments = fetchedAppointments.filter(appointment => {
          // Check if appointment is cancelled
          if (appointment.status?.toLowerCase() === 'cancelled') {
            return false;
          }

          try {
            // Try to parse the date
            const dateParts = appointment.date.split('-');
            if (dateParts.length !== 3) return false;

            const year = parseInt(dateParts[0], 10);
            const month = parseInt(dateParts[1], 10) - 1;
            const day = parseInt(dateParts[2], 10);

            const appointmentDate = new Date(year, month, day);
            return appointmentDate >= today;
          } catch (error) {
            console.error('Error parsing date:', error);
            return false;
          }
        });
      }

      // Format appointments for the UpcomingList component
      const formattedAppointments = upcomingAppointments.map(appointment => {
        // Parse the appointment date
        let appointmentDate = new Date();

        try {
          // Parse the date part
          const dateParts = appointment.date.split('-');
          if (dateParts.length === 3) {
            const year = parseInt(dateParts[0], 10);
            const month = parseInt(dateParts[1], 10) - 1; // Months are 0-indexed in JS
            const day = parseInt(dateParts[2], 10);

            // Create a new date object with the date parts
            appointmentDate = new Date(year, month, day);

            // Handle 12-hour format with AM/PM for the time
            if (appointment.time.includes('AM') || appointment.time.includes('PM')) {
              // Extract hours and minutes from time string (e.g., "10:00 AM")
              const timeParts = appointment.time.replace(/\s*(AM|PM)\s*$/i, '').split(':');
              let hours = parseInt(timeParts[0], 10);
              const minutes = parseInt(timeParts[1], 10);

              // Adjust hours for PM
              if (appointment.time.includes('PM') && hours < 12) {
                hours += 12;
              }
              // Adjust for 12 AM
              if (appointment.time.includes('AM') && hours === 12) {
                hours = 0;
              }

              // Set the time part
              appointmentDate.setHours(hours, minutes, 0, 0);
            } else {
              // Handle 24-hour format
              const timeParts = appointment.time.split(':');
              if (timeParts.length >= 2) {
                const hours = parseInt(timeParts[0], 10);
                const minutes = parseInt(timeParts[1], 10);
                appointmentDate.setHours(hours, minutes, 0, 0);
              }
            }
          }
        } catch (error) {
          console.error('Error parsing appointment date:', error);
          // Use current date as fallback
          appointmentDate = new Date();
        }

        const now = new Date();

        // Format the time display
        let timeDisplay;
        const dayDiff = Math.round((appointmentDate - now) / (24 * 60 * 60 * 1000));

        if (dayDiff === 0) {
          timeDisplay = `Today, ${appointment.time}`;
        } else if (dayDiff === 1) {
          timeDisplay = `Tomorrow, ${appointment.time}`;
        } else {
          timeDisplay = `${appointmentDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}, ${appointment.time}`;
        }

        // Format the appointment information for display
        return {
          id: appointment.id,
          title: appointment.doctor,
          description: `${appointment.specialty || 'Medical Appointment'}${appointment.reason ? ` - ${appointment.reason}` : ''}`,
          time: timeDisplay,
          type: 'appointment',
          status: appointment.status?.toLowerCase() || 'pending',
          // Add additional information that might be useful
          appointmentDate: appointment.date,
          appointmentDateObj: appointmentDate,
          appointmentTime: appointment.time,
          location: appointment.location || 'Neuro Care Medical Center',
          duration: appointment.duration ? `${appointment.duration} min` : '30 min',
          // Store the actual date object for sorting
          dateObject: appointmentDate
        };
      });

      // Sort appointments by date (from earliest to latest)
      formattedAppointments.sort((a, b) => {
        if (a.dateObject && b.dateObject) {
          return a.dateObject.getTime() - b.dateObject.getTime();
        }
        return 0;
      });

      // Check if we have appointments to display
      if (formattedAppointments.length > 0) {
        console.log(`Setting ${formattedAppointments.length} appointments for display`);
        setAppointments(formattedAppointments);
      } else {
        console.log('No appointments to display');
        setAppointments([]);
      }
    } catch (error) {
      console.error('Error loading appointments from Firebase:', error);
    }
  };

  // Function to navigate to RecordVitals screen
  const navigateToRecordVitals = () => {
    navigate('/record-vitals');
  };

  // Function to navigate to HealthRecords screen
  const navigateToHealthRecords = () => {
    navigate('/health-records');
  };

  // Function to handle the request appointment button
  const handleRequestAppointment = () => {
    // Navigate to the Appointments screen
    navigate('/appointments', { state: { openRequestModal: true } });
  };

  // Function to determine heart rate status
  const getHeartRateStatus = (rate) => {
    if (!rate || rate === '--') return 'normal';
    const numRate = parseInt(rate);
    if (numRate < 60) return 'low';
    if (numRate > 100) return 'high';
    return 'normal';
  };

  // Function to determine blood pressure status
  const getBloodPressureStatus = (bp) => {
    if (!bp || bp === '--/--') return 'normal';
    const [systolic, diastolic] = bp.split('/').map(v => parseInt(v));
    if (systolic > 140 || diastolic > 90) return 'high';
    if (systolic < 90 || diastolic < 60) return 'low';
    return 'normal';
  };

  // Function to determine blood glucose status
  const getBloodGlucoseStatus = (glucose) => {
    if (!glucose || glucose === '--') return 'normal';
    const numGlucose = parseInt(glucose);
    if (numGlucose > 140) return 'high';
    if (numGlucose < 70) return 'low';
    return 'normal';
  };

  // Function to determine weight status
  const getWeightStatus = (weight) => {
    if (!weight || weight === '--') return 'normal';
    // This is a simplified example - in reality, you'd use BMI or other metrics
    return 'normal';
  };

  // Animation effect
  useEffect(() => {
    // For web, we'll use CSS animations instead of Animated API
    if (fadeAnim.current) {
      fadeAnim.current.classList.add('fadeIn');
    }
  }, []);

  // Function to load the latest vitals from Firebase
  const loadLatestVitals = async () => {
    setLoadingVitals(true);
    try {
      // Get all vitals from Firebase using VitalsContext
      const allVitals = await getAllVitals();

      // Get the most recent vital of each type for the health stats
      const newHealthStats = { ...healthStats };
      const newVitalStatus = { ...vitalStatus };
      const newRecentActivities = [];

      // Process heart rate readings
      if (allVitals.heartRate && allVitals.heartRate.length > 0) {
        // Sort by timestamp (newest first)
        const sortedHeartRates = [...allVitals.heartRate].sort(
          (a, b) => new Date(b.timestamp) - new Date(a.timestamp)
        );

        // Use the most recent for health stats
        const latestHeartRate = sortedHeartRates[0];
        newHealthStats.heartRate = latestHeartRate.values.value.toString();
        newVitalStatus.heartRate = getHeartRateStatus(newHealthStats.heartRate);

        // Add all heart rate readings to recent activities
        sortedHeartRates.forEach(reading => {
          newRecentActivities.push({
            type: 'heartRate',
            value: reading.values.value.toString(),
            timestamp: reading.timestamp,
            icon: faHeart,
            color: '#EA4335'
          });
        });
      }

      // Process blood pressure readings
      if (allVitals.bloodPressure && allVitals.bloodPressure.length > 0) {
        // Sort by timestamp (newest first)
        const sortedBP = [...allVitals.bloodPressure].sort(
          (a, b) => new Date(b.timestamp) - new Date(a.timestamp)
        );

        // Use the most recent for health stats
        const latestBP = sortedBP[0];
        const bp = latestBP.values;
        newHealthStats.bloodPressure = `${bp.systolic}/${bp.diastolic}`;
        newVitalStatus.bloodPressure = getBloodPressureStatus(newHealthStats.bloodPressure);

        // Add all blood pressure readings to recent activities
        sortedBP.forEach(reading => {
          const bpValue = `${reading.values.systolic}/${reading.values.diastolic}`;
          newRecentActivities.push({
            type: 'bloodPressure',
            value: bpValue,
            timestamp: reading.timestamp,
            icon: faDumbbell,
            color: '#4285F4'
          });
        });
      }

      // Process blood glucose readings
      if (allVitals.bloodGlucose && allVitals.bloodGlucose.length > 0) {
        // Sort by timestamp (newest first)
        const sortedGlucose = [...allVitals.bloodGlucose].sort(
          (a, b) => new Date(b.timestamp) - new Date(a.timestamp)
        );

        // Use the most recent for health stats
        const latestGlucose = sortedGlucose[0];
        newHealthStats.bloodGlucose = latestGlucose.values.value.toString();
        newVitalStatus.bloodGlucose = getBloodGlucoseStatus(newHealthStats.bloodGlucose);

        // Add all glucose readings to recent activities
        sortedGlucose.forEach(reading => {
          newRecentActivities.push({
            type: 'bloodGlucose',
            value: reading.values.value.toString(),
            timestamp: reading.timestamp,
            icon: faTint,
            color: '#FBBC05'
          });
        });
      }

      // Process weight readings
      if (allVitals.weight && allVitals.weight.length > 0) {
        // Sort by timestamp (newest first)
        const sortedWeight = [...allVitals.weight].sort(
          (a, b) => new Date(b.timestamp) - new Date(a.timestamp)
        );

        // Use the most recent for health stats
        const latestWeight = sortedWeight[0];
        newHealthStats.weight = latestWeight.values.value.toString();
        newVitalStatus.weight = getWeightStatus(newHealthStats.weight);

        // Add all weight readings to recent activities
        sortedWeight.forEach(reading => {
          newRecentActivities.push({
            type: 'weight',
            value: reading.values.value.toString(),
            timestamp: reading.timestamp,
            icon: faWeight,
            color: '#34A853'
          });
        });
      }

      // Sort all activities by timestamp (newest first)
      newRecentActivities.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

      setHealthStats(newHealthStats);
      setVitalStatus(newVitalStatus);
      setRecentActivities(newRecentActivities);
    } catch (error) {
      console.error('Error loading vitals from Firebase:', error);
    } finally {
      setLoadingVitals(false);
    }
  };

  // Load initial data
  useEffect(() => {
    loadLatestVitals();
    loadMedications();
    loadAppointments();
  }, [user]);

  return (
    <>
      <DashboardLayout
        title="Patient Dashboard"
        roleName="Patient"
        menuItems={menuItems}
        userRole="patient"
        notifications={notifications}
      >
        <div className="scrollView">
          <button
            onClick={onRefresh}
            disabled={refreshing}
            style={{
              position: 'fixed',
              bottom: '20px',
              right: '20px',
              zIndex: 100,
              padding: '10px',
              borderRadius: '50%',
              backgroundColor: PATIENT_COLORS.primary,
              color: 'white',
              border: 'none',
              cursor: 'pointer',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              width: '50px',
              height: '50px',
              boxShadow: '0 2px 5px rgba(0,0,0,0.2)'
            }}
          >
            <FontAwesomeIcon icon={faSyncAlt} spin={refreshing} />
          </button>

          {/* Modern Header with Enhanced Gradient */}
          <div className="headerSection">
            <div className="headerGradient">
              <div className="greeting" ref={fadeAnim}>
                <div>
                  <div className="greetingText">
                    Hello, {user?.firstName || 'Patient'}
                  </div>
                  <div className="dateText">
                    {new Date().toLocaleDateString('en-US', {
                      weekday: 'long',
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Quick Action Buttons Grid */}
          <div className="quickActionsContainer">
            <div
              className="quickActionButton"
              onClick={handleRequestAppointment}
            >
              <div className="quickActionIcon" style={{ backgroundColor: '#FF9800', boxShadow: '0 4px 6px rgba(255, 152, 0, 0.6)' }}>
                <FontAwesomeIcon icon={faCalendar} size="lg" color="#fff" style={{ textShadow: '1px 1px 3px rgba(0,0,0,0.5)' }} />
              </div>
              <div className="quickActionText">Request Appointment</div>
            </div>

            <div
              className="quickActionButton"
              onClick={() => navigate('/map')}
            >
              <div className="quickActionIcon" style={{ backgroundColor: '#4DB6AC', boxShadow: '0 4px 6px rgba(77, 182, 172, 0.6)' }}>
                <FontAwesomeIcon icon={faMap} size="lg" color="#fff" style={{ textShadow: '1px 1px 3px rgba(0,0,0,0.5)' }} />
              </div>
              <div className="quickActionText">My Locations</div>
            </div>

            <div
              className="quickActionButton"
              onClick={navigateToRecordVitals}
            >
              <div className="quickActionIcon" style={{ backgroundColor: '#EA4335', boxShadow: '0 4px 6px rgba(234, 67, 53, 0.6)' }}>
                <FontAwesomeIcon icon={faHeartbeat} size="lg" color="#fff" style={{ textShadow: '1px 1px 3px rgba(0,0,0,0.5)' }} />
              </div>
              <div className="quickActionText">Record</div>
            </div>

            <div
              className="quickActionButton"
              onClick={() => navigate('/patient-guidance')}
            >
              <div className="quickActionIcon" style={{ backgroundColor: '#9C27B0', boxShadow: '0 4px 6px rgba(156, 39, 176, 0.6)' }}>
                <FontAwesomeIcon icon={faCompass} size="lg" color="#fff" style={{ textShadow: '1px 1px 3px rgba(0,0,0,0.5)' }} />
              </div>
              <div className="quickActionText">Guidance</div>
            </div>

            <div
              className="quickActionButton"
              onClick={() => navigate('/medications')}
            >
              <div className="quickActionIcon" style={{ backgroundColor: '#4CAF50', boxShadow: '0 4px 6px rgba(76, 175, 80, 0.6)' }}>
                <FontAwesomeIcon icon={faMedkit} size="lg" color="#fff" style={{ textShadow: '1px 1px 3px rgba(0,0,0,0.5)' }} />
              </div>
              <div className="quickActionText">Add Medication</div>
            </div>
          </div>

          <div className="contentSection">
            <div className="sectionHeader">
              <div className="sectionTitleContainer">
                <div className="sectionIcon">
                  <FontAwesomeIcon icon={faHeartbeat} size="sm" color={PATIENT_COLORS.primary} />
                </div>
                <div className="sectionTitle">Your Health Summary</div>
              </div>
              <div className="sectionActions">
                <div
                  className="actionButton"
                  onClick={navigateToHealthRecords}
                >
                  <FontAwesomeIcon icon={faChartLine} size="sm" color="#FF9800" />
                  <div className="actionButtonText">History</div>
                </div>
              </div>
            </div>

            {/* Health Cards with Loading State */}
            {loadingVitals ? (
              <div className="loadingContainer">
                <div className="spinner" style={{
                  border: '4px solid rgba(0, 0, 0, 0.1)',
                  borderRadius: '50%',
                  borderTop: `4px solid ${PATIENT_COLORS.primary}`,
                  width: '40px',
                  height: '40px',
                  animation: 'spin 1s linear infinite'
                }}></div>
                <div className="loadingText">Loading your health data...</div>
              </div>
            ) : (
              <div className="cardsContainer">
                <div className="cardRow">
                  <DashboardCard
                    title="Heart Rate"
                    value={healthStats.heartRate}
                    unit="bpm"
                    icon={faHeart}
                    iconColor="#EA4335"
                    width="48%"
                    status={vitalStatus.heartRate}
                    gradientStart="#FFEBEE"
                    gradientEnd="#FFCDD2"
                  />
                  <DashboardCard
                    title="Blood Pressure"
                    value={healthStats.bloodPressure}
                    unit="mmHg"
                    icon={faDumbbell}
                    iconColor="#4285F4"
                    width="48%"
                    status={vitalStatus.bloodPressure}
                    gradientStart="#E3F2FD"
                    gradientEnd="#BBDEFB"
                  />
                </div>
                <div className="cardRow">
                  <DashboardCard
                    title="Blood Glucose"
                    value={healthStats.bloodGlucose}
                    unit="mg/dL"
                    icon={faTint}
                    iconColor="#FBBC05"
                    width="48%"
                    status={vitalStatus.bloodGlucose}
                    gradientStart="#FFF8E1"
                    gradientEnd="#FFECB3"
                  />
                  <DashboardCard
                    title="Weight"
                    value={healthStats.weight}
                    unit="kg"
                    icon={faWeight}
                    iconColor="#34A853"
                    width="48%"
                    status={vitalStatus.weight}
                    gradientStart="#E8F5E9"
                    gradientEnd="#C8E6C9"
                  />
                </div>
              </div>
            )}
          </div>

          {/* Recent Activity Section with Enhanced UI */}
          <div className="activitySection">
            <div className="sectionHeader">
              <div className="sectionTitleContainer">
                <div className="sectionIcon">
                  <FontAwesomeIcon icon={faClock} size="sm" color={PATIENT_COLORS.primary} />
                </div>
                <div className="sectionTitle">Recent Activity</div>
              </div>
              <div
                className="actionButton"
                onClick={() => navigate('/health-records')}
              >
                <FontAwesomeIcon icon={faList} size="sm" color="#FF9800" />
                <div className="actionButtonText">View All</div>
              </div>
            </div>
            {recentActivities.length === 0 ? (
              <div className="emptyActivity">
                <FontAwesomeIcon icon={faCalendar} size="3x" color="#BDBDBD" />
                <div className="emptyActivityText">No recent activity</div>
              </div>
            ) : (
              <div className="activityItems">
                {recentActivities.slice(0, 3).map((activity, index) => {
                  const date = new Date(activity.timestamp);

                  // Format date in a more readable way
                  const today = new Date();
                  const yesterday = new Date(today);
                  yesterday.setDate(yesterday.getDate() - 1);

                  let dateLabel;
                  if (date.toDateString() === today.toDateString()) {
                    dateLabel = 'Today';
                  } else if (date.toDateString() === yesterday.toDateString()) {
                    dateLabel = 'Yesterday';
                  } else {
                    dateLabel = date.toLocaleDateString('en-US', {
                      month: 'short',
                      day: 'numeric'
                    });
                  }

                  // Format time
                  const timeString = date.toLocaleTimeString('en-US', {
                    hour: '2-digit',
                    minute: '2-digit'
                  });

                  return (
                    <div key={index} className="activityItem">
                      <div className="activityIcon" style={{ backgroundColor: activity.color }}>
                        <FontAwesomeIcon icon={activity.icon} size="lg" color="#fff" />
                      </div>
                      <div className="activityContent">
                        <div className="activityTitle">
                          {activity.type === 'heartRate' && `Heart Rate: ${activity.value} bpm`}
                          {activity.type === 'bloodPressure' && `Blood Pressure: ${activity.value} mmHg`}
                          {activity.type === 'bloodGlucose' && `Blood Glucose: ${activity.value} mg/dL`}
                          {activity.type === 'weight' && `Weight: ${activity.value} kg`}
                        </div>
                        <div className="activityTimeContainer">
                          <FontAwesomeIcon icon={faCalendar} size="sm" color="#757575" className="activityTimeIcon" />
                          <span className="activityTime">{dateLabel}</span>
                          <FontAwesomeIcon icon={faClock} size="sm" color="#757575" className="activityTimeIcon" style={{marginLeft: '8px'}} />
                          <span className="activityTime">{timeString}</span>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>

          {/* Appointments and Medications Section */}
          <div className="listsContainer">
            <div className="sectionHeader">
              <div className="sectionTitleContainer">
                <div className="sectionIcon">
                  <FontAwesomeIcon icon={faCalendar} size="sm" color={PATIENT_COLORS.primary} />
                </div>
                <div className="sectionTitle">Upcoming Appointments</div>
              </div>
              <div
                className="actionButton"
                onClick={() => navigate('/appointments')}
              >
                <FontAwesomeIcon icon={faCalendar} size="sm" color="#FF9800" />
                <div className="actionButtonText">View All</div>
              </div>
            </div>

            {/* Appointments List with Loading State */}
            {refreshing ? (
              <div className="loadingContainer">
                <div className="spinner" style={{
                  border: '4px solid rgba(0, 0, 0, 0.1)',
                  borderRadius: '50%',
                  borderTop: `4px solid ${PATIENT_COLORS.primary}`,
                  width: '30px',
                  height: '30px',
                  animation: 'spin 1s linear infinite'
                }}></div>
                <div className="loadingText">Loading appointments...</div>
              </div>
            ) : (
              <UpcomingList
                title=""
                data={appointments}
                onItemPress={(item) => navigate('/appointments', { state: { id: item.id } })}
                onViewAll={() => navigate('/appointments')}
                emptyText="No upcoming appointments"
              />
            )}

            <div className="sectionHeader">
              <div className="sectionTitleContainer">
                <div className="sectionIcon">
                  <FontAwesomeIcon icon={faMedkit} size="sm" color={PATIENT_COLORS.primary} />
                </div>
                <div className="sectionTitle">Medications</div>
              </div>
              <div
                className="actionButton"
                onClick={() => navigate('/medications')}
              >
                <FontAwesomeIcon icon={faList} size="sm" color="#FF9800" />
                <div className="actionButtonText">View All</div>
              </div>
            </div>

            <UpcomingList
              title=""
              data={medications}
              onItemPress={(item) => navigate('/medication-detail', { state: { id: item.id } })}
              onViewAll={() => navigate('/medications')}
              emptyText="No current medications"
            />
          </div>

          {/* Medication Reminders Section with Enhanced UI */}
          <div className="medicationRemindersSection">
            <div className="sectionHeader">
              <div className="sectionTitleContainer">
                <div className="sectionIcon">
                  <FontAwesomeIcon icon={faBell} size="sm" color={PATIENT_COLORS.primary} />
                </div>
                <div className="sectionTitle">Medication Reminders</div>
              </div>
              <div
                className="actionButton"
                onClick={() => navigate('/medications')}
              >
                <FontAwesomeIcon icon={faList} size="sm" color="#FF9800" />
                <div className="actionButtonText">View All</div>
              </div>
            </div>
            <MedicationReminders />
          </div>

          {/* Prescriptions Section */}
          <div className="prescriptionsSection">
            <div className="sectionHeader">
              <div className="sectionTitleContainer">
                <div className="sectionIcon">
                  <FontAwesomeIcon icon={faFileText} size="sm" color={PATIENT_COLORS.primary} />
                </div>
                <div className="sectionTitle">My Prescriptions</div>
              </div>
              <div
                className="actionButton"
                onClick={() => navigate('/patient-prescriptions')}
              >
                <FontAwesomeIcon icon={faList} size="sm" color="#FF9800" />
                <div className="actionButtonText">View All</div>
              </div>
            </div>
            <RecentPrescriptions />
          </div>

          {/* Health Tips Section with Enhanced UI */}
          <div className="tipsSection">
            <div className="sectionHeader">
              <div className="sectionTitleContainer">
                <div className="sectionIcon">
                  <FontAwesomeIcon icon={faLightbulb} size="sm" color={PATIENT_COLORS.primary} />
                </div>
                <div className="sectionTitle">Health Tips</div>
              </div>
              <div className="actionButton">
                <FontAwesomeIcon icon={faSyncAlt} size="sm" color="#FF9800" />
                <div className="actionButtonText">Refresh</div>
              </div>
            </div>

            <div className="tipsWrapper">
              <div className="tipCard">
                <div className="tipIconContainer" style={{ backgroundColor: '#FFF8E1' }}>
                  <FontAwesomeIcon icon={faTint} size="lg" color="#FBBC05" />
                </div>
                <div className="tipContent">
                  <div className="tipTitle">Stay Hydrated</div>
                  <div className="tipText">Drink at least 8 glasses of water daily to maintain good blood pressure.</div>
                </div>
              </div>

              <div className="tipCard">
                <div className="tipIconContainer" style={{ backgroundColor: '#E3F2FD' }}>
                  <FontAwesomeIcon icon={faWalking} size="lg" color="#4285F4" />
                </div>
                <div className="tipContent">
                  <div className="tipTitle">Daily Exercise</div>
                  <div className="tipText">A 30-minute walk each day can significantly improve your heart health.</div>
                </div>
              </div>

              <div className="tipCard">
                <div className="tipIconContainer" style={{ backgroundColor: '#E8F5E9' }}>
                  <FontAwesomeIcon icon={faAppleAlt} size="lg" color="#34A853" />
                </div>
                <div className="tipContent">
                  <div className="tipTitle">Balanced Diet</div>
                  <div className="tipText">Include fruits and vegetables in every meal for essential nutrients.</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </DashboardLayout>
    </>
  );
}
