import React, { createContext, useState, useContext, useEffect } from 'react';
import { useAuth } from './AuthContext';
import axios from 'axios';
import { API_URL } from '../config/api';
import { toast } from 'react-toastify'; // Remplacé react-native-flash-message par react-toastify
import { auth, db } from '../config/firebase';
import { collection, addDoc, doc, getDoc, getDocs, updateDoc, query, where, orderBy, serverTimestamp } from 'firebase/firestore';

// Create context
const AppointmentContext = createContext();

// Custom hook to use the appointment context
export const useAppointments = () => {
  const context = useContext(AppointmentContext);
  if (!context) {
    throw new Error('useAppointments must be used within an AppointmentProvider');
  }
  return context;
};

// Provider component
export const AppointmentProvider = ({ children }) => {
  const { user } = useAuth();
  const [appointments, setAppointments] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [useDirectFirebase, setUseDirectFirebase] = useState(true); // Set to true to use Firebase directly

  // Fetch appointments when user changes
  useEffect(() => {
    if (user) {
      fetchAppointments();
    } else {
      setAppointments([]);
    }
  }, [user]);

  // Helper function to fetch appointments directly from Firebase
  const fetchAppointmentsFromFirebase = async () => {
    try {
      if (!auth.currentUser) {
        throw new Error('No authenticated user found');
      }

      const patientId = auth.currentUser.uid;

      // Query appointments for the current patient
      const appointmentsCollection = collection(db, 'appointments');

      // Query without orderBy to avoid index requirements
      const q = query(
        appointmentsCollection,
        where('patientId', '==', patientId)
      );

      const querySnapshot = await getDocs(q);
      const appointmentsList = [];

      querySnapshot.forEach((doc) => {
        const data = doc.data();

        // Convert Firestore timestamps to strings for easier handling
        const createdAt = data.createdAt ?
          (typeof data.createdAt.toDate === 'function' ?
            data.createdAt.toDate().toISOString() :
            new Date().toISOString()) :
          new Date().toISOString();

        const updatedAt = data.updatedAt ?
          (typeof data.updatedAt.toDate === 'function' ?
            data.updatedAt.toDate().toISOString() :
            new Date().toISOString()) :
          new Date().toISOString();

        const rescheduledAt = data.rescheduledAt ?
          (typeof data.rescheduledAt.toDate === 'function' ?
            data.rescheduledAt.toDate().toISOString() :
            (data.rescheduledAt || new Date().toISOString())) :
          null;

        // Ensure all fields are properly converted and included
        const appointment = {
          id: doc.id,
          ...data,
          createdAt,
          updatedAt,
          rescheduledAt: rescheduledAt,
          // Ensure these fields are always included if they exist
          previousDate: data.previousDate || null,
          previousTime: data.previousTime || null,
          notes: data.notes || null
        };

        appointmentsList.push(appointment);
      });

      return appointmentsList;
    } catch (error) {
      throw new Error('Failed to fetch appointments from Firebase: ' + error.message);
    }
  };

  // Fetch all appointments for the current user
  const fetchAppointments = async (forceRefresh = false) => {
    if (!user) {
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // If forceRefresh is true, clear the cache first
      if (forceRefresh) {
        setAppointments([]);
      }

      let appointmentsList = [];

      if (useDirectFirebase) {
        // Use Firebase directly
        appointmentsList = await fetchAppointmentsFromFirebase();
      } else {
        // Use API
        // Get the current Firebase Auth user
        const currentUser = auth.currentUser;
        if (!currentUser) {
          throw new Error('No authenticated user found');
        }

        const token = await currentUser.getIdToken();

        const response = await axios.get(`${API_URL}/api/appointments/patient`, {
          headers: {
            Authorization: `Bearer ${token}`
          }
        });

        appointmentsList = response.data.appointments || [];
      }

      // Make sure all appointments have the necessary fields
      const validatedAppointments = appointmentsList.map(appointment => {
        // Make sure the status is lowercase for consistency
        const status = appointment.status ? appointment.status.toLowerCase() : 'pending';

        return {
          ...appointment,
          status: status,
          // Make sure these fields exist
          date: appointment.date || new Date().toISOString().split('T')[0],
          time: appointment.time || '12:00 PM'
        };
      });

      // Update the state with validated appointments
      setAppointments(validatedAppointments);

      // Make sure appointments are properly saved in the context
      // before returning the value
      await new Promise(resolve => setTimeout(resolve, 100));

      return validatedAppointments;
    } catch (err) {
      setError('Failed to fetch appointments');
      // Remplacé showMessage par toast.error
      toast.error('Failed to load your appointments. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Helper function to save appointment directly to Firebase
  const saveAppointmentToFirebase = async (appointmentData) => {
    try {
      if (!auth.currentUser) {
        throw new Error('No authenticated user found');
      }

      const patientId = auth.currentUser.uid;

      // Prepare appointment data
      const appointmentToSave = {
        ...appointmentData,
        patientId,
        status: 'pending',
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      };

      // Save to Firestore
      const appointmentsCollection = collection(db, 'appointments');

      const docRef = await addDoc(appointmentsCollection, appointmentToSave);

      // Get the saved appointment with ID
      const savedAppointment = {
        id: docRef.id,
        ...appointmentToSave,
        createdAt: new Date().toISOString(), // Convert for client-side use
        updatedAt: new Date().toISOString()
      };

      return savedAppointment;
    } catch (error) {
      throw new Error('Failed to save appointment to Firebase: ' + error.message);
    }
  };

  // Create a new appointment
  const createAppointment = async (appointmentData) => {
    if (!user) {
      throw new Error('User not authenticated');
    }

    setLoading(true);
    setError(null);

    try {
      let savedAppointment;

      if (useDirectFirebase) {
        // Use Firebase directly
        savedAppointment = await saveAppointmentToFirebase(appointmentData);
      } else {
        // Use API
        // Get the current Firebase Auth user
        const currentUser = auth.currentUser;
        if (!currentUser) {
          throw new Error('No authenticated user found');
        }

        const token = await currentUser.getIdToken();

        const response = await axios.post(
          `${API_URL}/api/appointments/create`,
          appointmentData,
          {
            headers: {
              Authorization: `Bearer ${token}`
            }
          }
        );

        savedAppointment = response.data;
      }

      // Add the new appointment to the state
      setAppointments(prev => [...prev, savedAppointment]);

      return savedAppointment;
    } catch (err) {
      console.error('Error creating appointment:', err);
      const errorMessage = err.message || 'Failed to create appointment';
      setError(errorMessage);
      // Remplacé showMessage par toast.error
      toast.error(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // Update appointment status
  const updateAppointmentStatus = async (appointmentId, status, notes, additionalData = {}) => {
    if (!user) {
      throw new Error('User not authenticated');
    }

    setLoading(true);
    setError(null);

    try {
      // First, try to update directly in Firebase if enabled
      if (useDirectFirebase && additionalData.date && additionalData.time) {
        try {
          const appointmentRef = doc(db, 'appointments', appointmentId);
          const appointmentSnap = await getDoc(appointmentRef);

          if (!appointmentSnap.exists()) {
            throw new Error('Appointment not found');
          }

          const appointmentData = appointmentSnap.data();

          // Create update data with all fields
          const updateData = {
            status,
            updatedAt: new Date().toISOString(),
            date: additionalData.date,
            time: additionalData.time
          };

          if (notes) {
            updateData.notes = notes;
          }

          // Add any other additional data
          Object.keys(additionalData).forEach(key => {
            updateData[key] = additionalData[key];
          });

          // Update in Firebase
          await updateDoc(appointmentRef, updateData);

          // Create updated appointment object
          const updatedAppointment = {
            id: appointmentId,
            ...appointmentData,
            ...updateData
          };

          // Update in state
          setAppointments(prev =>
            prev.map(app => app.id === appointmentId ? updatedAppointment : app)
          );

          return updatedAppointment;
        } catch (firebaseError) {
          // Fall back to API if Firebase direct access fails
        }
      }

      // If direct Firebase access is disabled or failed, use the API
      const currentUser = auth.currentUser;
      if (!currentUser) {
        throw new Error('No authenticated user found');
      }

      const token = await currentUser.getIdToken();

      const response = await axios.put(
        `${API_URL}/api/appointments/${appointmentId}/status`,
        {
          status,
          notes,
          ...additionalData // Include any additional data like date, time for rescheduling
        },
        {
          headers: {
            Authorization: `Bearer ${token}`
          }
        }
      );

      // Update the appointment in the state
      setAppointments(prev =>
        prev.map(app => app.id === appointmentId ? { ...app, ...response.data } : app)
      );

      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.error || 'Failed to update appointment';
      setError(errorMessage);
      // Remplacé showMessage par toast.error
      toast.error(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // Cancel an appointment
  const cancelAppointment = async (appointmentId, reason) => {
    if (!user) {
      throw new Error('User not authenticated');
    }

    setLoading(true);
    setError(null);

    try {
      // First, check if we can use direct Firebase access
      if (useDirectFirebase) {
        try {
          // Update the appointment status directly in Firebase
          const appointmentRef = doc(db, 'appointments', appointmentId);
          const appointmentSnap = await getDoc(appointmentRef);

          if (!appointmentSnap.exists()) {
            throw new Error('Appointment not found');
          }

          const appointmentData = appointmentSnap.data();

          // Update the document with cancellation data
          const updateData = {
            status: 'cancelled',
            cancellationReason: reason || 'Cancelled by patient',
            cancelledBy: auth.currentUser.uid,
            cancelledAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          };

          await updateDoc(appointmentRef, updateData);

          // Return the updated appointment
          const updatedAppointment = {
            id: appointmentId,
            ...appointmentData,
            ...updateData
          };

          // Update the appointment in the state
          setAppointments(prev =>
            prev.map(app => app.id === appointmentId ? updatedAppointment : app)
          );

          return updatedAppointment;
        } catch (firebaseError) {
          // Fall back to API if Firebase direct access fails
        }
      }

      // If direct Firebase access is disabled or failed, use the API
      const currentUser = auth.currentUser;
      if (!currentUser) {
        throw new Error('No authenticated user found');
      }

      const token = await currentUser.getIdToken();

      const response = await axios.post(
        `${API_URL}/api/appointments/${appointmentId}/cancel`,
        { reason },
        {
          headers: {
            Authorization: `Bearer ${token}`
          }
        }
      );

      // Update the appointment in the state
      setAppointments(prev =>
        prev.map(app => app.id === appointmentId ? { ...app, ...response.data } : app)
      );

      return response.data;
    } catch (err) {
      const errorMessage = err.response?.data?.error || 'Failed to cancel appointment';
      setError(errorMessage);
      // Remplacé showMessage par toast.error
      toast.error(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // Get available time slots for a doctor on a specific date
  const getAvailableTimeSlots = async (doctorId, date) => {
    if (!user) {
      throw new Error('User not authenticated');
    }

    setLoading(true);
    setError(null);

    try {
      // Get the current Firebase Auth user
      const currentUser = auth.currentUser;
      if (!currentUser) {
        throw new Error('No authenticated user found');
      }

      const token = await currentUser.getIdToken();

      // Set a timeout for the axios request
      const response = await axios.get(
        `${API_URL}/api/appointments/available-slots/${doctorId}`,
        {
          params: { date },
          headers: {
            Authorization: `Bearer ${token}`
          },
          timeout: 8000 // 8 second timeout
        }
      );

      // Validate the response data
      if (!response.data || !Array.isArray(response.data)) {
        throw new Error('Invalid response format from server');
      }

      return response.data;
    } catch (err) {
      console.error('Error fetching available time slots:', err);

      // Handle different types of errors
      let errorMessage;

      if (err.code === 'ECONNABORTED') {
        errorMessage = 'Request timed out. Please try again.';
      } else if (err.response) {
        // The server responded with a status code outside the 2xx range
        errorMessage = err.response.data?.error || `Server error: ${err.response.status}`;
      } else if (err.request) {
        // The request was made but no response was received
        errorMessage = 'No response from server. Please check your connection.';
      } else {
        // Something else happened while setting up the request
        errorMessage = err.message || 'Failed to fetch available time slots';
      }

      setError(errorMessage);

      // Remplacé showMessage par toast.error pour les erreurs non-réseau
      if (!(err.message && (
          err.message.includes('Network Error') ||
          err.code === 'ECONNABORTED' ||
          (err.request && !err.response)))) {
        toast.error(errorMessage);
      }

      // For network errors, return a default set of time slots
      if (err.message && (
          err.message.includes('Network Error') ||
          err.code === 'ECONNABORTED' ||
          (err.request && !err.response))) {
        // Generate default time slots (9 AM to 5 PM, every hour)
        const defaultSlots = ['09:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00', '17:00'];

        // Throw a specific error that includes the default slots
        // This way the UI can still show something useful
        const enhancedError = new Error(errorMessage);
        enhancedError.defaultSlots = defaultSlots;
        throw enhancedError;
      }

      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // Helper function to parse appointment date safely
  const parseAppointmentDate = (appointment) => {
    try {
      // Ensure we have valid date and time
      if (!appointment.date || !appointment.time) {
        return new Date(); // Return current date as fallback
      }

      // Handle 12-hour format with AM/PM
      if (appointment.time.includes('AM') || appointment.time.includes('PM')) {
        // Extract hours and minutes from time string (e.g., "10:00 AM")
        const timeParts = appointment.time.replace(/\s*(AM|PM)\s*$/i, '').split(':');
        let hours = parseInt(timeParts[0], 10);
        const minutes = parseInt(timeParts[1], 10);

        // Adjust hours for PM
        if (appointment.time.includes('PM') && hours < 12) {
          hours += 12;
        }
        // Adjust for 12 AM
        if (appointment.time.includes('AM') && hours === 12) {
          hours = 0;
        }

        // Parse the date part
        const dateParts = appointment.date.split('-');
        if (dateParts.length !== 3) {
          return new Date(); // Return current date as fallback
        }

        const year = parseInt(dateParts[0], 10);
        const month = parseInt(dateParts[1], 10) - 1; // Months are 0-indexed in JS
        const day = parseInt(dateParts[2], 10);

        // Validate date parts
        if (isNaN(year) || isNaN(month) || isNaN(day) ||
            isNaN(hours) || isNaN(minutes)) {
          return new Date(); // Return current date as fallback
        }

        // Create date object
        const appointmentDate = new Date(year, month, day, hours, minutes);

        // Verify the date is valid
        if (isNaN(appointmentDate.getTime())) {
          return new Date(); // Return current date as fallback
        }

        return appointmentDate;
      }

      // Try standard formats if no AM/PM
      // First, try to create a date using the ISO format
      const isoDate = `${appointment.date}T${appointment.time.replace(/\s/g, '')}`;
      let appointmentDate = new Date(isoDate);

      // Check if the date is valid
      if (isNaN(appointmentDate.getTime())) {
        // If not valid, try with space format
        appointmentDate = new Date(`${appointment.date} ${appointment.time}`);
      }

      // If still not valid, try manual parsing
      if (isNaN(appointmentDate.getTime())) {
        // Parse the date part
        const dateParts = appointment.date.split('-');
        if (dateParts.length !== 3) {
          return new Date(); // Return current date as fallback
        }

        const year = parseInt(dateParts[0], 10);
        const month = parseInt(dateParts[1], 10) - 1; // Months are 0-indexed in JS
        const day = parseInt(dateParts[2], 10);

        // Parse the time part (assuming 24-hour format)
        const timeParts = appointment.time.split(':');
        const hours = parseInt(timeParts[0], 10);
        const minutes = parseInt(timeParts[1], 10) || 0;

        // Create date object
        appointmentDate = new Date(year, month, day, hours, minutes);
      }

      // Verify the date is valid
      if (isNaN(appointmentDate.getTime())) {
        return new Date(); // Return current date as fallback
      }

      return appointmentDate;
    } catch (error) {
      return new Date(); // Fallback to current date
    }
  };

  // Get upcoming appointments
  const getUpcomingAppointments = () => {
    const now = new Date();

    // If no appointments are available, return an empty array
    if (!appointments || appointments.length === 0) {
      return [];
    }

    // Filter upcoming appointments
    const upcomingAppointments = appointments.filter(appointment => {
      // Check if date and time fields exist
      if (!appointment.date || !appointment.time) {
        return false;
      }

      // Check if the appointment is cancelled
      const status = appointment.status ? appointment.status.toLowerCase() : '';
      if (status === 'cancelled') {
        return false;
      }

      try {
        // Compare just the dates (without time)
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        today.setHours(0, 0, 0, 0); // Reset time to midnight for proper comparison

        // Try to parse the date
        const dateParts = appointment.date.split('-');
        if (dateParts.length !== 3) {
          return false;
        }

        const year = parseInt(dateParts[0], 10);
        const month = parseInt(dateParts[1], 10) - 1;
        const day = parseInt(dateParts[2], 10);

        // Create a date at midnight for proper comparison
        const appointmentDate = new Date(year, month, day);
        appointmentDate.setHours(0, 0, 0, 0);

        // Check if the date is valid
        if (isNaN(appointmentDate.getTime())) {
          return false;
        }

        // Check if the appointment is upcoming or today
        const isUpcoming = appointmentDate >= today;

        return isUpcoming;
      } catch (error) {
        return false;
      }
    });

    // Sort appointments by date (from closest to furthest)
    upcomingAppointments.sort((a, b) => {
      const dateA = parseAppointmentDate(a);
      const dateB = parseAppointmentDate(b);

      // Check if dates are valid
      if (isNaN(dateA.getTime()) || isNaN(dateB.getTime())) {
        // Fallback to string comparison
        // Split the date strings and compare year, month, and day
        const [yearA, monthA, dayA] = a.date.split('-').map(Number);
        const [yearB, monthB, dayB] = b.date.split('-').map(Number);

        // Compare years
        if (yearA !== yearB) return yearA - yearB;
        // Compare months
        if (monthA !== monthB) return monthA - monthB;
        // Compare days
        if (dayA !== dayB) return dayA - dayB;

        // If dates are identical, sort by time
        return a.time.localeCompare(b.time);
      }

      // Compare dates
      return dateA.getTime() - dateB.getTime();
    });

    return upcomingAppointments;
  };

  // Get past appointments
  const getPastAppointments = () => {
    const now = new Date();
    return appointments.filter(appointment => {
      if (!appointment.date || !appointment.time) return false;
      const appointmentDate = parseAppointmentDate(appointment);
      return appointmentDate <= now || appointment.status === 'cancelled';
    });
  };

  // Reschedule an appointment (direct function for clarity)
  const rescheduleAppointment = async (appointmentId, newDate, newTime, notes) => {
    if (!user) {
      throw new Error('User not authenticated');
    }

    setLoading(true);
    setError(null);

    try {
      // Get the current appointment to store previous date/time
      let currentAppointment;

      if (useDirectFirebase) {
        const appointmentRef = doc(db, 'appointments', appointmentId);
        const appointmentSnap = await getDoc(appointmentRef);

        if (!appointmentSnap.exists()) {
          throw new Error('Appointment not found');
        }

        currentAppointment = {
          id: appointmentId,
          ...appointmentSnap.data()
        };
      } else {
        // Find in current state
        currentAppointment = appointments.find(app => app.id === appointmentId);
        if (!currentAppointment) {
          throw new Error('Appointment not found in current state');
        }
      }

      // Create the notes for rescheduling
      const reschedulingNotes = notes || `Rescheduled from ${currentAppointment.date} at ${currentAppointment.time} to ${newDate} at ${newTime}`;

      // Prepare additional data for rescheduling
      const additionalData = {
        date: newDate,
        time: newTime,
        previousDate: currentAppointment.date,
        previousTime: currentAppointment.time,
        rescheduledAt: new Date().toISOString(),
        rescheduledBy: user.uid
      };

      // If using direct Firebase access, update the document directly for more reliability
      if (useDirectFirebase) {
        try {
          const appointmentRef = doc(db, 'appointments', appointmentId);

          // Create update data with all fields
          const updateData = {
            date: newDate,
            time: newTime,
            status: 'pending', // Reset to pending since it's been rescheduled
            notes: reschedulingNotes,
            previousDate: currentAppointment.date,
            previousTime: currentAppointment.time,
            rescheduledAt: serverTimestamp(),
            rescheduledBy: user.uid,
            updatedAt: serverTimestamp()
          };

          // Update in Firebase
          await updateDoc(appointmentRef, updateData);

          // Wait a moment to ensure data is properly saved
          await new Promise(resolve => setTimeout(resolve, 500));

          // Get the updated document to return
          const updatedSnap = await getDoc(appointmentRef);

          // Convert timestamps to strings
          const data = updatedSnap.data();
          const createdAt = data.createdAt ?
            (typeof data.createdAt.toDate === 'function' ?
              data.createdAt.toDate().toISOString() :
              new Date().toISOString()) :
            new Date().toISOString();

          const updatedAt = data.updatedAt ?
            (typeof data.updatedAt.toDate === 'function' ?
              data.updatedAt.toDate().toISOString() :
              new Date().toISOString()) :
            new Date().toISOString();

          const rescheduledAt = data.rescheduledAt ?
            (typeof data.rescheduledAt.toDate === 'function' ?
              data.rescheduledAt.toDate().toISOString() :
              (data.rescheduledAt || new Date().toISOString())) :
            new Date().toISOString();

          // Create an object with all necessary data
          const updatedAppointment = {
            id: appointmentId,
            ...data,
            createdAt,
            updatedAt,
            rescheduledAt,
            // Make sure these fields are always included
            date: newDate,
            time: newTime,
            previousDate: currentAppointment.date,
            previousTime: currentAppointment.time,
            notes: reschedulingNotes
          };

          // Update in state
          setAppointments(prev =>
            prev.map(app => app.id === appointmentId ? updatedAppointment : app)
          );

          // Force a complete refresh of appointments
          await fetchAppointments(true); // true = forceRefresh

          // Wait a moment and refresh again
          setTimeout(async () => {
            await fetchAppointments(true);
          }, 1000);

          return updatedAppointment;
        } catch (directUpdateError) {
          // Fall back to using updateAppointmentStatus if direct update fails
        }
      }

      // Use the updateAppointmentStatus function with the additional data as fallback
      const updatedAppointment = await updateAppointmentStatus(
        appointmentId,
        'pending', // Reset to pending since it's been rescheduled
        reschedulingNotes,
        additionalData
      );

      // Force a refresh of appointments
      await fetchAppointments();

      return updatedAppointment;
    } catch (err) {
      const errorMessage = err.response?.data?.error || 'Failed to reschedule appointment';
      setError(errorMessage);
      // Remplacé showMessage par toast.error
      toast.error(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // Context value
  const value = {
    appointments,
    loading,
    error,
    fetchAppointments,
    createAppointment,
    updateAppointmentStatus,
    cancelAppointment,
    rescheduleAppointment,
    getAvailableTimeSlots,
    getUpcomingAppointments,
    getPastAppointments
  };

  return (
    <AppointmentContext.Provider value={value}>
      {children}
    </AppointmentContext.Provider>
  );
};

export default AppointmentContext;