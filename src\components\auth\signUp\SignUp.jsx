import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { CssVarsProvider, extendTheme, useColorScheme } from '@mui/joy/styles';
import GlobalStyles from '@mui/joy/GlobalStyles';
import CssBaseline from '@mui/joy/CssBaseline';
import Box from '@mui/joy/Box';
import Button from '@mui/joy/Button';
import Checkbox from '@mui/joy/Checkbox';
import Divider from '@mui/joy/Divider';
import FormControl from '@mui/joy/FormControl';
import FormLabel from '@mui/joy/FormLabel';
import IconButton from '@mui/joy/IconButton';
import Link from '@mui/joy/Link';
import Input from '@mui/joy/Input';
import Typography from '@mui/joy/Typography';
import Stack from '@mui/joy/Stack';
import Select from '@mui/joy/Select';
import Option from '@mui/joy/Option';
import DarkModeRoundedIcon from '@mui/icons-material/DarkModeRounded';
import LightModeRoundedIcon from '@mui/icons-material/LightModeRounded';
import GoogleIcon from '../icons/GoogleIcon';
import '../../../styles/auth/auth.css';

function ColorSchemeToggle(props) {
  const { onClick, ...other } = props;
  const { mode, setMode } = useColorScheme();
  const [mounted, setMounted] = React.useState(false);

  React.useEffect(() => setMounted(true), []);

  return (
    <IconButton
      aria-label="toggle light/dark mode"
      size="sm"
      variant="outlined"
      disabled={!mounted}
      onClick={(event) => {
        setMode(mode === 'light' ? 'dark' : 'light');
        onClick?.(event);
      }}
      {...other}
    >
      {mode === 'light' ? <DarkModeRoundedIcon /> : <LightModeRoundedIcon />}
    </IconButton>
  );
}

const customTheme = extendTheme({ defaultColorScheme: 'dark' });

export default function SignUp() {

  // Initialize navigate hook
    const navigate = useNavigate();

    // States to manage form data
    const [firstName, setFirstName] = useState('');
    const [lastName, setLastName] = useState('');
    const [email, setEmail] = useState('');
    const [password, setPassword] = useState('');
    const [role, setRole] = useState('');
    const [agreeToTerms, setAgreeToTerms] = useState(false);
    const [loading, setLoading] = useState(false);

      // Function to handle signup
      const handleSignup = (e) => {
        e.preventDefault();

        if (!firstName.trim() || !lastName.trim() || !email.trim() || !password.trim() || !role) {
          alert('Please fill in all required fields');
          return;
        }

        if (!agreeToTerms) {
          alert('Please agree to the terms and conditions');
          return;
        }

        setLoading(true);

        // Simulate signup request
        setTimeout(() => {
          // Show success message
          console.log('Signup successful');

          // Simulate navigation after successful signup
          setLoading(false);
          alert('Signup successful!');

          // Navigate to dashboard after successful signup
          navigate('/')
        }, 1500);
      };

      // Function to navigate to login page
      const navigateToLogin = () => {
        console.log('Redirecting to login page');
        navigate('/login');
      };


  return (
    <CssVarsProvider theme={customTheme} disableTransitionOnChange>
      <CssBaseline />
      <GlobalStyles
        styles={{
          ':root': {
            '--Form-maxWidth': '800px',
            '--Transition-duration': '0.4s', // set to `none` to disable transition
          },
          '.MuiSelect-root': {
            '&:focus-within': {
              outline: 'none !important',
              borderColor: 'transparent !important',
              boxShadow: 'none !important',
            },
            '&.Mui-focused': {
              outline: 'none !important',
              borderColor: 'transparent !important',
              boxShadow: 'none !important',
            },
          },
        }}
      />
      <Box
        sx={(theme) => ({
          width: { xs: '100%', md: '50vw' },
          transition: 'width var(--Transition-duration)',
          transitionDelay: 'calc(var(--Transition-duration) + 0.1s)',
          position: 'relative',
          zIndex: 1,
          display: 'flex',
          justifyContent: 'flex-start',
          backdropFilter: 'blur(12px)',
          backgroundColor: 'rgba(255 255 255 / 0.2)',
          right: 0,
          left: { xs: 0, md: '50vw' },
          [theme.getColorSchemeSelector('dark')]: {
            backgroundColor: 'rgba(19 19 24 / 0.4)',
          },
        })}
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            minHeight: '100dvh',
            width: '100%',
            px: 2,
          }}
        >
          <Box
            component="header"
            sx={{ py: 3, display: 'flex', justifyContent: 'space-between' }}
          >
            <Box sx={{ gap: 2, display: 'flex', alignItems: 'center' }}>
              <img src='../../../../assets/LogoNeuroCare/Neuro_Care_rounded_3.png' alt="Logo" width={50} height={50}/>
              <Typography level="title-lg" className="brand-title">Neur Care</Typography>
            </Box>
            <ColorSchemeToggle />
          </Box>
          <Box
            component="main"
            sx={{
              my: 'auto',
              py: 2,
              pb: 5,
              display: 'flex',
              flexDirection: 'column',
              gap: 2,
              width: 400,
              maxWidth: '100%',
              mx: 'auto',
              borderRadius: 'sm',
              '& form': {
                display: 'flex',
                flexDirection: 'column',
                gap: 2,
              },
              [`& .MuiFormLabel-asterisk`]: {
                visibility: 'hidden',
              },
            }}
          >
            <Stack sx={{ gap: 4, mb: 2 }}>
              <Stack sx={{ gap: 1 }}>
                <Typography component="h1" level="h3">
                  Sign Up
                </Typography>
                <Typography level="body-sm">
                Already have an account?{' '}
                  <Link
                    onClick={navigateToLogin}
                    level="title-sm"
                    className="auth-link"
                  >
                    Login!
                  </Link>
                </Typography>
              </Stack>
              <Button
                variant="soft"
                color="neutral"
                fullWidth
                startDecorator={<GoogleIcon />}
              >
                Continue with Google
              </Button>
            </Stack>
            <Divider
              sx={(theme) => ({
                [theme.getColorSchemeSelector('light')]: {
                  color: { xs: '#FFF', md: 'text.tertiary' },
                },
              })}
            >
              or
            </Divider>
            <Stack sx={{ gap: 4, mt: 2 }}>
              <form
                onSubmit={handleSignup}
              >
                <FormControl required>
                  <FormLabel>First Name</FormLabel>
                  <Input type="text" name="firstName" value={firstName} onChange={(e) => setFirstName(e.target.value)} />
                </FormControl>
                <FormControl required>
                  <FormLabel>Last Name</FormLabel>
                  <Input type="text" name="lastName" value={lastName} onChange={(e) => setLastName(e.target.value)} />
                </FormControl>
                <FormControl required>
                  <FormLabel>Email</FormLabel>
                  <Input type="email" name="email" value={email} onChange={(e) => setEmail(e.target.value)} />
                </FormControl>
                <FormControl required>
                  <FormLabel>Password</FormLabel>
                  <Input type="password" name="password" value={password} onChange={(e) => setPassword(e.target.value)} />
                </FormControl>
                <FormControl required>
                  <FormLabel>Select Role</FormLabel>
                  <Select
                    placeholder="Choose your role"
                    value={role}
                    onChange={(_, newValue) => setRole(newValue)}
                    sx={{
                      '&:focus-within': {
                        outline: 'none',
                        borderColor: 'transparent',
                        boxShadow: 'none',
                        '&::before': {
                          boxShadow: 'none',
                        },
                      },
                      '&::before': {
                        boxShadow: 'none',
                      },
                      '&.Mui-focused': {
                        outline: 'none',
                        borderColor: 'transparent',
                        boxShadow: 'none',
                      },
                      '& .MuiSelect-indicator': {
                        transition: '0.2s',
                        transform: role ? 'rotate(-180deg)' : 'rotate(0)',
                      },
                    }}
                    slotProps={{
                      listbox: {
                        sx: {
                          '&:focus': {
                            outline: 'none',
                          },
                        },
                      },
                    }}
                  >
                    <Option value="patient">Patient</Option>
                    <Option value="doctor">Doctor</Option>
                    <Option value="caregiver">Caregiver</Option>
                    <Option value="supervisor">Supervisor</Option>
                  </Select>
                </FormControl>
                <Stack sx={{ gap: 4, mt: 2 }}>
                  <Box
                    sx={{
                      display: 'flex',
                      justifyContent: 'flex-start',
                      alignItems: 'center',
                    }}
                  >
                    <Checkbox
                      size="sm"
                      label="I agree to the terms and conditions"
                      name="agreeToTerms"
                      checked={agreeToTerms}
                      onChange={(e) => setAgreeToTerms(e.target.checked)}
                    />
                  </Box>
                  <button
                    type="submit"
                    className="auth-button"
                    disabled={loading}
                  >
                    <span style={{ position: 'relative', zIndex: 2 }}>
                      {loading ? 'Signing up...' : 'Sign Up'}
                    </span>
                    <span className="button-overlay" />
                  </button>
                </Stack>
              </form>
            </Stack>
          </Box>
          <Box component="footer" sx={{ py: 3 }}>
            <Typography level="body-xs" sx={{ textAlign: 'center' }}>
              © Your company {new Date().getFullYear()}
            </Typography>
          </Box>
        </Box>
      </Box>
      <Box
        sx={(theme) => ({
          height: '100%',
          position: 'fixed',
          right: { xs: 0, md: '50vw' },
          left: 0,
          top: 0,
          bottom: 0,
          transition:
            'background-image var(--Transition-duration), left var(--Transition-duration) !important',
          transitionDelay: 'calc(var(--Transition-duration) + 0.1s)',
          backgroundColor: 'background.level1',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
          backgroundImage:
            'url(../../../../assets/Backgrounds/pexels-pixabay-40568.jpg)',
          [theme.getColorSchemeSelector('dark')]: {
            backgroundImage:
              'linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url(../../../../assets/Backgrounds/pexels-pixabay-40568.jpg)',
          },
        })}
      />

    </CssVarsProvider>
  );
}
