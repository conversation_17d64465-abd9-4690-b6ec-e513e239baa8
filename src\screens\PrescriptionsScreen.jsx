import React, { useState, useEffect } from 'react';
import '../styles/screens/prescriptionsScreen.css';
import { useAuth } from '../contexts/AuthContext';
import { firebasePrescriptionsService } from '../services/firebasePrescriptionsService';
import { ROLE_COLORS } from '../config/theme';

const PrescriptionsScreen = () => {
  const { user } = useAuth();
  const [prescriptions, setPrescriptions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedPrescription, setSelectedPrescription] = useState(null);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const doctorColors = ROLE_COLORS.doctor;

  useEffect(() => {
    loadPrescriptions();
  }, [user]);

  const loadPrescriptions = async () => {
    setLoading(true);
    try {
      const doctorId = user?.uid;
      const data = await firebasePrescriptionsService.getDoctorPrescriptions(doctorId);

      // Check if there was a permission error
      if (data._permissionError) {
        alert('You do not have permission to access prescriptions. Please contact your administrator to update Firestore security rules.');
      }

      setPrescriptions(data);
    } catch (error) {
      console.error('Error loading prescriptions from Firebase:', error);
      alert('Failed to load prescriptions. Please check your connection and try again.');
    } finally {
      setLoading(false);
    }
  };

  const handlePrescriptionPress = (prescription) => {
    setSelectedPrescription(prescription);
    setDetailModalVisible(true);
  };

  const handleSendPrescription = async (prescription) => {
    try {
      await firebasePrescriptionsService.updatePrescriptionStatus(prescription.id, 'sent');
      alert('Prescription sent successfully');
      setDetailModalVisible(false);
      loadPrescriptions();
    } catch (error) {
      console.error('Error sending prescription in Firebase:', error);
      alert('Failed to send prescription. Please check your connection and try again.');
    }
  };

  const handleDeletePrescription = async (prescription) => {
    if (window.confirm('Are you sure you want to delete this prescription?')) {
      try {
        await firebasePrescriptionsService.deletePrescription(prescription.id);
        setDetailModalVisible(false);
        loadPrescriptions();
      } catch (error) {
        console.error('Error deleting prescription from Firebase:', error);
        alert('Failed to delete prescription. Please check your connection and try again.');
      }
    }
  };

  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString('en-US', options);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return '#FFA000';
      case 'sent':
        return '#4CAF50';
      case 'filled':
        return '#2196F3';
      default:
        return '#9E9E9E';
    }
  };

  const handleGoBack = () => {
    window.history.back();
  };

  const handleNavigateToNewPrescription = () => {
    // Navigation logic for React app
    window.location.href = '/new-prescription';
  };

  const renderPrescriptionItem = (item) => (
    <div
      key={item.id}
      className="prescription-item"
      onClick={() => handlePrescriptionPress(item)}
    >
      <div className="prescription-header">
        <div className="prescription-icon" style={{ backgroundColor: '#E8F5E9' }}>
          <i className="icon-document" style={{ color: doctorColors.primary }}>📄</i>
        </div>
        <div className="prescription-info">
          <div className="prescription-title">{item.patientName}</div>
          <div className="prescription-medication">{item.medications.map(med => med.name).join(', ')}</div>
          <div className="prescription-date">{formatDate(item.createdAt)}</div>
        </div>
        <div className="status-badge" style={{ backgroundColor: getStatusColor(item.status) }}>
          <span className="status-text">{item.status}</span>
        </div>
      </div>
    </div>
  );

  return (
    <div className="container">
      <div className="header">
        <button className="back-button" onClick={handleGoBack}>
          <span>←</span>
        </button>
        <h1 className="header-title">Prescriptions</h1>
        <button className="add-button" onClick={handleNavigateToNewPrescription}>
          <span style={{ color: doctorColors.primary }}>+</span>
        </button>
      </div>

      {loading ? (
        <div className="loading-container">
          <div className="loading-spinner" style={{ borderTopColor: doctorColors.primary }}></div>
        </div>
      ) : (
        <div className="content">
          {prescriptions.length === 0 ? (
            <div className="empty-container">
              <div className="empty-icon">📄</div>
              <div className="empty-text">No prescriptions yet</div>
              <button
                className="empty-button"
                style={{ backgroundColor: doctorColors.primary }}
                onClick={handleNavigateToNewPrescription}
              >
                Create Prescription
              </button>
            </div>
          ) : (
            <div className="list-container">
              {prescriptions.map(renderPrescriptionItem)}
            </div>
          )}
        </div>
      )}

      {/* Prescription Detail Modal */}
      {detailModalVisible && selectedPrescription && (
        <div className="modal-overlay" onClick={() => setDetailModalVisible(false)}>
          <div className="modal-content" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h2 className="modal-title">Prescription Details</h2>
              <button className="close-button" onClick={() => setDetailModalVisible(false)}>
                ×
              </button>
            </div>

            <div className="detail-section">
              <span className="detail-label">Patient:</span>
              <span className="detail-value">{selectedPrescription.patientName}</span>
            </div>

            <div className="detail-section">
              <span className="detail-label">Date:</span>
              <span className="detail-value">{formatDate(selectedPrescription.createdAt)}</span>
            </div>

            <div className="detail-section">
              <span className="detail-label">Status:</span>
              <div className="status-badge" style={{ backgroundColor: getStatusColor(selectedPrescription.status) }}>
                <span className="status-text">{selectedPrescription.status}</span>
              </div>
            </div>

            <div className="medications-section">
              <h3 className="section-title">Medications</h3>
              {selectedPrescription.medications.map((med, index) => (
                <div key={index} className="medication-item">
                  <div className="medication-name">{med.name}</div>
                  <div className="medication-dosage">{med.dosage}</div>
                  <div className="medication-instructions">{med.instructions}</div>
                </div>
              ))}
            </div>

            {selectedPrescription.notes && (
              <div className="notes-section">
                <h3 className="section-title">Notes</h3>
                <div className="notes-text">{selectedPrescription.notes}</div>
              </div>
            )}

            {selectedPrescription.prescriptionImage && (
              <div className="image-section">
                <h3 className="section-title">Prescription Image</h3>
                <img
                  src={selectedPrescription.prescriptionImage}
                  alt="Prescription"
                  className="prescription-image"
                />
              </div>
            )}

            <div className="action-buttons">
              {selectedPrescription.status === 'pending' && (
                <button
                  className="action-button send-button"
                  onClick={() => handleSendPrescription(selectedPrescription)}
                >
                  <span>📤</span>
                  Send
                </button>
              )}

              <button
                className="action-button delete-button"
                onClick={() => handleDeletePrescription(selectedPrescription)}
              >
                <span>🗑️</span>
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PrescriptionsScreen;
