import React, { useState, useRef, useEffect } from 'react';
import { IoPersonOutline, IoMailOutline, IoCalendarOutline, IoMedkitOutline, IoAlertCircleOutline, IoNotificationsOutline, IoCloseOutline } from 'react-icons/io5';
import '../../styles/notifications/notificationsList.css';

const NotificationItem = ({ notification, onPress, onDismiss }) => {
  const [isVisible, setIsVisible] = useState(true);
  const itemRef = useRef(null);

  const handleDismiss = () => {
    setIsVisible(false);
    setTimeout(() => {
      onDismiss(notification.id);
    }, 300);
  };

  // Get icon based on notification type
  const getIcon = () => {
    switch (notification.type) {
      case 'profile':
        return <IoPersonOutline size={20} color="#fff" />;
      case 'message':
        return <IoMailOutline size={20} color="#fff" />;
      case 'appointment':
        return <IoCalendarOutline size={20} color="#fff" />;
      case 'medication':
        return <IoMedkitOutline size={20} color="#fff" />;
      case 'alert':
        return <IoAlertCircleOutline size={20} color="#fff" />;
      default:
        return <IoNotificationsOutline size={20} color="#fff" />;
    }
  };

  return (
    <div 
      ref={itemRef}
      className="notificationItem" 
      style={{ opacity: isVisible ? 1 : 0 }}
    >
      <div 
        className="notificationContent"
        onClick={() => onPress(notification)}
      >
        <div className="notificationInner">
          <div 
            className="iconContainer" 
            style={{ backgroundColor: notification.color || 'var(--primary)' }}
          >
            {getIcon()}
          </div>
          <div className="textContainer">
            <div className="notificationTitle">{notification.title}</div>
            <div className="notificationMessage">{notification.message}</div>
            {notification.time && (
              <div className="notificationTime">{notification.time}</div>
            )}
          </div>
        </div>
      </div>
      <div onClick={handleDismiss} className="dismissButton">
        <IoCloseOutline size={18} color="var(--text-medium)" />
      </div>
    </div>
  );
};

const NotificationsList = ({ notifications = [], onPress, onDismiss, onDismissAll }) => {
  if (notifications.length === 0) {
    return null;
  }

  return (
    <div className="container">
      <div className="header">
        <div className="headerTitle">Notifications</div>
        {notifications.length > 0 && (
          <div onClick={onDismissAll} className="clearAllButton">
            <div className="clearAllText">Clear All</div>
          </div>
        )}
      </div>
      
      <div className="list">
        {notifications.map(item => (
          <NotificationItem 
            key={item.id}
            notification={item} 
            onPress={onPress}
            onDismiss={onDismiss} 
          />
        ))}
      </div>
    </div>
  );
};

export default NotificationsList;
