import React, { useState, useEffect } from 'react';
import { Ionicons } from '@expo/vector-icons';
import { useNavigate } from 'react-router-dom';
import { ROLE_COLORS } from '../../../../config/theme';
import { db, auth } from '../../../../config/firebase';
import { collection, query, where, onSnapshot, orderBy, limit } from 'firebase/firestore';
import '../../../styles/dashboards/patient/patientGuidanceReceiver.css';

const PatientGuidanceReceiver = () => {
  const navigate = useNavigate();
  const patientColors = ROLE_COLORS.patient;

  // State variables
  const [loading, setLoading] = useState(true);
  const [guidanceList, setGuidanceList] = useState([]);
  const [selectedGuidance, setSelectedGuidance] = useState(null);
  const [showGuidanceModal, setShowGuidanceModal] = useState(false);

  useEffect(() => {
    // Listen for guidance updates when component mounts
    const unsubscribe = listenForGuidanceUpdates();

    // Clean up listener when component unmounts
    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
    };
  }, []);

  const listenForGuidanceUpdates = () => {
    try {
      const currentUser = auth.currentUser;
      if (!currentUser) {
        setLoading(false);
        return null;
      }

      const guidanceCollection = collection(db, 'patientGuidance');
      const q = query(
        guidanceCollection,
        where('patientId', '==', currentUser.uid),
        where('status', 'in', ['sent', 'active', 'completed']),
        orderBy('createdAt', 'desc'),
        limit(10)
      );

      // Set up real-time listener
      return onSnapshot(q, (snapshot) => {
        const guidanceData = [];
        snapshot.forEach((doc) => {
          const data = doc.data();
          guidanceData.push({
            id: doc.id,
            ...data,
            createdAt: data.createdAt?.toDate?.() || new Date()
          });
        });

        setGuidanceList(guidanceData);
        setLoading(false);
      }, (error) => {
        console.error('Error listening to guidance updates:', error);
        setLoading(false);
      });
    } catch (error) {
      console.error('Error setting up guidance listener:', error);
      setLoading(false);
      return null;
    }
  };

  const handleViewGuidance = (guidance) => {
    setSelectedGuidance(guidance);
    setShowGuidanceModal(true);
  };

  const handleStartNavigation = (guidance) => {
    // Navigate to the map screen with guidance data
    navigate('/map', {
      state: {
        guidanceData: guidance,
        mode: 'navigation'
      }
    });

    // Close the modal if it's open
    setShowGuidanceModal(false);
  };

  const renderGuidanceItem = (item) => {
    const formattedDate = new Date(item.createdAt).toLocaleDateString();
    const formattedTime = new Date(item.createdAt).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

    return (
      <div 
        className="guidanceItem"
        onClick={() => handleViewGuidance(item)}
        key={item.id}
      >
        <div className="guidanceIconContainer">
          <Ionicons
            name={item.routeType === 'walking' ? 'walk' : item.routeType === 'driving' ? 'car' : 'bus'}
            size={24}
            color="#fff"
          />
        </div>

        <div className="guidanceContent">
          <div className="guidanceDestination">
            {item.destination}
          </div>
          <div className="guidanceSupervisor">
            From: {item.supervisorName}
          </div>
          <div className="guidanceTime">
            {formattedDate} at {formattedTime}
          </div>
        </div>

        <Ionicons name="chevron-forward" size={20} color="#999" />
      </div>
    );
  };

  const renderEmptyList = () => (
    <div className="emptyContainer">
      <Ionicons name="navigate-circle-outline" size={60} color="#ccc" />
      <div className="emptyText">No guidance received</div>
      <div className="emptySubtext">
        When your supervisor sends you guidance, it will appear here
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className="loadingContainer">
        <div className="spinner" style={{ color: patientColors.primary }}>Loading...</div>
      </div>
    );
  }

  return (
    <div className="container">
      <div className="header">
        <div className="headerTitle">Navigation Guidance</div>
      </div>

      <div className="guidanceList">
        {guidanceList.length > 0 ? (
          guidanceList.map(item => renderGuidanceItem(item))
        ) : (
          renderEmptyList()
        )}
      </div>

      {/* Guidance Detail Modal */}
      {showGuidanceModal && (
        <div className="modalBackdrop">
          <div className="modalContent">
            <div className="modalHeader">
              <div className="modalTitle">Guidance Details</div>
              <button
                className="closeButton"
                onClick={() => setShowGuidanceModal(false)}
              >
                <Ionicons name="close" size={24} color="#333" />
              </button>
            </div>

            {selectedGuidance && (
              <div className="guidanceDetails">
                <div className="guidanceDetailRow">
                  <div className="guidanceDetailLabel">From:</div>
                  <div className="guidanceDetailValue">{selectedGuidance.supervisorName}</div>
                </div>

                <div className="guidanceDetailRow">
                  <div className="guidanceDetailLabel">Destination:</div>
                  <div className="guidanceDetailValue">{selectedGuidance.destination}</div>
                </div>

                <div className="guidanceDetailRow">
                  <div className="guidanceDetailLabel">Mode:</div>
                  <div className="routeTypeContainer">
                    <Ionicons
                      name={
                        selectedGuidance.routeType === 'walking' ? 'walk' :
                        selectedGuidance.routeType === 'driving' ? 'car' : 'bus'
                      }
                      size={20}
                      color={patientColors.primary}
                    />
                    <div className="routeTypeText">
                      {selectedGuidance.routeType.charAt(0).toUpperCase() + selectedGuidance.routeType.slice(1)}
                    </div>
                  </div>
                </div>

                {/* Route Information */}
                {selectedGuidance.routeDistance && selectedGuidance.routeDuration && (
                  <div className="routeInfoContainer">
                    <div className="routeInfoItem">
                      <Ionicons name="speedometer-outline" size={20} color={patientColors.primary} />
                      <div className="routeInfoText">
                        Distance: {selectedGuidance.routeDistance.toFixed(2)} km
                      </div>
                    </div>
                    <div className="routeInfoItem">
                      <Ionicons name="time-outline" size={20} color={patientColors.primary} />
                      <div className="routeInfoText">
                        Duration: {selectedGuidance.routeDuration > 60
                          ? `${Math.floor(selectedGuidance.routeDuration / 60)}h ${selectedGuidance.routeDuration % 60}min`
                          : `${selectedGuidance.routeDuration} min`}
                      </div>
                    </div>
                  </div>
                )}

                <div className="guidanceDetailRow">
                  <div className="guidanceDetailLabel">Sent:</div>
                  <div className="guidanceDetailValue">
                    {new Date(selectedGuidance.createdAt).toLocaleString()}
                  </div>
                </div>

                {selectedGuidance.instructions && (
                  <div className="instructionsContainer">
                    <div className="instructionsLabel">Instructions:</div>
                    <div className="instructionsText">{selectedGuidance.instructions}</div>
                  </div>
                )}

                <button
                  className="startNavigationButton"
                  onClick={() => handleStartNavigation(selectedGuidance)}
                >
                  <Ionicons name="navigate" size={20} color="#fff" />
                  <span className="startNavigationText">Start Navigation</span>
                </button>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default PatientGuidanceReceiver;
