.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.headerTitle {
  font-size: 20px;
  font-weight: bold;
  color: #212121;
}

.backButton {
  padding: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
}

.backButtonText {
  color: #4285F4;
  font-size: 16px;
  font-weight: bold;
}

.content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.loadingText {
  margin-top: 16px;
  font-size: 16px;
  color: #757575;
}

.errorContainer {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 24px;
}

.errorText {
  margin-top: 16px;
  font-size: 18px;
  color: #F44336;
  margin-bottom: 24px;
}

.medicationCard {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.medicationHeader {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.medicationTitleContainer {
  margin-left: 16px;
  flex: 1;
}

.medicationName {
  font-size: 20px;
  font-weight: bold;
  color: #212121;
}

.medicationDosage {
  font-size: 16px;
  color: #757575;
  margin-top: 4px;
}

.detailsSection {
  margin-bottom: 8px;
}

.detailRow {
  margin-bottom: 12px;
}

.detailLabel {
  font-size: 14px;
  font-weight: bold;
  color: #616161;
  margin-bottom: 4px;
}

.detailValue {
  font-size: 16px;
  color: #212121;
}

.remindersSection {
  margin-bottom: 24px;
}

.sectionHeader {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.sectionTitle {
  font-size: 18px;
  font-weight: bold;
  color: #212121;
}

.addButton {
  display: flex;
  flex-direction: row;
  align-items: center;
  background-color: #E3F2FD;
  padding: 6px 12px;
  border-radius: 16px;
  cursor: pointer;
}

.addButtonText {
  font-size: 14px;
  color: #4285F4;
  margin-left: 4px;
}

.emptyReminders {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px;
  background-color: #f9f9f9;
  border-radius: 8px;
}

.emptyRemindersText {
  font-size: 16px;
  color: #757575;
  margin-top: 8px;
  margin-bottom: 8px;
}

.emptyRemindersSubtext {
  font-size: 14px;
  color: #9E9E9E;
  text-align: center;
}

.reminderItem {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.reminderHeader {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 8px;
}

.reminderTime {
  font-size: 16px;
  color: #212121;
  flex: 1;
  margin-left: 12px;
}

.statusBadge {
  padding: 4px 8px;
  border-radius: 12px;
}

.statusText {
  font-size: 12px;
  font-weight: bold;
}

.reminderInstructions {
  font-size: 14px;
  color: #757575;
  margin-top: 8px;
  margin-left: 36px;
}

.reminderActions {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin-top: 12px;
}

.reminderAction {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 8px 12px;
  border-radius: 4px;
  flex: 1;
  margin: 0 4px;
  cursor: pointer;
}

.takenAction {
  background-color: #4CAF50;
}

.missedAction {
  background-color: #F44336;
}

.actionText {
  color: #fff;
  font-size: 14px;
  font-weight: 500;
  margin-left: 4px;
}
