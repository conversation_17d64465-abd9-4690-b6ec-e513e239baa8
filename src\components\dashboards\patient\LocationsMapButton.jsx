import React from 'react';
import { useNavigate } from 'react-router-dom';
import { IoMap } from 'react-icons/io5';
import '../../../styles/dashboards/patient//locationsMapButton.css';

const LocationsMapButton = () => {
  const navigate = useNavigate();

  const handlePress = () => {
    navigate('/map');
  };

  return (
    <button 
      className="button"
      onClick={handlePress}
    >
      <div className="buttonContent">
        <IoMap size={16} color="#fff" />
        <span className="buttonText">My Locations</span>
      </div>
    </button>
  );
};

export default LocationsMapButton;
