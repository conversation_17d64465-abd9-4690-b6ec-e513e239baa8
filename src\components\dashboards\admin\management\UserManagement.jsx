import React, { useState, useEffect } from 'react';
import { Ionicons } from '@expo/vector-icons';
import FilterUsers from './FilterUsers';
import SortControl from './SortControl';
import axios from 'axios';
import { API_URL, usersAPI } from '../../../../config/api';
import { useAuth } from '../../../../contexts/AuthContext';
import { auth } from '../../../../config/firebase';
import { ROLE_COLORS, COLORS } from '../../../../config/theme';
import '../../../../styles/dashboards/admin/userManagement.css';

const UserManagement = () => {
  const { token } = useAuth();
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortOption, setSortOption] = useState('firstName');
  const [sortDirection, setSortDirection] = useState('asc'); // 'asc' or 'desc'

  // Modal state
  const [modalVisible, setModalVisible] = useState(false);
  const [currentUser, setCurrentUser] = useState(null);
  const [modalMode, setModalMode] = useState('edit'); // 'edit' or 'create'
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    role: '',
    speciality: '',
    city: '',
    country: ''
  });

  // Filter state
  const [filters, setFilters] = useState({
    role: [],
    country: [],
    city: [],
    speciality: [],
    status: []
  });

  // Track if filter panel is open
  const [showFilters, setShowFilters] = useState(false);

  // Get unique values for each filter type
  const [filterOptions, setFilterOptions] = useState({
    role: [],
    country: [],
    city: [],
    speciality: [],
    status: ['active', 'banned', 'inactive']
  });

  // Bulk selection state
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [bulkSelectMode, setBulkSelectMode] = useState(false);
  const [bulkActionModalVisible, setBulkActionModalVisible] = useState(false);

  // User details modal
  const [detailsModalVisible, setDetailsModalVisible] = useState(false);
  const [userDetails, setUserDetails] = useState(null);

  // View mode is always card since we removed the toggle buttons
  const viewMode = 'card';

  // Get users
  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    setLoading(true);
    setError(null);

    try {
      // Ensure the user is authenticated
      const currentUser = auth.currentUser;
      if (!currentUser) {
        await new Promise(resolve => setTimeout(resolve, 1000)); // Wait for auth to initialize
        if (!auth.currentUser) {
          setError('You need to be logged in to access this page.');
          setLoading(false);
          return;
        }
      }

      // Force token refresh to ensure we have the latest
      try {
        await auth.currentUser.getIdToken(true);
      } catch (tokenError) {
        console.error('Failed to refresh token:', tokenError);
      }

      const fetchedUsers = await usersAPI.admin.getAllUsers();
      console.log('Fetched users successfully:', fetchedUsers);

      if (!fetchedUsers) {
        console.error('No users data received');
        setError('No users data received from server.');
        setLoading(false);
        return;
      }

      if (!Array.isArray(fetchedUsers)) {
        console.error('Invalid users data received:', fetchedUsers);
        setError('Received invalid data from server. Check console for details.');
        setLoading(false);
        return;
      }

      if (fetchedUsers.length === 0) {
        console.log('No users found in database');
      }

      setUsers(fetchedUsers);

      // Extract unique values for each filter type
      const roles = [...new Set(fetchedUsers.map(user => user.role))].filter(Boolean);
      const countries = [...new Set(fetchedUsers.map(user => user.location?.country))].filter(Boolean);
      const cities = [...new Set(fetchedUsers.map(user => user.location?.city))].filter(Boolean);
      const specialities = [...new Set(fetchedUsers.map(user => user.speciality))].filter(Boolean);
      const statuses = [...new Set(fetchedUsers.map(user => user.status))].filter(Boolean);

      setFilterOptions({
        role: roles,
        country: countries,
        city: cities,
        speciality: specialities,
        status: ['active', 'banned', 'inactive'].concat(
          statuses.filter(status => !['active', 'banned', 'inactive'].includes(status))
        )
      });
    } catch (err) {
      console.error('Error fetching users:', err);
      const errorMessage = err.responseData?.error || err.message || 'Failed to fetch users. Please try again.';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
  };

  const handleDelete = (uid) => {
    if (window.confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
      try {
        usersAPI.admin.deleteUser(uid);

        // Remove user from state
        setUsers(users.filter((user) => user.uid !== uid));
        alert('User deleted successfully');
      } catch (err) {
        console.error('Error deleting user:', err);
        alert('Failed to delete user. Please try again.');
      }
    }
  };

  const handleBan = async (uid, isBanned) => {
    const action = isBanned ? 'unban' : 'ban';

    if (window.confirm(`Are you sure you want to ${action} this user?`)) {
      try {
        await usersAPI.admin.banUser(uid, !isBanned);

        // Update user status in state
        setUsers(users.map(user =>
          user.uid === uid
            ? {...user, status: isBanned ? 'active' : 'banned'}
            : user
        ));

        alert(`User ${isBanned ? 'unbanned' : 'banned'} successfully`);
      } catch (err) {
        console.error(`Error ${action}ning user:`, err);
        alert(`Failed to ${action} user. Please try again.`);
      }
    }
  };

  const handleEdit = (user) => {
    console.log('Editing user:', user);

    if (!user || !user.uid) {
      alert('Cannot edit user: Invalid user data');
      return;
    }

    // Use proper defaults for all fields to avoid undefined values
    setCurrentUser(user);
    setFormData({
      firstName: user.firstName || '',
      lastName: user.lastName || '',
      email: user.email || '',
      role: user.role || 'patient',
      speciality: user.speciality || '',
      city: user.location?.city || '',
      country: user.location?.country || ''
    });

    // Set the mode and show the modal
    setModalMode('edit');
    setModalVisible(true);
  };

  const handleCreate = () => {
    setCurrentUser(null);
    setFormData({
      firstName: '',
      lastName: '',
      email: '',
      password: '',
      role: 'patient',
      speciality: '',
      city: '',
      country: ''
    });
    setModalMode('create');
    setModalVisible(true);
  };

  const handleSubmit = async () => {
    try {
      // Update user
      if (!formData.firstName || !formData.lastName) {
        return alert('Error: Please fill in all required fields');
      }

      if (!currentUser || !currentUser.uid) {
        return alert('Error: Cannot update user: Invalid user data');
      }

      console.log('Updating user with data:', formData);

      await usersAPI.admin.updateUser(currentUser.uid, {
        firstName: formData.firstName,
        lastName: formData.lastName,
        role: formData.role,
        speciality: formData.speciality,
        location: {
          city: formData.city || '',
          country: formData.country || ''
        }
      });

      // Update user in state
      setUsers(users.map(user =>
        user.uid === currentUser.uid
          ? {
              ...user,
              firstName: formData.firstName,
              lastName: formData.lastName,
              displayName: `${formData.firstName} ${formData.lastName}`,
              role: formData.role,
              speciality: formData.speciality,
              location: {
                city: formData.city || '',
                country: formData.country || ''
              }
            }
          : user
      ));

      alert('Success: User updated successfully');

      // Close modal and reset form
      setModalVisible(false);
      setFormData({
        firstName: '',
        lastName: '',
        email: '',
        password: '',
        role: '',
        speciality: '',
        city: '',
        country: ''
      });
    } catch (err) {
      console.error('Error updating user:', err);
      alert(`Error: Failed to update user. ${err.response?.data?.message || 'Please try again.'}`);
    }
  };

  const sortUsers = (users) => {
    return [...users].sort((a, b) => {
      let comparison = 0;

      if (sortOption === 'firstName') {
        comparison = (a.firstName || '').localeCompare(b.firstName || '');
      } else if (sortOption === 'lastName') {
        comparison = (a.lastName || '').localeCompare(b.lastName || '');
      } else if (sortOption === 'createdAt') {
        // Convert to date objects for proper comparison
        const dateA = a.createdAt ? new Date(a.createdAt) : new Date(0);
        const dateB = b.createdAt ? new Date(b.createdAt) : new Date(0);
        comparison = dateA - dateB;
      }

      // Reverse the comparison if sort direction is descending
      return sortDirection === 'desc' ? -comparison : comparison;
    });
  };

  // Toggle user selection for bulk actions
  const toggleUserSelection = (uid) => {
    setSelectedUsers(prevSelected => {
      if (prevSelected.includes(uid)) {
        return prevSelected.filter(id => id !== uid);
      } else {
        return [...prevSelected, uid];
      }
    });
  };

  // Toggle bulk select mode
  const toggleBulkSelectMode = () => {
    setBulkSelectMode(prev => !prev);
    if (bulkSelectMode) {
      // Clear selections when exiting bulk mode
      setSelectedUsers([]);
    }
  };

  // Select all visible users
  const selectAllUsers = () => {
    if (selectedUsers.length === sortedAndFilteredUsers.length) {
      // If all are selected, deselect all
      setSelectedUsers([]);
    } else {
      // Otherwise select all visible users
      setSelectedUsers(sortedAndFilteredUsers.map(user => user.uid));
    }
  };

  // Handle bulk delete
  const handleBulkDelete = () => {
    if (selectedUsers.length === 0) return;

    if (window.confirm(`Are you sure you want to delete ${selectedUsers.length} users? This action cannot be undone.`)) {
      try {
        // Create a copy of the selected users array
        const usersToDelete = [...selectedUsers];

        // Show loading indicator
        setLoading(true);

        // Delete each user
        for (const uid of usersToDelete) {
          usersAPI.admin.deleteUser(uid);
        }

        // Update state to remove deleted users
        setUsers(users.filter(user => !usersToDelete.includes(user.uid)));

        // Clear selections
        setSelectedUsers([]);
        setBulkActionModalVisible(false);

        alert(`${usersToDelete.length} users deleted successfully`);
      } catch (err) {
        console.error('Error deleting users:', err);
        alert('Failed to delete some users. Please try again.');
      } finally {
        setLoading(false);
      }
    }
  };

  // Handle bulk ban/unban
  const handleBulkBanUnban = (shouldBan) => {
    if (selectedUsers.length === 0) return;

    const action = shouldBan ? 'ban' : 'unban';

    if (window.confirm(`Are you sure you want to ${action} ${selectedUsers.length} users?`)) {
      try {
        // Create a copy of the selected users array
        const usersToUpdate = [...selectedUsers];

        // Show loading indicator
        setLoading(true);

        // Update each user
        for (const uid of usersToUpdate) {
          usersAPI.admin.banUser(uid, shouldBan);
        }

        // Update user status in state
        setUsers(users.map(user =>
          usersToUpdate.includes(user.uid)
            ? {...user, status: shouldBan ? 'banned' : 'active'}
            : user
        ));

        // Clear selections
        setSelectedUsers([]);
        setBulkActionModalVisible(false);

        alert(`${usersToUpdate.length} users ${shouldBan ? 'banned' : 'unbanned'} successfully`);
      } catch (err) {
        console.error(`Error ${action}ning users:`, err);
        alert(`Failed to ${action} some users. Please try again.`);
      } finally {
        setLoading(false);
      }
    }
  };

  // Show user details
  const showUserDetails = (user) => {
    setUserDetails(user);
    setDetailsModalVisible(true);
  };

  const filteredUsers = users
    .filter((user) => {
      // Text search filter
      const displayName = `${user.firstName || ''} ${user.lastName || ''}`.trim();
      const matchesSearch = displayName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           (user.email || '').toLowerCase().includes(searchTerm.toLowerCase());

      // Check if user matches all active filters
      const matchesRole = filters.role.length === 0 ||
        filters.role.includes(user.role);

      const matchesCountry = filters.country.length === 0 ||
        filters.country.includes(user.location?.country);

      const matchesCity = filters.city.length === 0 ||
        filters.city.includes(user.location?.city);

      const matchesSpeciality = filters.speciality.length === 0 ||
        filters.speciality.includes(user.speciality);

      const matchesStatus = filters.status.length === 0 ||
        filters.status.includes(user.status);

      return matchesSearch && matchesRole && matchesCountry && matchesCity && matchesSpeciality && matchesStatus;
    });

  const sortedAndFilteredUsers = sortUsers(filteredUsers);

  // Count active filters
  const activeFilterCount = Object.values(filters).flat().length;

  // Function to get role badge color
  const getRoleBadgeColor = (role) => {
    const roleLower = role?.toLowerCase();
    if (roleLower === 'doctor' && ROLE_COLORS.doctor) {
      return ROLE_COLORS.doctor.primary;
    } else if (roleLower === 'patient' && ROLE_COLORS.patient) {
      return ROLE_COLORS.patient.primary;
    } else if ((roleLower === 'caregiver' || roleLower === 'nurse') && ROLE_COLORS.caregiver) {
      return ROLE_COLORS.caregiver.primary;
    } else if (roleLower === 'supervisor' && ROLE_COLORS.supervisor) {
      return ROLE_COLORS.supervisor.primary;
    } else if (roleLower === 'admin' && ROLE_COLORS.admin) {
      return ROLE_COLORS.admin.primary;
    } else {
      return 'rgb(158, 158, 158)';
    }
  };

  // Function to get status indicator color
  const getStatusColor = (status) => {
    if (status === 'banned') return 'rgb(244, 67, 54)';
    return status === 'active' ? 'rgb(76, 175, 80)' : 'rgb(158, 158, 158)';
  };

  // Render user card
  const renderUserCard = (item) => {
    const isBanned = item.status === 'banned';
    const displayName = `${item.firstName || ''} ${item.lastName || ''}`.trim() || 'Unknown User';
    const isSelected = selectedUsers.includes(item.uid);

    return (
      <div
        className={`card ${isSelected ? 'selected-card' : ''}`}
        key={item.uid || item.id || Math.random().toString()}
      >
        {bulkSelectMode && (
          <div
            className="checkbox-container"
            onClick={() => toggleUserSelection(item.uid)}
          >
            <div className={`checkbox ${isSelected ? 'checkbox-selected' : ''}`}>
              {isSelected && (
                <Ionicons name="checkmark" size={16} color="#FFFFFF" />
              )}
            </div>
          </div>
        )}

        <div
          className="card-header"
          onClick={() => showUserDetails(item)}
        >
          <div className="profile-section">
            <img
              src={
                item.profilePicture
                  ? item.profilePicture
                  : require('../../../../../assets/ImagesTest/avatar.png').default
              }
              className="profile-picture"
              style={{ borderColor: getRoleBadgeColor(item.role) }}
              alt="User profile"
            />
            <div className="name-section">
              <h3 className="user-name">{displayName}</h3>
              <p className="user-email">{item.email}</p>

              <div className="status-container">
                <div className="status-indicator" style={{ backgroundColor: getStatusColor(item.status) }}></div>
                <span className="status-text">
                  {item.status === 'active' ? 'Active' :
                   item.status === 'banned' ? 'Banned' :
                   item.status === 'inactive' ? 'Inactive' : item.status || 'Unknown'}
                </span>
              </div>
            </div>
          </div>

          <div
            className="role-badge"
            style={{ backgroundColor: getRoleBadgeColor(item.role) }}
          >
            <span className="role-badge-text">
              {item.role ? item.role.charAt(0).toUpperCase() + item.role.slice(1) : 'Unknown'}
            </span>
          </div>
        </div>

        <div className="card-divider"></div>

        <div className="card-content">
          <div className="info-row">
            <div className="info-column">
              <span className="info-label">Speciality</span>
              <span className="info-value">{item.speciality || 'N/A'}</span>
            </div>

            <div className="info-column">
              <span className="info-label">Location</span>
              <span className="info-value">
                {item.location?.city && item.location?.country
                  ? `${item.location.city}, ${item.location.country}`
                  : 'N/A'
                }
              </span>
            </div>
          </div>

          <div className="info-row">
            <div className="info-column">
              <span className="info-label">Created</span>
              <span className="info-value">
                {item.createdAt
                  ? new Date(item.createdAt).toLocaleDateString()
                  : 'N/A'
                }
              </span>
            </div>

            <div className="info-column">
              <span className="info-label">User Code</span>
              <span className="info-value">
                {item.userCode || 'N/A'}
              </span>
            </div>
          </div>
        </div>

        <div className="card-divider"></div>

        <div className="card-actions">
          <button
            className="action-button view-button"
            onClick={() => showUserDetails(item)}
          >
            <Ionicons name="eye-outline" size={20} color="#3498DB" className="action-icon" />
            <span className="action-button-text" style={{ color: '#3498DB' }}>View</span>
          </button>

          <button
            className="action-button edit-button"
            onClick={() => handleEdit(item)}
          >
            <Ionicons name="create-outline" size={20} color="#4285F4" className="action-icon" />
            <span className="action-button-text" style={{ color: '#4285F4' }}>Edit</span>
          </button>

          <button
            className="action-button delete-button"
            onClick={() => {
              // Check if uid exists before attempting to delete
              if (item && item.uid) {
                handleDelete(item.uid);
              } else {
                alert('Error: Cannot delete user: Missing user ID');
              }
            }}
          >
            <Ionicons name="trash-outline" size={20} color="#F44336" className="action-icon" />
            <span className="action-button-text" style={{ color: '#F44336' }}>Delete</span>
          </button>

          <button
            className={`action-button ${isBanned ? 'unban-button' : 'ban-button'}`}
            onClick={() => handleBan(item.uid, isBanned)}
          >
            <Ionicons
              name={isBanned ? "shield-checkmark-outline" : "shield-outline"}
              size={20}
              color={isBanned ? "#4CAF50" : "#FF9800"}
              className="action-icon"
            />
            <span className="action-button-text" style={{ color: isBanned ? "#4CAF50" : "#FF9800" }}>
              {isBanned ? 'Unban' : 'Ban'}
            </span>
          </button>
        </div>
      </div>
    );
  };

  // Render form input field
  const renderFormField = (label, placeholder, name, secure = false) => (
    <div className="form-group">
      <label className="form-label">{label}</label>
      <input
        className="form-input"
        type={secure ? "password" : "text"}
        placeholder={placeholder}
        value={formData[name]}
        onChange={(e) => setFormData({...formData, [name]: e.target.value})}
      />
    </div>
  );

  // User form modal
  const renderUserModal = () => (
    <div className={`modal-overlay ${modalVisible ? 'visible' : ''}`}>
      <div className="modal-content">
        <div className="modal-header">
          <h2 className="modal-title">
            {modalMode === 'edit' ? 'Edit User' : 'Create User'}
          </h2>
          <button
            className="close-button"
            onClick={() => setModalVisible(false)}
          >
            <Ionicons name="close" size={24} color="#34495E" />
          </button>
        </div>

        <div className="modal-body">
          <div className="form-row">
            <div className="form-group form-group-half">
              <label className="form-label">First Name <span className="required-star">*</span></label>
              <div className="input-container">
                <Ionicons name="person-outline" size={20} color="#95A5A6" className="input-icon" />
                <input
                  className="form-input"
                  placeholder="Enter first name"
                  value={formData.firstName}
                  onChange={(e) => setFormData({...formData, firstName: e.target.value})}
                />
              </div>
            </div>

            <div className="form-group form-group-half">
              <label className="form-label">Last Name <span className="required-star">*</span></label>
              <div className="input-container">
                <Ionicons name="person-outline" size={20} color="#95A5A6" className="input-icon" />
                <input
                  className="form-input"
                  placeholder="Enter last name"
                  value={formData.lastName}
                  onChange={(e) => setFormData({...formData, lastName: e.target.value})}
                />
              </div>
            </div>
          </div>

          <div className="form-group">
            <label className="form-label">Email</label>
            <div className={`input-container ${modalMode === 'edit' ? 'disabled' : ''}`}>
              <Ionicons name="mail-outline" size={20} color="#95A5A6" className="input-icon" />
              <input
                className="form-input"
                value={formData.email}
                onChange={(e) => setFormData({...formData, email: e.target.value})}
                disabled={modalMode === 'edit'}
                placeholder={modalMode === 'edit' ? '' : 'Enter email address'}
              />
            </div>
          </div>

          {modalMode === 'create' && (
            <div className="form-group">
              <label className="form-label">Password <span className="required-star">*</span></label>
              <div className="input-container">
                <Ionicons name="lock-closed-outline" size={20} color="#95A5A6" className="input-icon" />
                <input
                  className="form-input"
                  type="password"
                  placeholder="Enter password"
                  value={formData.password}
                  onChange={(e) => setFormData({...formData, password: e.target.value})}
                />
              </div>
            </div>
          )}

          <div className="form-group">
            <label className="form-label">Role <span className="required-star">*</span></label>
            <div className="role-options">
              {['patient', 'doctor', 'caregiver', 'supervisor', 'admin'].map((role) => (
                <button
                  key={role}
                  className={`role-option ${formData.role === role ? 'role-option-selected' : ''}`}
                  style={{ backgroundColor: getRoleBadgeColor(role) }}
                  onClick={() => setFormData({...formData, role})}
                >
                  <Ionicons
                    name={
                      role === 'patient' ? 'person-outline' :
                      role === 'doctor' ? 'medkit-outline' :
                      role === 'caregiver' ? 'heart-outline' :
                      role === 'supervisor' ? 'people-outline' :
                      'shield-outline'
                    }
                    size={18}
                    color="#FFFFFF"
                    className="role-icon"
                  />
                  <span className="role-option-text">
                    {role.charAt(0).toUpperCase() + role.slice(1)}
                  </span>
                </button>
              ))}
            </div>
          </div>

          <div className="form-group">
            <label className="form-label">Speciality</label>
            <div className="input-container">
              <Ionicons name="fitness-outline" size={20} color="#95A5A6" className="input-icon" />
              <input
                className="form-input"
                placeholder="Enter speciality (if applicable)"
                value={formData.speciality}
                onChange={(e) => setFormData({...formData, speciality: e.target.value})}
              />
            </div>
          </div>

          <div className="form-row">
            <div className="form-group form-group-half">
              <label className="form-label">City</label>
              <div className="input-container">
                <Ionicons name="location-outline" size={20} color="#95A5A6" className="input-icon" />
                <input
                  className="form-input"
                  placeholder="Enter city"
                  value={formData.city}
                  onChange={(e) => setFormData({...formData, city: e.target.value})}
                />
              </div>
            </div>

            <div className="form-group form-group-half">
              <label className="form-label">Country</label>
              <div className="input-container">
                <Ionicons name="globe-outline" size={20} color="#95A5A6" className="input-icon" />
                <input
                  className="form-input"
                  placeholder="Enter country"
                  value={formData.country}
                  onChange={(e) => setFormData({...formData, country: e.target.value})}
                />
              </div>
            </div>
          </div>

          <button
            className="submit-button"
            onClick={handleSubmit}
          >
            <Ionicons name="save-outline" size={24} color="#FFFFFF" style={{ marginRight: 8 }} />
            <span className="submit-button-text">
              {modalMode === 'edit' ? 'Update User' : 'Create User'}
            </span>
          </button>
        </div>
      </div>
    </div>
  );

  // User details modal
  const renderUserDetailsModal = () => (
    <div className={`modal-overlay ${detailsModalVisible ? 'visible' : ''}`}>
      <div className="modal-content">
        <div className="modal-header">
          <h2 className="modal-title">User Details</h2>
          <button
            className="close-button"
            onClick={() => setDetailsModalVisible(false)}
          >
            <Ionicons name="close" size={24} color="#34495E" />
          </button>
        </div>

        {userDetails && (
          <div className="modal-body">
            <div className="user-details-header">
              <img
                src={
                  userDetails.profilePicture
                    ? userDetails.profilePicture
                    : require('../../../../../assets/ImagesTest/avatar.png').default
                }
                className="details-profile-picture"
                alt="User profile"
              />
              <div className="user-details-info">
                <h2 className="details-name">
                  {`${userDetails.firstName || ''} ${userDetails.lastName || ''}`.trim() || 'Unknown User'}
                </h2>
                <p className="details-email">{userDetails.email}</p>

                <div className="details-badge-container">
                  <div className="details-badge" style={{ backgroundColor: getRoleBadgeColor(userDetails.role) }}>
                    <span className="details-badge-text">
                      {userDetails.role ? userDetails.role.charAt(0).toUpperCase() + userDetails.role.slice(1) : 'Unknown'}
                    </span>
                  </div>

                  <div className="details-badge" style={{
                    backgroundColor:
                      userDetails.status === 'active' ? '#4CAF50' :
                      userDetails.status === 'banned' ? '#F44336' :
                      '#9E9E9E'
                  }}>
                    <span className="details-badge-text">
                      {userDetails.status === 'active' ? 'Active' :
                       userDetails.status === 'banned' ? 'Banned' :
                       userDetails.status === 'inactive' ? 'Inactive' :
                       userDetails.status || 'Unknown'}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <div className="details-section">
              <h3 className="details-section-title">Personal Information</h3>

              <div className="details-row">
                <div className="details-item">
                  <span className="details-label">User ID</span>
                  <span className="details-value">{userDetails.uid || 'N/A'}</span>
                </div>
              </div>

              <div className="details-row">
                <div className="details-item">
                  <span className="details-label">User Code</span>
                  <span className="details-value">{userDetails.userCode || 'N/A'}</span>
                </div>
              </div>

              <div className="details-row">
                <div className="details-item">
                  <span className="details-label">Speciality</span>
                  <span className="details-value">{userDetails.speciality || 'N/A'}</span>
                </div>
              </div>

              <div className="details-row">
                <div className="details-item">
                  <span className="details-label">Location</span>
                  <span className="details-value">
                    {userDetails.location?.city && userDetails.location?.country
                      ? `${userDetails.location.city}, ${userDetails.location.country}`
                      : 'N/A'
                    }
                  </span>
                </div>
              </div>
            </div>

            <div className="details-section">
              <h3 className="details-section-title">Account Information</h3>

              <div className="details-row">
                <div className="details-item">
                  <span className="details-label">Created</span>
                  <span className="details-value">
                    {userDetails.createdAt
                      ? new Date(userDetails.createdAt).toLocaleString()
                      : 'N/A'
                    }
                  </span>
                </div>
              </div>

              <div className="details-row">
                <div className="details-item">
                  <span className="details-label">Last Updated</span>
                  <span className="details-value">
                    {userDetails.updatedAt
                      ? new Date(userDetails.updatedAt).toLocaleString()
                      : 'N/A'
                    }
                  </span>
                </div>
              </div>

              <div className="details-row">
                <div className="details-item">
                  <span className="details-label">Email Verified</span>
                  <span className="details-value">
                    {userDetails.emailVerified ? 'Yes' : 'No'}
                  </span>
                </div>
              </div>
            </div>

            <div className="details-actions">
              <button
                className="details-action-button edit-action-button"
                onClick={() => {
                  setDetailsModalVisible(false);
                  handleEdit(userDetails);
                }}
              >
                <Ionicons name="create-outline" size={20} color="#FFFFFF" />
                <span className="details-action-button-text">Edit User</span>
              </button>

              <button
                className={`details-action-button ${userDetails.status === 'banned' ? 'unban-action-button' : 'ban-action-button'}`}
                onClick={() => {
                  setDetailsModalVisible(false);
                  handleBan(userDetails.uid, userDetails.status === 'banned');
                }}
              >
                <Ionicons
                  name={userDetails.status === 'banned' ? "shield-checkmark-outline" : "shield-outline"}
                  size={20}
                  color="#FFFFFF"
                />
                <span className="details-action-button-text">
                  {userDetails.status === 'banned' ? 'Unban User' : 'Ban User'}
                </span>
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );

  // Bulk action modal
  const renderBulkActionModal = () => (
    <div className={`modal-overlay ${bulkActionModalVisible ? 'visible' : ''}`}>
      <div className="modal-content bulk-modal">
        <div className="modal-header">
          <h2 className="modal-title">Bulk Actions</h2>
          <button
            className="close-button"
            onClick={() => setBulkActionModalVisible(false)}
          >
            <Ionicons name="close" size={24} color="#34495E" />
          </button>
        </div>

        <div className="bulk-action-content">
          <p className="bulk-action-text">
            {selectedUsers.length} users selected
          </p>

          <div className="bulk-action-buttons">
            <button
              className="bulk-action-button bulk-delete-button"
              onClick={handleBulkDelete}
              disabled={selectedUsers.length === 0}
            >
              <Ionicons name="trash-outline" size={24} color="#FFFFFF" />
              <span className="bulk-action-button-text">Delete</span>
            </button>

            <button
              className="bulk-action-button bulk-ban-button"
              onClick={() => handleBulkBanUnban(true)}
              disabled={selectedUsers.length === 0}
            >
              <Ionicons name="shield-outline" size={24} color="#FFFFFF" />
              <span className="bulk-action-button-text">Ban</span>
            </button>

            <button
              className="bulk-action-button bulk-unban-button"
              onClick={() => handleBulkBanUnban(false)}
              disabled={selectedUsers.length === 0}
            >
              <Ionicons name="shield-checkmark-outline" size={24} color="#FFFFFF" />
              <span className="bulk-action-button-text">Unban</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  if (loading && users.length === 0) {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <p className="loading-text">Loading users...</p>
      </div>
    );
  }

  if (error && users.length === 0) {
    return (
      <div className="error-container">
        <Ionicons name="alert-circle" size={48} color="#F44336" />
        <p className="error-text">{error}</p>
        <button className="retry-button" onClick={fetchUsers}>
          <span className="retry-button-text">Retry</span>
        </button>
      </div>
    );
  }

  return (
    <div className="container">
      <div className="header">
        <h1 className="title">All users</h1>
      </div>

      <div className="search-container">
        <div className="search-box">
          <Ionicons name="search" size={20} color="#666" className="search-icon" />
          <input
            className="search-input"
            placeholder="Search by name or email..."
            value={searchTerm}
            onChange={handleSearch}
          />
        </div>

        <button
          className="filter-button"
          onClick={() => setShowFilters(!showFilters)}
        >
          <Ionicons name="options-outline" size={20} color="#FFFFFF" style={{ marginRight: 8 }} />
          <span className="filter-button-text">Filters</span>
          {activeFilterCount > 0 && (
            <div className="filter-badge">
              <span className="filter-badge-text">{activeFilterCount}</span>
            </div>
          )}
        </button>
      </div>

      {showFilters && (
        <FilterUsers
          options={filterOptions}
          filters={filters}
          setFilters={setFilters}
        />
      )}

      <div className="sort-container">
        <div className="sort-control-wrapper">
          <span className="sort-label">Sort by:</span>
          <SortControl
            options={[
              { value: 'firstName', label: 'First Name' },
              { value: 'lastName', label: 'Last Name' },
              { value: 'createdAt', label: 'Created Date' },
              { value: 'role', label: 'Role' }
            ]}
            value={sortOption}
            onSelect={setSortOption}
            direction={sortDirection}
            onDirectionChange={setSortDirection}
          />
        </div>

        {loading && (
          <div className="loading-indicator"></div>
        )}
      </div>

      <div className="result-container">
        <p className="result-count">
          {sortedAndFilteredUsers.length} users found
        </p>

        {selectedUsers.length > 0 && (
          <p className="selected-count">
            {selectedUsers.length} selected
          </p>
        )}
      </div>

      <div className="card-list">
        {sortedAndFilteredUsers.length > 0 ? (
          sortedAndFilteredUsers.map(item => renderUserCard(item))
        ) : (
          <div className="empty-container">
            <Ionicons name="people-outline" size={64} color="#CCCCCC" />
            <p className="empty-text">No users found</p>
            <p className="empty-subtext">Try adjusting your filters or search terms</p>
          </div>
        )}
      </div>

      {renderUserModal()}
      {renderUserDetailsModal()}
      {renderBulkActionModal()}
    </div>
  );
};

export default UserManagement;