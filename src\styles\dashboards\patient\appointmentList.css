.container {
  width: 100%;
}

.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.card {
  margin: 8px;
  cursor: pointer;
  transition: transform 0.2s;
}

.card:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.cardHeader {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.statusChip {
  height: 24px;
  padding: 0 12px;
  border-radius: 16px;
  display: inline-flex;
  align-items: center;
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.modal {
  background-color: white;
  padding: 20px;
  margin: 20px;
  border-radius: 8px;
  max-height: 80%;
  overflow-y: auto;
  position: relative;
  max-width: 600px;
  margin: 40px auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.divider {
  margin: 16px 0;
  border-bottom: 1px solid #e0e0e0;
}

.cancelButton {
  background-color: #F44336;
  color: white;
  margin: 16px 0;
  border: none;
  padding: 10px 16px;
  border-radius: 4px;
  cursor: pointer;
}

.cancelButton:hover {
  background-color: #d32f2f;
}

.noAppointments {
  text-align: center;
  margin-top: 20px;
  font-size: 16px;
}

.pagination {
  margin: 16px 0;
  display: flex;
  justify-content: center;
}

.modalBackdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.listItem {
  padding: 12px 0;
}

.listItemTitle {
  font-weight: bold;
  margin-bottom: 4px;
}

.listItemDescription {
  color: #666;
}
