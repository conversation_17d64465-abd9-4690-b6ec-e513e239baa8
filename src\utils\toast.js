import { toast } from 'react-toastify';

// Configuration options that can be overridden when calling toast functions
const defaultOptions = {
  position: "top-right",
  autoClose: 5000,
  hideProgressBar: false,
  closeOnClick: true,
  pauseOnHover: true,
  draggable: true,
};

// Success toast notification
export const showSuccessToast = (message, options = {}) => {
  return toast.success(message, { ...defaultOptions, ...options });
};

// Error toast notification
export const showErrorToast = (message, options = {}) => {
  return toast.error(message, { ...defaultOptions, ...options });
};

// Info toast notification
export const showInfoToast = (message, options = {}) => {
  return toast.info(message, { ...defaultOptions, ...options });
};

// Warning toast notification
export const showWarningToast = (message, options = {}) => {
  return toast.warning(message, { ...defaultOptions, ...options });
};

// Default toast notification
export const showToast = (message, options = {}) => {
  return toast(message, { ...defaultOptions, ...options });
};
