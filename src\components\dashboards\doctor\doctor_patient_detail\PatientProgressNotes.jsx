import React, { useState, useEffect } from 'react';
import { IoDocumentText, IoAdd, Io<PERSON><PERSON>ch, IoFilter } from 'react-icons/io5';
import { useNavigate } from 'react-router-dom';
import '../../../styles/dashboards/doctor/doctor_patient_detail/patientProgressNotes.css';

const PatientProgressNotes = ({ patientId, token }) => {
  const navigate = useNavigate();
  const [notes, setNotes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('all');

  // Mock data for demonstration
  useEffect(() => {
    // In a real app, this would fetch data from an API
    const mockNotes = generateMockNotes();
    setNotes(mockNotes);
    setLoading(false);
  }, [patientId]);

  // Generate mock notes for demonstration
  const generateMockNotes = () => {
    const categories = ['General', 'Medication', 'Treatment', 'Follow-up', 'Lab Results'];
    const mockNotes = [];
    
    for (let i = 0; i < 5; i++) {
      const date = new Date();
      date.setDate(date.getDate() - i * 3);
      
      mockNotes.push({
        id: `note-${i}`,
        title: `Progress Note ${i + 1}`,
        content: `This is a sample progress note content for demonstration purposes. It contains information about the patient's condition, treatment plan, and follow-up instructions.`,
        category: categories[Math.floor(Math.random() * categories.length)],
        date: date.toISOString(),
        doctorName: 'Dr. Smith',
      });
    }
    
    return mockNotes;
  };

  // Filter notes based on search query and selected filter
  const filteredNotes = notes.filter(note => {
    const matchesSearch = note.title.toLowerCase().includes(searchQuery.toLowerCase()) || 
                         note.content.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesFilter = selectedFilter === 'all' || note.category.toLowerCase() === selectedFilter.toLowerCase();
    
    return matchesSearch && matchesFilter;
  });

  // Format date for display
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Handle creating a new note
  const handleCreateNote = () => {
    navigate('/create-progress-note', { state: { patientId } });
  };

  // Handle viewing a note
  const handleViewNote = (noteId) => {
    navigate(`/progress-note/${noteId}`);
  };

  return (
    <div className="patient-progress-notes">
      <div className="notes-header">
        <h3 className="notes-title">Patient Progress Notes</h3>
        <button 
          className="create-note-button"
          onClick={handleCreateNote}
        >
          <IoAdd size={20} />
          <span>New Note</span>
        </button>
      </div>
      
      <div className="notes-filters">
        <div className="search-container">
          <IoSearch className="search-icon" />
          <input
            type="text"
            className="search-input"
            placeholder="Search notes..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        
        <div className="filter-container">
          <IoFilter className="filter-icon" />
          <select
            className="filter-select"
            value={selectedFilter}
            onChange={(e) => setSelectedFilter(e.target.value)}
          >
            <option value="all">All Categories</option>
            <option value="general">General</option>
            <option value="medication">Medication</option>
            <option value="treatment">Treatment</option>
            <option value="follow-up">Follow-up</option>
            <option value="lab results">Lab Results</option>
          </select>
        </div>
      </div>
      
      {loading ? (
        <div className="loading-container">
          <div className="spinner">Loading...</div>
        </div>
      ) : filteredNotes.length > 0 ? (
        <div className="notes-list">
          {filteredNotes.map(note => (
            <div 
              key={note.id} 
              className="note-card"
              onClick={() => handleViewNote(note.id)}
            >
              <div className="note-header">
                <div className="note-category">
                  <IoDocumentText size={16} />
                  <span>{note.category}</span>
                </div>
                <div className="note-date">{formatDate(note.date)}</div>
              </div>
              <h4 className="note-title">{note.title}</h4>
              <p className="note-content">{note.content.substring(0, 100)}...</p>
              <div className="note-footer">
                <span className="note-doctor">{note.doctorName}</span>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="empty-notes">
          <IoDocumentText size={48} color="#e0e0e0" />
          <p>No progress notes found</p>
          {patientId ? (
            <button 
              className="create-first-note-button"
              onClick={handleCreateNote}
            >
              Create First Note
            </button>
          ) : (
            <p className="select-patient-message">Please select a patient to view or create notes</p>
          )}
        </div>
      )}
    </div>
  );
};

export default PatientProgressNotes;
