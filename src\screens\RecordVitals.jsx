import React, { useState, useEffect, useRef } from 'react';
import '../styles/screens/recordVitals.css';
import { useVitals } from '../contexts/VitalsContext';

const RecordVitals = ({ navigation }) => {
  // State for vital sign selection
  const [selectedVital, setSelectedVital] = useState('heartRate');

  // State for vital sign values
  const [vitalValues, setVitalValues] = useState({
    heartRate: '',
    bloodPressureSystolic: '',
    bloodPressureDiastolic: '',
    bloodGlucose: '',
    weight: ''
  });

  // State for notes
  const [notes, setNotes] = useState('');

  // State for voice recognition
  const [isListening, setIsListening] = useState(false);
  const [voiceResults, setVoiceResults] = useState('');
  const [partialResults, setPartialResults] = useState([]);
  const [voiceError, setVoiceError] = useState('');

  // State for form submission
  const [submitting, setSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);

  // Get vitals context
  const { saveVital } = useVitals();

  // Recording state
  const [recordingDuration, setRecordingDuration] = useState(0);
  const recordingTimer = useRef(null);
  const [recording, setRecording] = useState(null);

  // Web Speech API recognition
  const recognition = useRef(null);

  // Initialize Web Speech API
  useEffect(() => {
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      recognition.current = new SpeechRecognition();
      recognition.current.continuous = true;
      recognition.current.interimResults = true;
      recognition.current.lang = 'en-US';

      recognition.current.onstart = () => {
        console.log('Speech recognition started');
        setPartialResults(['Listening...']);
      };

      recognition.current.onresult = (event) => {
        let interimTranscript = '';
        let finalTranscript = '';

        for (let i = event.resultIndex; i < event.results.length; i++) {
          const transcript = event.results[i].transcript;
          if (event.results[i].isFinal) {
            finalTranscript += transcript;
          } else {
            interimTranscript += transcript;
          }
        }

        if (interimTranscript) {
          setPartialResults([interimTranscript]);
        }

        if (finalTranscript) {
          setVoiceResults(finalTranscript);
          processRecognizedSpeech(finalTranscript);
        }
      };

      recognition.current.onerror = (event) => {
        console.error('Speech recognition error:', event.error);
        setVoiceError(`Speech recognition error: ${event.error}`);
        setIsListening(false);
      };

      recognition.current.onend = () => {
        console.log('Speech recognition ended');
        setIsListening(false);
        if (recordingTimer.current) {
          clearInterval(recordingTimer.current);
          recordingTimer.current = null;
        }
      };
    }

    return () => {
      if (recordingTimer.current) {
        clearInterval(recordingTimer.current);
      }
    };
  }, []);

  // Start voice recognition
  const startListening = async () => {
    try {
      setVoiceError('');
      setVoiceResults('');
      setPartialResults([]);
      setRecordingDuration(0);

      if (!recognition.current) {
        setVoiceError('Speech recognition is not supported in this browser.');
        return;
      }

      setIsListening(true);
      recognition.current.start();

      // Start timer to track recording duration
      recordingTimer.current = setInterval(() => {
        setRecordingDuration(prev => prev + 1);
      }, 1000);

    } catch (e) {
      console.error('Error starting speech recognition:', e);
      setVoiceError(`Failed to start: ${e.message || 'Unknown error'}`);
      setIsListening(false);
    }
  };

  // Stop voice recognition
  const stopListening = () => {
    try {
      if (recordingTimer.current) {
        clearInterval(recordingTimer.current);
        recordingTimer.current = null;
      }

      if (recognition.current && isListening) {
        recognition.current.stop();
      }

      setIsListening(false);
    } catch (e) {
      console.error("Error stopping speech recognition:", e);
      setIsListening(false);
    }
  };

  // Process recognized speech to extract vital values
  const processRecognizedSpeech = (text) => {
    console.log('Processing speech result:', text);

    // Extract numbers from the speech
    const numbers = text.match(/\d+/g);

    if (!numbers || numbers.length === 0) {
      setVoiceError('No numbers detected in speech. Please try again.');
      return;
    }

    // Update values based on the selected vital type
    if (selectedVital === 'bloodPressure') {
      if (numbers.length >= 2) {
        setVitalValues(prev => ({
          ...prev,
          bloodPressureSystolic: numbers[0],
          bloodPressureDiastolic: numbers[1]
        }));
      } else {
        setVoiceError("Please provide both systolic and diastolic values for blood pressure.");
      }
    } else {
      setVitalValues(prev => ({
        ...prev,
        [selectedVital]: numbers[0]
      }));
    }
  };

  // Handle vital type selection
  const handleVitalSelect = (vitalType) => {
    setSelectedVital(vitalType);
  };

  // Handle form submission
  const handleSubmit = async () => {
    // Validate vital values based on type
    if (selectedVital === 'bloodPressure') {
      if (!vitalValues.bloodPressureSystolic || !vitalValues.bloodPressureDiastolic) {
        alert('Please enter both systolic and diastolic values.');
        return;
      }
    } else if (!vitalValues[selectedVital]) {
      alert(`Please enter a value for ${getVitalTitle(selectedVital)}.`);
      return;
    }

    try {
      setSubmitting(true);

      // Prepare data for Firebase
      const vitalData = {
        vitalType: selectedVital,
        values: selectedVital === 'bloodPressure'
          ? {
              systolic: vitalValues.bloodPressureSystolic,
              diastolic: vitalValues.bloodPressureDiastolic
            }
          : { value: vitalValues[selectedVital] },
        notes: notes,
        recordMethod: isListening ? 'voice' : 'manual',
        recordType: 'self'
      };

      console.log('Attempting to save vital data:', vitalData);

      // Save to Firebase using VitalsContext
      const savedVital = await saveVital(vitalData);
      console.log('Vital record saved to Firebase:', savedVital);

      if (!savedVital) {
        alert('Could not save to the cloud database. Your data has been saved locally and will sync when connectivity is restored.');
        return;
      }

      // Show success
      setSubmitSuccess(true);

      // Reset form after delay
      setTimeout(() => {
        resetForm();
        setSubmitSuccess(false);
      }, 2000);

    } catch (error) {
      console.error('Error saving vitals to Firebase:', error);

      if (error.code === 'permission-denied') {
        alert('You do not have permission to save vital signs. Your data has been saved locally.');
      } else {
        alert(`Failed to save vital signs: ${error.message}. Please try again.`);
      }
    } finally {
      setSubmitting(false);
    }
  };

  // Reset form
  const resetForm = () => {
    setSelectedVital('heartRate');
    setVitalValues({
      heartRate: '',
      bloodPressureSystolic: '',
      bloodPressureDiastolic: '',
      bloodGlucose: '',
      weight: ''
    });
    setNotes('');
    setVoiceResults('');
    setPartialResults([]);
    setVoiceError('');
  };

  // Helper function to get title for vital type
  const getVitalTitle = (vitalType) => {
    switch (vitalType) {
      case 'heartRate':
        return 'Heart Rate';
      case 'bloodPressure':
        return 'Blood Pressure';
      case 'bloodGlucose':
        return 'Blood Glucose';
      case 'weight':
        return 'Weight';
      default:
        return vitalType;
    }
  };

  // Helper function to get unit for vital type
  const getVitalUnit = (vitalType) => {
    switch (vitalType) {
      case 'heartRate':
        return 'bpm';
      case 'bloodPressure':
        return 'mmHg';
      case 'bloodGlucose':
        return 'mg/dL';
      case 'weight':
        return 'kg';
      default:
        return '';
    }
  };

  return (
    <div className="container">
      <div className="header">
        <div>
          <h1 className="header-title">Record Your Vitals</h1>
          <p className="header-subtitle">Use voice commands to easily record your health metrics</p>
        </div>
        <button
          className="history-button"
          onClick={() => navigation?.navigate('HealthRecords')}
        >
          <span className="icon">⏰</span>
          <span className="history-button-text">History</span>
        </button>
      </div>

      {/* Vital Sign Selection */}
      <div className="section">
        <h2 className="section-title">Select Vital Sign</h2>

        <div className="vital-type-container">
          <button
            className={`vital-type-button ${selectedVital === 'heartRate' ? 'selected' : ''}`}
            onClick={() => handleVitalSelect('heartRate')}
          >
            <span className="icon heart">❤️</span>
            <span className="vital-type-text">Heart Rate</span>
          </button>

          <button
            className={`vital-type-button ${selectedVital === 'bloodPressure' ? 'selected' : ''}`}
            onClick={() => handleVitalSelect('bloodPressure')}
          >
            <span className="icon fitness">💪</span>
            <span className="vital-type-text">Blood Pressure</span>
          </button>

          <button
            className={`vital-type-button ${selectedVital === 'bloodGlucose' ? 'selected' : ''}`}
            onClick={() => handleVitalSelect('bloodGlucose')}
          >
            <span className="icon water">💧</span>
            <span className="vital-type-text">Blood Glucose</span>
          </button>

          <button
            className={`vital-type-button ${selectedVital === 'weight' ? 'selected' : ''}`}
            onClick={() => handleVitalSelect('weight')}
          >
            <span className="icon body">🏃</span>
            <span className="vital-type-text">Weight</span>
          </button>
        </div>
      </div>

      {/* Voice Input */}
      <div className="section">
        <h2 className="section-title">Voice Input</h2>

        <button
          className={`voice-button ${isListening ? 'active' : ''}`}
          onClick={isListening ? stopListening : startListening}
          disabled={submitting}
        >
          <span className={`icon mic ${isListening ? 'active' : ''}`}>
            {isListening ? '🎤' : '🎙️'}
          </span>
          <span className={`voice-button-text ${isListening ? 'active' : ''}`}>
            {isListening ? "Listening... Click to stop" : "Click to speak"}
          </span>

          {isListening && (
            <div className="recording-dot" />
          )}
        </button>

        {/* Voice Instructions */}
        <div className="voice-instructions">
          <h3 className="voice-instructions-title">Voice Input Instructions:</h3>
          {selectedVital === 'heartRate' && (
            <p className="voice-instructions-text">Say: "My heart rate is 78 beats per minute" or just "78"</p>
          )}
          {selectedVital === 'bloodPressure' && (
            <p className="voice-instructions-text">Say: "My blood pressure is 125 over 82" or "125 82"</p>
          )}
          {selectedVital === 'bloodGlucose' && (
            <p className="voice-instructions-text">Say: "My blood glucose is 92 milligrams per deciliter" or just "92"</p>
          )}
          {selectedVital === 'weight' && (
            <p className="voice-instructions-text">Say: "My weight is 73 kilograms" or just "73"</p>
          )}
        </div>

        {/* Recording Duration */}
        {isListening && (
          <div className="recording-duration-container">
            <p className="recording-duration-text">
              Recording: {Math.floor(recordingDuration / 60)}:{(recordingDuration % 60).toString().padStart(2, '0')}
            </p>
          </div>
        )}

        {/* Show partial results while listening */}
        {isListening && partialResults.length > 0 && (
          <div className="partial-results-box">
            <p className="partial-results-text">
              {partialResults[0]}
            </p>
          </div>
        )}

        {voiceResults && (
          <div className="result-box">
            <p className="voice-result-text">
              Heard: "{voiceResults}"
            </p>
          </div>
        )}

        {voiceError && (
          <p className="voice-error-text">
            {voiceError}
          </p>
        )}
      </div>

      {/* Manual Input */}
      <div className="section">
        <h2 className="section-title">Manual Input</h2>

        {selectedVital === 'bloodPressure' ? (
          <div className="blood-pressure-inputs">
            <div className="input-group">
              <label className="input-label">Systolic (mmHg)</label>
              <input
                type="number"
                className="input"
                value={vitalValues.bloodPressureSystolic}
                onChange={(e) => setVitalValues(prev => ({
                  ...prev,
                  bloodPressureSystolic: e.target.value
                }))}
                placeholder="e.g., 120"
              />
            </div>

            <div className="input-group">
              <label className="input-label">Diastolic (mmHg)</label>
              <input
                type="number"
                className="input"
                value={vitalValues.bloodPressureDiastolic}
                onChange={(e) => setVitalValues(prev => ({
                  ...prev,
                  bloodPressureDiastolic: e.target.value
                }))}
                placeholder="e.g., 80"
              />
            </div>
          </div>
        ) : (
          <div className="input-group">
            <label className="input-label">
              {getVitalTitle(selectedVital)} ({getVitalUnit(selectedVital)})
            </label>
            <input
              type="number"
              className="input"
              value={vitalValues[selectedVital]}
              onChange={(e) => setVitalValues(prev => ({
                ...prev,
                [selectedVital]: e.target.value
              }))}
              placeholder={`e.g., ${selectedVital === 'heartRate' ? '75' :
                            selectedVital === 'bloodGlucose' ? '95' : '70'}`}
            />
          </div>
        )}

        {/* Notes */}
        <div className="input-group">
          <label className="input-label">Notes (Optional)</label>
          <textarea
            className="input notes-input"
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
            placeholder="Add any additional notes here"
            rows={4}
          />
        </div>
      </div>

      {/* Submit Button */}
      <button
        className={`submit-button ${submitting ? 'submitting' : ''} ${submitSuccess ? 'success' : ''}`}
        onClick={handleSubmit}
        disabled={submitting || submitSuccess}
      >
        {submitting ? (
          <div className="loading-spinner" />
        ) : submitSuccess ? (
          <>
            <span className="icon">✅</span>
            <span className="submit-button-text">Saved Successfully</span>
          </>
        ) : (
          <>
            <span className="icon">💾</span>
            <span className="submit-button-text">Save Vital Signs</span>
          </>
        )}
      </button>
    </div>
  );
};

export default RecordVitals;
