.animated-container {
  flex: 1;
  background-color: rgba(0,0,0,0.8);
  opacity: 0;
  transform: translateY(50px);
  animation: fadeIn 400ms forwards;
}

@keyframes fadeIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

.loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f5f5f5;
}

.loading-text {
  margin-top: 16px;
  font-size: 16px;
  color: #555;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
}

.header-left {
  display: flex;
  align-items: center;
}

.back-button {
  margin-right: 12px;
  cursor: pointer;
}

.header-title {
  font-size: 18px;
  font-weight: bold;
  color: #fff;
}

.header-right {
  display: flex;
  align-items: center;
}

.timer {
  font-size: 16px;
  color: #fff;
  font-weight: 500;
}

.content {
  display: flex;
  flex: 1;
}

.video-container {
  flex: 2;
  background-color: #000;
  border-radius: 8px;
  margin: 8px;
  overflow: hidden;
  position: relative;
}

.start-call-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
}

.start-call-button {
  background-color: var(--doctor-primary);
  padding: 20px;
  border-radius: 50px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.start-call-text {
  color: #fff;
  margin-top: 10px;
  font-size: 16px;
  font-weight: bold;
}

.no-messages-text {
  text-align: center;
  color: #999;
  margin-top: 20px;
}

.video-controls {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16px;
  background-color: rgba(0,0,0,0.5);
  z-index: 10;
}

.control-button {
  width: 44px;
  height: 44px;
  border-radius: 22px;
  background-color: rgba(255,255,255,0.2);
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 8px;
  cursor: pointer;
}

.control-button-off {
  background-color: #F44336;
}

.control-button-active {
  background-color: #4CAF50;
}

.end-call-button {
  background-color: #F44336;
  transform: rotate(135deg);
}

.side-panel {
  flex: 1;
  background-color: #fff;
  border-radius: 8px;
  margin: 8px;
  overflow: hidden;
  max-width: 320px;
}

.panel-tabs {
  display: flex;
  border-bottom: 1px solid #e0e0e0;
}

.panel-tab {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px 0;
  background-color: #f5f5f5;
  cursor: pointer;
}

.active-tab {
  background-color: #fff;
  border-bottom: 2px solid var(--doctor-primary);
}

.tab-text {
  margin-left: 4px;
  font-size: 14px;
  color: #757575;
}

.active-tab-text {
  color: var(--doctor-primary);
  font-weight: 500;
}

.panel-content {
  flex: 1;
  animation: slideIn 300ms forwards;
}

@keyframes slideIn {
  from {
    transform: translateX(20px);
  }
  to {
    transform: translateX(0);
  }
}

.chat-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 12px;
}

.message-item {
  margin-bottom: 12px;
  padding: 12px;
  border-radius: 8px;
  max-width: 85%;
}

.doctor-message {
  align-self: flex-end;
  background-color: var(--doctor-primary-lighter);
  border-bottom-right-radius: 0;
}

.patient-message {
  align-self: flex-start;
  background-color: #f0f0f0;
  border-bottom-left-radius: 0;
}

.message-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.message-sender {
  font-size: 12px;
  font-weight: 500;
  color: #616161;
}

.message-time {
  font-size: 10px;
  color: #9e9e9e;
  margin-left: 8px;
}

.message-text {
  font-size: 14px;
  color: #212121;
}

.message-status {
  margin-top: 4px;
  align-self: flex-end;
}

.status-text {
  font-size: 10px;
  color: #757575;
  font-style: italic;
}

.error-status {
  display: flex;
  align-items: center;
}

.error-status-text {
  font-size: 10px;
  color: #F44336;
  margin-right: 4px;
}

.retry-text {
  font-size: 10px;
  color: var(--doctor-primary);
  font-weight: bold;
  text-decoration: underline;
  cursor: pointer;
}

.input-container {
  display: flex;
  align-items: center;
  padding: 8px;
  border-top: 1px solid #e0e0e0;
}

.input {
  flex: 1;
  background-color: #f5f5f5;
  border-radius: 20px;
  padding: 8px 16px;
  max-height: 100px;
  font-size: 14px;
  resize: none;
  border: none;
}

.send-button {
  margin-left: 8px;
  width: 40px;
  height: 40px;
  border-radius: 20px;
  background-color: #f0f0f0;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.participants-container {
  flex: 1;
  padding: 16px;
}

.participant {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #e0e0e0;
}

.participant-avatar {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  background-color: var(--doctor-primary);
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 12px;
}

.patient-avatar {
  background-color: var(--patient-primary);
}

.avatar-text {
  color: #fff;
  font-size: 18px;
  font-weight: bold;
}

.participant-info {
  flex: 1;
}

.participant-name {
  font-size: 16px;
  font-weight: 500;
  color: #212121;
}

.participant-role {
  font-size: 12px;
  color: #757575;
}

.participant-status {
  display: flex;
  width: 50px;
  justify-content: space-between;
}

.invite-container {
  margin-top: 24px;
  display: flex;
  justify-content: center;
}

.invite-button {
  display: flex;
  align-items: center;
  background-color: var(--doctor-primary);
  padding: 8px 16px;
  border-radius: 20px;
  cursor: pointer;
}

.invite-text {
  color: #fff;
  margin-left: 8px;
  font-weight: 500;
}

.notes-container {
  flex: 1;
  padding: 16px;
}

.notes-title {
  font-size: 18px;
  font-weight: bold;
  color: #212121;
  margin-bottom: 4px;
}

.notes-subtitle {
  font-size: 14px;
  color: #757575;
  margin-bottom: 16px;
}

.notes-input {
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 12px;
  font-size: 14px;
  color: #212121;
  min-height: 200px;
  width: 100%;
  resize: vertical;
  border: none;
}

.notes-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 16px;
}

.notes-button {
  display: flex;
  align-items: center;
  background-color: var(--doctor-primary);
  padding: 8px 16px;
  border-radius: 20px;
  flex: 1;
  justify-content: center;
  margin: 0 4px;
  cursor: pointer;
}

.prescription-button {
  background-color: #4CAF50;
}

.notes-button-text {
  color: #fff;
  margin-left: 8px;
  font-weight: 500;
}
