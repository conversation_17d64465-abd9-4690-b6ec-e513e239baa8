import React, { useMemo } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import '../styles/appNavigator.css';

// Components
import HealthRecords from '../components/dashboards/patient/healthRecords/HealthRecords';
import Appointments from '../components/dashboards/patient/appointments/Appointments';
import Medications from '../components/dashboards/patient/medications/Medications';
import MedicationDetail from '../components/dashboards/patient/medications/MedicationDetail';
import PatientPrescriptions from '../components/dashboards/patient/prescriptions/PatientPrescriptions';
import DoctorAppointments from '../components/dashboards/doctor/appointments/DoctorAppointments';
import RecordVitals from '../screens/RecordVitals';
import MapScreen from '../screens/MapScreen';
import PatientVideoCallScreen from '../screens/PatientVideoCallScreen';
import PatientGuidanceScreen from '../screens/PatientGuidanceScreen';
import SupervisorPatientTrackingScreen from '../screens/SupervisorPatientTrackingScreen';
import SupervisorGuidanceScreen from '../screens/SupervisorGuidanceScreen';
import SupervisorAssignCaregiverScreen from '../screens/SupervisorAssignCaregiverScreen';
import SupervisorCaregiversScreen from '../screens/SupervisorCaregiversScreen';
import SupervisorAppointmentsScreen from '../screens/SupervisorAppointmentsScreen';
import CaregiverPatientsScreen from '../screens/CaregiverPatientsScreen';
import CaregiverPatientDetailScreen from '../screens/CaregiverPatientDetailScreen';
import CaregiverRecordVitalsScreen from '../screens/CaregiverRecordVitalsScreen';
import CaregiverRecordActivityScreen from '../screens/CaregiverRecordActivityScreen';
import CaregiverPatientNavigationScreen from '../screens/CaregiverPatientNavigationScreen';
import CaregiverAppointmentsScreen from '../screens/CaregiverAppointmentsScreen';
import SupervisorCaregiverManagementScreen from '../screens/SupervisorCaregiverManagementScreen';
import SupervisorPatientHealthScreen from '../screens/SupervisorPatientHealthScreen';
import ScanCaregiverQRScreen from '../screens/ScanCaregiverQRScreen';
import { ROLE_COLORS } from '../config/theme';

// Components
import Login from '../components/login/Login';
import SignUp from '../components/signUp/SignUp';
import HomeScreen from '../components/homeScreen/HomeScreen';
import Dashboard from '../screens/Dashboard';
import Profile from '../components/profile/Profile';
import ForgotPassword from '../components/auth/ForgotPassword';
import ProfileCompletion from '../components/profile/ProfileCompletion';
import UserQRCode from '../components/profile/UserQRCode';
import AddPatientScreen from '../screens/AddPatientScreen';
import ScanPatientQRScreen from '../screens/ScanPatientQRScreen';
import PatientListScreen from '../components/dashboards/PatientListScreen';
import ProfileCompletionScreen from '../screens/ProfileCompletionScreen';
import ProfileScreen from '../screens/ProfileScreen';
import UserQRCodeScreen from '../screens/UserQRCodeScreen';
import NotificationsScreen from '../screens/NotificationsScreen';
import UserManagement from '../components/dashboards/admin/management/UserManagement';
import ChatRoomScreen from '../screens/ChatRoomScreen';
import PatientConsultationScreen from '../screens/PatientConsultationScreen';
import PatientHealthMonitoringScreen from '../screens/PatientHealthMonitoringScreen';
import PrescriptionsScreen from '../screens/PrescriptionsScreen';
import NewPrescriptionScreen from '../screens/NewPrescriptionScreen';
import MyDoctorsScreen from '../screens/MyDoctorsScreen';
import MessagesScreen from '../screens/MessagesScreen';
import SettingsScreen from '../screens/SettingsScreen';
import ChangePasswordScreen from '../screens/ChangePasswordScreen';

// Layout components
import Header from '../components/layout/Header';
import ProtectedRoute from '../components/auth/ProtectedRoute';

const AppNavigator = () => {
  const { user, loading, isRegistering } = useAuth();

  // Get the primary color based on user role
  const primaryColor = useMemo(() => {
    if (!user) return ROLE_COLORS.default.primary;
    const role = user.role?.toLowerCase() || 'default';
    return (ROLE_COLORS[role] || ROLE_COLORS.default).primary;
  }, [user]);

  if (loading) {
    return <div className="loading">Loading...</div>;
  }

  return (
    <Router>
      {user && !isRegistering && <Header primaryColor={primaryColor} />}
      <div className="app-container">
        <Routes>
          {!user || isRegistering ? (
            // Auth Routes
            <>
              <Route path="/" element={<HomeScreen />} />
              <Route path="/login" element={<Login />} />
              <Route path="/signup" element={<SignUp />} />
              <Route path="/forgot-password" element={<ForgotPassword />} />
              <Route path="*" element={<Navigate to="/" replace />} />
            </>
          ) : (
            // App Routes - Only show when user is authenticated AND not in registration process
            <>
              <Route path="/" element={<Dashboard />} />
              <Route path="/profile" element={<ProfileScreen />} />
              <Route path="/qr-code" element={<UserQRCodeScreen />} />
              <Route path="/add-patient" element={<AddPatientScreen />} />
              <Route path="/scan-patient" element={<ScanPatientQRScreen />} />
              <Route path="/patients" element={<PatientListScreen />} />
              <Route path="/notifications" element={<NotificationsScreen />} />
              <Route path="/user-management" element={<UserManagement />} />
              <Route path="/health-records" element={<HealthRecords />} />
              <Route path="/appointments" element={<Appointments />} />
              <Route path="/record-vitals" element={<RecordVitals />} />
              <Route path="/medications" element={<Medications />} />
              <Route path="/medication/:id" element={<MedicationDetail />} />
              <Route path="/chat/:id" element={<ChatRoomScreen />} />
              <Route path="/patient-consultation" element={<PatientConsultationScreen />} />
              <Route path="/patient-health" element={<PatientHealthMonitoringScreen />} />
              <Route path="/prescriptions" element={<PrescriptionsScreen />} />
              <Route path="/new-prescription" element={<NewPrescriptionScreen />} />
              <Route path="/map" element={<MapScreen />} />
              <Route path="/patient-guidance" element={<PatientGuidanceScreen />} />
              <Route path="/supervisor-tracking" element={<SupervisorPatientTrackingScreen />} />
              <Route path="/supervisor-guidance" element={<SupervisorGuidanceScreen />} />
              <Route path="/assign-caregiver" element={<SupervisorAssignCaregiverScreen />} />
              <Route path="/scan-caregiver" element={<ScanCaregiverQRScreen />} />
              <Route path="/caregivers" element={<SupervisorCaregiversScreen />} />
              <Route path="/caregiver-management" element={<SupervisorCaregiverManagementScreen />} />
              <Route path="/supervisor-patient-health" element={<SupervisorPatientHealthScreen />} />
              <Route path="/my-doctors" element={<MyDoctorsScreen />} />
              <Route path="/doctor-appointments" element={<DoctorAppointments />} />
              <Route path="/my-prescriptions" element={<PatientPrescriptions />} />
              <Route path="/video-call/:id" element={<PatientVideoCallScreen />} />
              <Route path="/messages" element={<MessagesScreen />} />
              <Route path="/settings" element={<SettingsScreen />} />
              <Route path="/change-password" element={<ChangePasswordScreen />} />
              <Route path="/caregiver-patients" element={<CaregiverPatientsScreen />} />
              <Route path="/caregiver-patient/:id" element={<CaregiverPatientDetailScreen />} />
              <Route path="/caregiver-record-vitals/:id" element={<CaregiverRecordVitalsScreen />} />
              <Route path="/caregiver-record-activity/:id" element={<CaregiverRecordActivityScreen />} />
              <Route path="/caregiver-navigation/:id" element={<CaregiverPatientNavigationScreen />} />
              <Route path="/caregiver-appointments" element={<CaregiverAppointmentsScreen />} />
              <Route path="/supervisor-appointments" element={<SupervisorAppointmentsScreen />} />
              <Route path="*" element={<Navigate to="/" replace />} />
            </>
          )}
        </Routes>
      </div>
      <ToastContainer position="top-center" />
    </Router>
  );
};

export default AppNavigator;
