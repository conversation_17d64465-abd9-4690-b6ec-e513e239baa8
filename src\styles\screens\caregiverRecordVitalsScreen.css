.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  overflow-y: auto;
}

.header {
  padding: 20px;
  background-color: #fff;
  border-bottom: 1px solid #e0e0e0;
}

.header-title {
  font-size: 22px;
  font-weight: bold;
  color: #333;
  margin: 0 0 5px 0;
}

.patient-name {
  font-size: 16px;
  color: #666;
  margin: 0;
}

.vital-type-container {
  padding: 20px;
  background-color: #fff;
  margin: 15px;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin: 0 0 15px 0;
}

.vital-type-scroll {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  overflow-x: auto;
  padding-bottom: 5px;
}

.vital-type-button {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  border-radius: 20px;
  border: 1px solid #e0e0e0;
  background-color: #f5f5f5;
  color: #888;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  font-size: 14px;
}

.vital-type-button:hover {
  background-color: #e8e8e8;
}

.vital-type-button.active {
  background-color: #f0f8ff;
  font-weight: 500;
}

.vital-type-button i {
  margin-right: 5px;
  font-size: 16px;
}

.form-container {
  padding: 20px;
  background-color: #fff;
  margin: 15px;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.input-container {
  margin-bottom: 15px;
}

.input-label {
  display: block;
  font-size: 14px;
  color: #666;
  margin-bottom: 5px;
}

.input {
  width: 100%;
  border: 1px solid #e0e0e0;
  border-radius: 5px;
  padding: 10px;
  font-size: 16px;
  background-color: #f9f9f9;
  box-sizing: border-box;
  transition: border-color 0.2s ease;
}

.input:focus {
  outline: none;
  border-color: #007bff;
  background-color: #fff;
}

.notes-input {
  min-height: 100px;
  resize: vertical;
  font-family: inherit;
}

.submit-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 15px;
  border: none;
  border-radius: 5px;
  margin-top: 20px;
  color: #fff;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: opacity 0.2s ease;
}

.submit-button:hover:not(:disabled) {
  opacity: 0.9;
}

.submit-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.submit-button i {
  margin-right: 10px;
  font-size: 18px;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid transparent;
  border-top: 2px solid #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error {
  padding: 20px;
  text-align: center;
  color: #f44336;
  font-size: 16px;
}

.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 15px;
  border-radius: 5px;
  color: white;
  z-index: 1000;
  max-width: 300px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Icon classes for different vital types */
.icon-heart::before { content: "♥"; }
.icon-fitness::before { content: "💪"; }
.icon-water::before { content: "💧"; }
.icon-scale::before { content: "⚖"; }
.icon-thermometer::before { content: "🌡"; }
.icon-pulse::before { content: "📊"; }
.icon-save::before { content: "💾"; }

/* Responsive design */
@media (max-width: 768px) {
  .vital-type-container,
  .form-container {
    margin: 10px;
  }
  
  .header {
    padding: 15px;
  }
  
  .vital-type-scroll {
    flex-direction: column;
    align-items: stretch;
  }
  
  .vital-type-button {
    justify-content: center;
    margin-bottom: 5px;
  }
}

@media (max-width: 480px) {
  .header-title {
    font-size: 20px;
  }
  
  .patient-name {
    font-size: 14px;
  }
  
  .section-title {
    font-size: 14px;
  }
  
  .input {
    font-size: 14px;
  }
  
  .submit-button {
    font-size: 14px;
    padding: 12px;
  }
}
