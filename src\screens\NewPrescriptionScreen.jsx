import React, { useState, useEffect } from 'react';
import '../styles/screens/newPrescriptionScreen.css';
import { useAuth } from '../contexts/AuthContext';
import { firebasePrescriptionsService } from '../services/firebasePrescriptionsService';
import DoctorPatientSelectorModal from '../components/doctor/DoctorPatientSelectorModal';
import { ROLE_COLORS } from '../config/theme';

const NewPrescriptionScreen = () => {
  const { user } = useAuth();
  const doctorColors = ROLE_COLORS.doctor;

  // Form states
  const [patientId, setPatientId] = useState('');
  const [patientName, setPatientName] = useState('');
  const [medications, setMedications] = useState([]);
  const [notes, setNotes] = useState('');
  const [prescriptionImage, setPrescriptionImage] = useState(null);

  // UI states
  const [loading, setLoading] = useState(false);
  const [showPatientSelector, setShowPatientSelector] = useState(false);

  // Simplified medication handling
  const [medicationName, setMedicationName] = useState('');
  const [medicationDosage, setMedicationDosage] = useState('');
  const [medicationInstructions, setMedicationInstructions] = useState('');

  const handlePatientSelected = (patient) => {
    setPatientId(patient.id);
    setPatientName(patient.name);
  };

  const handleAddMedication = () => {
    // Validate input
    if (!medicationName.trim()) {
      alert('Medication name is required');
      return;
    }

    // Create new medication object
    const newMedication = {
      name: medicationName,
      dosage: medicationDosage,
      instructions: medicationInstructions
    };

    // Add to medications array
    const updatedMedications = [...medications, newMedication];
    setMedications(updatedMedications);

    // Clear input fields
    setMedicationName('');
    setMedicationDosage('');
    setMedicationInstructions('');

    // Show success message
    alert('Medication added successfully');
  };

  const handleRemoveMedication = (index) => {
    const updatedMedications = [...medications];
    updatedMedications.splice(index, 1);
    setMedications(updatedMedications);
  };

  const pickImage = async () => {
    try {
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = 'image/*';
      input.onchange = (e) => {
        const file = e.target.files[0];
        if (file) {
          const reader = new FileReader();
          reader.onload = (e) => {
            setPrescriptionImage(e.target.result);
          };
          reader.readAsDataURL(file);
        }
      };
      input.click();
    } catch (error) {
      console.error('Error picking image:', error);
      alert('Failed to pick image');
    }
  };

  const takePicture = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ video: true });
      const video = document.createElement('video');
      video.srcObject = stream;
      video.play();

      // Create a simple camera interface
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      video.addEventListener('loadedmetadata', () => {
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;

        // Draw video frame to canvas
        ctx.drawImage(video, 0, 0);

        // Convert to data URL
        const dataURL = canvas.toDataURL('image/jpeg', 0.8);
        setPrescriptionImage(dataURL);

        // Stop the stream
        stream.getTracks().forEach(track => track.stop());
      });
    } catch (error) {
      console.error('Error taking picture:', error);
      alert('Failed to take picture. Camera access may not be available.');
    }
  };

  const handleSubmit = async () => {
    // Validate form
    if (!patientName) {
      alert('Please select a patient');
      return;
    }

    if (medications.length === 0) {
      alert('Please add at least one medication');
      return;
    }

    // Check if any medication is missing required fields
    const invalidMedication = medications.find(med => !med.name.trim());
    if (invalidMedication) {
      alert('All medications must have a name');
      return;
    }

    try {
      setLoading(true);

      // Prepare prescription data
      const prescriptionData = {
        doctorId: user?.uid,
        doctorName: `Dr. ${user?.lastName || 'Doctor'}`,
        patientId: patientId,
        patientName,
        medications,
        notes,
        prescriptionImage,
        status: 'pending'
      };

      // Save prescription to Firebase
      const result = await firebasePrescriptionsService.savePrescription(prescriptionData);

      // Check if there was a permission error
      if (result._permissionError) {
        alert('Permission Error: You do not have permission to save prescriptions to Firebase. Please contact your administrator to update Firestore security rules. The prescription has been saved locally.');
        window.history.back();
      } else {
        alert('Success: Prescription saved to Firebase successfully');
        window.history.back();
      }
    } catch (error) {
      console.error('Error creating prescription in Firebase:', error);
      alert('Error: Failed to create prescription. Please check your connection and try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container">
      <div className="header">
        <button
          className="back-button"
          onClick={() => window.history.back()}
        >
          <i className="icon-arrow-back">←</i>
        </button>
        <h1 className="header-title">New Prescription</h1>
        <div style={{ width: '40px' }} />
      </div>

      <div className="content">
        {/* Patient Selection */}
        <div className="section">
          <h2 className="section-title">Patient Information</h2>
          <button
            className="patient-selector"
            onClick={() => setShowPatientSelector(true)}
          >
            {patientName ? (
              <div className="selected-patient">
                <i className="icon-person">👤</i>
                <span className="selected-patient-name">{patientName}</span>
              </div>
            ) : (
              <div className="patient-placeholder">
                <i className="icon-person-add">👤+</i>
                <span className="patient-placeholder-text">Select Patient</span>
              </div>
            )}
          </button>
        </div>

        {/* Medications */}
        <div className="section">
          <h2 className="section-title">Add Medication</h2>

          <div className="form-group">
            <label className="form-label">Medication Name*</label>
            <input
              type="text"
              className="form-input"
              placeholder="e.g., Amoxicillin"
              value={medicationName}
              onChange={(e) => setMedicationName(e.target.value)}
            />
          </div>

          <div className="form-group">
            <label className="form-label">Dosage</label>
            <input
              type="text"
              className="form-input"
              placeholder="e.g., 500mg twice daily"
              value={medicationDosage}
              onChange={(e) => setMedicationDosage(e.target.value)}
            />
          </div>

          <div className="form-group">
            <label className="form-label">Instructions</label>
            <textarea
              className="form-input text-area"
              placeholder="e.g., Take with food"
              value={medicationInstructions}
              onChange={(e) => setMedicationInstructions(e.target.value)}
            />
          </div>

          <button
            className="add-medication-button"
            style={{ backgroundColor: doctorColors.primary }}
            onClick={handleAddMedication}
          >
            <i className="icon-add-circle">➕</i>
            <span className="add-medication-button-text">Add Medication</span>
          </button>

          <div style={{marginTop: '20px'}}>
            <h3 className="medications-list-title">Medications List ({medications.length})</h3>

            {medications.length === 0 ? (
              <div className="empty-medications">
                <i className="icon-medkit">💊</i>
                <span className="empty-medications-text">No medications added</span>
              </div>
            ) : (
              <div>
                {medications.map((medication, index) => (
                  <div key={index} className="medication-item">
                    <div className="medication-info">
                      <div className="medication-name">{medication.name || 'Unnamed medication'}</div>
                      <div className="medication-dosage">{medication.dosage || 'No dosage specified'}</div>
                      {medication.instructions ? (
                        <div className="medication-instructions">{medication.instructions}</div>
                      ) : (
                        <div className="medication-instructions italic">No instructions</div>
                      )}
                    </div>
                    <button
                      className="medication-action-button"
                      onClick={() => handleRemoveMedication(index)}
                    >
                      <i className="icon-trash">🗑️</i>
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Notes */}
        <div className="section">
          <h2 className="section-title">Notes</h2>
          <textarea
            className="notes-input"
            placeholder="Add any additional notes or instructions"
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
          />
        </div>

        {/* Prescription Image */}
        <div className="section">
          <h2 className="section-title">Prescription Image (Optional)</h2>

          {prescriptionImage ? (
            <div className="image-container">
              <img src={prescriptionImage} alt="Prescription" className="prescription-image" />
              <button
                className="remove-image-button"
                onClick={() => setPrescriptionImage(null)}
              >
                <i className="icon-close-circle">❌</i>
              </button>
            </div>
          ) : (
            <div className="image-actions">
              <button
                className="image-action-button camera-button"
                onClick={takePicture}
              >
                <i className="icon-camera">📷</i>
                <span className="image-action-text camera-text">Take Photo</span>
              </button>

              <button
                className="image-action-button upload-button"
                onClick={pickImage}
              >
                <i className="icon-image">🖼️</i>
                <span className="image-action-text upload-text">Upload Image</span>
              </button>
            </div>
          )}
        </div>

        {/* Submit Button */}
        <button
          className="submit-button"
          style={{ backgroundColor: doctorColors.primary }}
          onClick={handleSubmit}
          disabled={loading}
        >
          {loading ? (
            <div className="loading-spinner">⏳</div>
          ) : (
            <>
              <i className="icon-checkmark-circle">✅</i>
              <span className="submit-button-text">Create Prescription</span>
            </>
          )}
        </button>
      </div>

      {/* Doctor Patient Selector Modal */}
      {showPatientSelector && (
        <DoctorPatientSelectorModal
          visible={showPatientSelector}
          onClose={() => setShowPatientSelector(false)}
          onSuccess={handlePatientSelected}
          scannerTitle="Select Your Patient"
        />
      )}
    </div>
  );
};

export default NewPrescriptionScreen;
