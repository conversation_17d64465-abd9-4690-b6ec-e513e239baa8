import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../../../../contexts/AuthContext';
import { firebasePrescriptionsService } from '../../../../services/firebasePrescriptionsService';
import { PATIENT_COLORS } from '../../../../config/theme';
import '../../../../styles/dashboards/patient/prescriptions/patientPrescriptions.css';

const PatientPrescriptions = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [prescriptions, setPrescriptions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedPrescription, setSelectedPrescription] = useState(null);
  const [detailModalVisible, setDetailModalVisible] = useState(false);

  useEffect(() => {
    loadPrescriptions();

    // No direct equivalent for navigation.addListener in React Router
    // We'll rely on the initial load and any manual refreshes
    return () => {
      // Cleanup if needed
    };
  }, [user]);

  const loadPrescriptions = async () => {
    if (!user) return;

    setLoading(true);
    try {
      const patientId = user.uid;
      // Retrieve only prescriptions with "sent" status
      const data = await firebasePrescriptionsService.getPatientPrescriptions(patientId, 'sent');

      // Check if there was a permission error
      if (data._permissionError) {
        alert('Permission Error: You do not have permission to access prescriptions. Please contact your healthcare provider.');
      }

      setPrescriptions(data);
    } catch (error) {
      console.error('Error loading prescriptions from Firebase:', error);
      alert('Error: Failed to load prescriptions. Please check your connection and try again.');
    } finally {
      setLoading(false);
    }
  };

  const handlePrescriptionPress = (prescription) => {
    setSelectedPrescription(prescription);
    setDetailModalVisible(true);
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'Unknown date';

    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const renderPrescriptionItem = (item) => (
    <div 
      className="prescriptionItem"
      onClick={() => handlePrescriptionPress(item)}
      key={item.id}
    >
      <div className="prescriptionHeader">
        <div className="prescriptionIcon" style={{ backgroundColor: PATIENT_COLORS.secondary }}>
          <Ionicons name="document-text" size={24} color={PATIENT_COLORS.primary} />
        </div>
        <div className="prescriptionInfo">
          <div className="prescriptionTitle">Dr. {item.doctorName}</div>
          <div className="prescriptionMedication">
            {item.medications.length > 0
              ? `${item.medications.length} medication${item.medications.length > 1 ? 's' : ''}: ${item.medications.map(med => med.name).join(', ')}`
              : 'No medications'
            }
          </div>
          <div className="prescriptionDate">{formatDate(item.createdAt)}</div>
        </div>
      </div>
    </div>
  );

  const renderDetailModal = () => {
    if (!selectedPrescription || !detailModalVisible) return null;

    return (
      <div className="modalOverlay">
        <div className="modalContent">
          <div className="modalHeader">
            <div className="modalTitle">Prescription Details</div>
            <div onClick={() => setDetailModalVisible(false)} style={{ cursor: 'pointer' }}>
              <Ionicons name="close" size={24} color="#333" />
            </div>
          </div>

          <div className="modalScrollView">
            <div className="detailSection">
              <div className="detailLabel">Doctor:</div>
              <div className="detailValue">Dr. {selectedPrescription.doctorName}</div>
            </div>

            <div className="detailSection">
              <div className="detailLabel">Date:</div>
              <div className="detailValue">{formatDate(selectedPrescription.createdAt)}</div>
            </div>

            <div className="medicationsSection">
              <div className="sectionTitle">Medications</div>
              {selectedPrescription.medications.map((med, index) => (
                <div key={index} className="medicationItem">
                  <div className="medicationName">{med.name || 'Unnamed medication'}</div>

                  {med.dosage ? (
                    <div className="medicationDetailRow">
                      <div className="medicationDetailLabel">Dosage:</div>
                      <div className="medicationDetailValue">{med.dosage}</div>
                    </div>
                  ) : null}

                  {med.instructions ? (
                    <div className="medicationDetailRow">
                      <div className="medicationDetailLabel">Instructions:</div>
                      <div className="medicationDetailValue">{med.instructions}</div>
                    </div>
                  ) : null}
                </div>
              ))}
            </div>

            {selectedPrescription.notes && (
              <div className="notesSection">
                <div className="sectionTitle">Notes</div>
                <div className="notesText">{selectedPrescription.notes}</div>
              </div>
            )}

            {selectedPrescription.prescriptionImage && (
              <div className="imageSection">
                <div className="sectionTitle">Prescription Image</div>
                <img
                  src={selectedPrescription.prescriptionImage}
                  className="prescriptionImage"
                  alt="Prescription"
                />
              </div>
            )}
          </div>

          <div className="modalFooter">
            <div
              className="closeButton"
              onClick={() => setDetailModalVisible(false)}
              style={{ backgroundColor: PATIENT_COLORS.primary }}
            >
              <span className="closeButtonText">Close</span>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="container">
      <div className="header">
        <div
          className="backButton"
          onClick={() => navigate(-1)}
        >
          <Ionicons name="arrow-back" size={24} color="#333" />
        </div>
        <div className="headerTitle">My Prescriptions</div>
        <div style={{ width: '40px' }} />
      </div>

      {loading ? (
        <div className="loadingContainer">
          <div className="spinner" style={{ color: PATIENT_COLORS.primary }}>Loading...</div>
        </div>
      ) : (
        <div className="content">
          {prescriptions.length === 0 ? (
            <div className="emptyContainer">
              <Ionicons name="document-text-outline" size={64} color="#BDBDBD" />
              <div className="emptyText">No prescriptions yet</div>
              <div className="emptySubText">
                Prescriptions sent by your doctors will appear here
              </div>
            </div>
          ) : (
            <div className="listContainer">
              {prescriptions.map(item => renderPrescriptionItem(item))}
            </div>
          )}
        </div>
      )}

      {renderDetailModal()}
    </div>
  );
};

export default PatientPrescriptions;
