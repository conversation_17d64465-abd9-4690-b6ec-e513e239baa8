.container {
  flex: 1;
  background-color: #f8f9fa;
  overflow-y: auto;
}

.sectionHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #fff;
  padding: 12px 16px;
  border-bottom: 1px solid #eaeaea;
}

.sectionHeaderLeft {
  display: flex;
  align-items: center;
}

.sectionTitle {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-left: 8px;
}

.sectionContent {
  background-color: #fff;
  padding: 16px;
  border-bottom: 1px solid #eaeaea;
}

.fieldContainer {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.fieldIcon {
  margin-right: 6px;
}

.fieldLabel {
  font-size: 14px;
  font-weight: 600;
  color: #666;
  width: 120px;
  margin-right: 8px;
}

.fieldValue {
  font-size: 14px;
  color: #333;
  flex: 1;
}

.emptyText {
  font-size: 14px;
  color: #999;
  font-style: italic;
  margin: 8px 0;
}

.subSectionTitle {
  font-size: 15px;
  font-weight: 600;
  color: #555;
  margin-top: 12px;
  margin-bottom: 8px;
}

.conditionsContainer {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 12px;
}

.conditionTag {
  background-color: #E3F2FD;
  border-radius: 16px;
  padding: 4px 10px;
  margin: 4px;
}

.conditionText {
  font-size: 12px;
  color: #1976D2;
}

.allergiesContainer {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 12px;
}

.allergyTag {
  background-color: #FFEBEE;
  border-radius: 16px;
  padding: 4px 10px;
  margin: 4px;
}

.allergyText {
  font-size: 12px;
  color: #D32F2F;
}

.contactCard {
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 8px;
}

.contactName {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.contactRelationship {
  font-size: 13px;
  color: #666;
  margin-top: 2px;
}

.contactPhone {
  font-size: 13px;
  color: #666;
  margin-top: 2px;
}
