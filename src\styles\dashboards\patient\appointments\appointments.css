

.container {
  flex: 1;
  background-color: #f5f5f5;
}

.header {
  padding: 16px;
  padding-top: 16px;
  padding-bottom: 8px;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.action-buttons-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px 16px 16px;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 8px;
}

.header-title {
  font-size: 20px;
  font-weight: bold;
  color: #333;
}

.header-buttons {
  display: flex;
  align-items: center;
}

.filter-button {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background-color: #f5f5f5;
  border-radius: 20px;
  border: 1px solid #e0e0e0;
}

.filter-button-text {
  color: #3f51b5;
  font-weight: bold;
  margin-left: 6px;
  font-size: 14px;
}

.request-button {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  background-color: #3f51b5;
  border-radius: 20px;
  border: none;
  cursor: pointer;
}

.request-button-text {
  color: #fff;
  font-weight: bold;
  margin-left: 6px;
  font-size: 14px;
}

.loading-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.loading-text {
  margin-top: 12px;
  font-size: 16px;
  color: #666;
}

.empty-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 0 24px;
  height: 100%;
}

.empty-text {
  font-size: 18px;
  font-weight: bold;
  color: #666;
  margin-top: 16px;
}

.empty-sub-text {
  font-size: 14px;
  color: #888;
  text-align: center;
  margin-top: 8px;
}

.list-container {
  padding: 16px;
}

.card {
  margin: 12px;
  border-radius: 16px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  background-color: transparent;
  margin-bottom: 16px;
  overflow: hidden;
}

.card-gradient {
  border-radius: 16px;
  overflow: hidden;
  background: linear-gradient(to right, #f5f7fa, #ffffff);
}

.card-content {
  padding: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.date-container {
  flex: 1;
}

.date-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.status-container {
  display: flex;
  align-items: center;
  margin-top: 4px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 4px;
  margin-right: 6px;
}

.status-text {
  font-size: 12px;
  font-weight: bold;
}

.avatar-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  border-radius: 20px;
}

.card-divider {
  height: 1px;
  background-color: #eee;
  margin: 8px 0;
}

.card-details {
  margin-bottom: 12px;
}

.detail-row {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.detail-icon {
  margin-right: 8px;
}

.detail-text {
  font-size: 14px;
  color: #555;
}

.card-footer {
  margin-top: 8px;
}

.action-buttons-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.action-button {
  display: flex;
  align-items: center;
  padding: 6px 12px;
  border-radius: 16px;
  margin-right: 8px;
}

.reschedule-button {
  background-color: #fff8e1;
  border: 1px solid #ffecb3;
}

.reschedule-button-text {
  color: #ff9800;
  font-size: 12px;
  font-weight: bold;
}

.cancel-button {
  background-color: #ffebee;
  border: 1px solid #ffcdd2;
}

.cancel-button-text {
  color: #f44336;
  font-size: 12px;
  font-weight: bold;
}

.button-icon {
  margin-right: 4px;
}

.view-details-button {
  display: flex;
  align-items: center;
  margin-left: auto;
}

.view-details-text {
  color: #4CAF50;
  font-size: 12px;
  font-weight: bold;
  margin-right: 4px;
}

.rescheduled-banner {
  display: flex;
  align-items: center;
  background-color: #FFF8E1;
  padding: 6px 10px;
  border-radius: 4px;
  margin-top: 8px;
  margin-bottom: 4px;
  align-self: flex-start;
  border-left: 3px solid #FF9800;
}

.rescheduled-text {
  font-size: 12px;
  color: #FF9800;
  font-weight: bold;
  margin-left: 4px;
}

/* Modal styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  width: 90%;
  max-width: 500px;
  max-height: 80vh;
  background-color: white;
  border-radius: 16px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #eee;
}

.modal-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.modal-body {
  padding: 16px;
  overflow-y: auto;
}

.input-label {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
  margin-top: 12px;
}

.doctor-selection {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.doctor-option {
  width: 48%;
  padding: 12px;
  margin-bottom: 12px;
  background-color: #f5f5f5;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  cursor: pointer;
}

.selected-doctor-option {
  background-color: #e8f5e9;
  border-color: #a5d6a7;
}

.doctor-option-text {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.doctor-specialty {
  font-size: 14px;
  color: #666;
}

.selected-doctor-text {
  color: #2e7d32;
}

.date-input {
  display: flex;
  align-items: center;
  padding: 12px;
  background-color: #f5f5f5;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  margin-bottom: 16px;
  cursor: pointer;
}

.date-text {
  font-size: 16px;
  color: #333;
  margin-left: 8px;
}

.reason-input {
  width: 100%;
  padding: 12px;
  background-color: #f5f5f5;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  margin-bottom: 16px;
  min-height: 100px;
  resize: vertical;
  font-family: inherit;
  font-size: 16px;
}

.availability-section {
  margin-bottom: 16px;
}

.check-availability-button {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 12px;
  background-color: #3f51b5;
  border-radius: 8px;
  border: none;
  color: white;
  font-weight: bold;
  cursor: pointer;
  margin-bottom: 12px;
}

.checking-button {
  background-color: #7986cb;
}

.checking-container {
  display: flex;
  align-items: center;
}

.check-availability-text {
  color: white;
  font-weight: bold;
  margin-left: 8px;
}

.availability-result {
  display: flex;
  align-items: center;
  margin-top: 8px;
}

.availability-text {
  margin-left: 8px;
  font-size: 14px;
}

.submit-button {
  width: 100%;
  padding: 16px;
  background-color: #4CAF50;
  border-radius: 8px;
  border: none;
  color: white;
  font-weight: bold;
  font-size: 16px;
  cursor: pointer;
  margin-top: 16px;
}

.disabled-button {
  background-color: #e0e0e0;
  color: #9e9e9e;
  cursor: not-allowed;
}

/* Date picker modal styles */
.date-picker-modal {
  width: 90%;
  max-width: 500px;
  background-color: white;
  border-radius: 16px;
  overflow: hidden;
}

.date-picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #eee;
}

.date-picker-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.date-scroll-view {
  max-height: 300px;
  overflow-x: auto;
}

.date-options-container {
  display: flex;
  padding: 16px;
}

.date-option {
  width: 70px;
  height: 100px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-right: 12px;
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 8px;
  border: none;
  cursor: pointer;
}

.selected-date-option {
  background-color: #3f51b5;
}

.date-option-day {
  font-size: 13px;
  color: #666;
  margin-bottom: 4px;
}

.date-option-date {
  font-size: 20px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.date-option-month {
  font-size: 13px;
  color: #666;
}

.selected-date-text {
  color: white;
}

.date-picker-actions {
  display: flex;
  justify-content: flex-end;
  padding: 16px;
  border-top: 1px solid #eee;
}

.cancel-date-button {
  padding: 8px 16px;
  margin-right: 12px;
  background: none;
  border: none;
  cursor: pointer;
}

.cancel-date-text {
  color: #666;
  font-weight: bold;
}

.confirm-date-button {
  background-color: #3f51b5;
  padding: 8px 16px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
}

.confirm-date-text {
  color: white;
  font-weight: bold;
}

.time-selection-container {
  padding: 16px;
  border-top: 1px solid #eee;
}

.time-selection-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 12px;
}

.time-scroll-view {
  overflow-x: auto;
}

.time-options-container {
  display: flex;
  padding: 8px 0;
}

.time-option {
  width: 80px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 10px;
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 8px;
  border: 1px solid #e0e0e0;
  cursor: pointer;
}

.selected-time-option {
  background-color: #3f51b5;
  border-color: #3f51b5;
}

.time-option-text {
  font-size: 14px;
  color: #333;
}

.selected-time-text {
  color: white;
  font-weight: bold;
}

/* Spinner for loading */
.spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border-left-color: #3f51b5;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Appointment guide styles */
.appointment-guide {
  background-color: #fff;
  padding: 16px;
  margin-bottom: 8px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin: 0 16px 16px 16px;
}

.guide-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 12px;
}

.guide-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8px;
  padding: 4px 0;
}

.guide-text {
  font-size: 14px;
  color: #555;
  margin-left: 8px;
  flex: 1;
}

.guide-highlight {
  font-weight: bold;
  color: #3f51b5;
}

/* Appointment details modal styles */
.details-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #eee;
}

.status-badge {
  padding: 4px 12px;
  border-radius: 12px;
  margin-bottom: 8px;
}

.details-title {
  font-size: 22px;
  font-weight: bold;
  color: #333;
  margin-top: 8px;
  text-align: center;
}

.details-subtitle {
  font-size: 16px;
  color: #666;
  margin-top: 4px;
  text-align: center;
}

.details-section {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #eee;
}

.details-section-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 16px;
}

.details-row {
  display: flex;
  margin-bottom: 16px;
}

.details-icon-container {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  background-color: #E8EAF6;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 16px;
}

.details-content {
  flex: 1;
}

.details-label {
  display: block;
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.details-value {
  display: block;
  font-size: 16px;
  font-weight: bold;
  color: #333;
}
