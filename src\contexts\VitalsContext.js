import React, { createContext, useState, useContext, useEffect } from 'react';
import { collection, addDoc, query, where, getDocs, doc, deleteDoc, serverTimestamp } from 'firebase/firestore';
import { db as firestore } from '../config/firebase';
import { useAuth } from './AuthContext';

// Create the context
export const VitalsContext = createContext();

export const VitalsProvider = ({ children }) => {
  const [vitals, setVitals] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const { user } = useAuth();

  // Fetch vitals for the current user
  const fetchVitals = async (vitalType = null) => {
    if (!user) return [];

    setLoading(true);
    setError(null);

    try {
      // Use a simple approach to avoid index issues
      const vitalsRef = collection(firestore, 'vitals');
      let q;

      if (vitalType) {
        // Simple query without orderBy to avoid index issues
        q = query(
          vitalsRef,
          where('userId', '==', user.uid),
          where('vitalType', '==', vitalType)
        );
      } else {
        // Simple query without orderBy to avoid index issues
        q = query(
          vitalsRef,
          where('userId', '==', user.uid)
        );
      }

      const querySnapshot = await getDocs(q);
      const vitalsList = [];

      querySnapshot.forEach((doc) => {
        const data = doc.data();
        vitalsList.push({
          id: doc.id,
          ...data,
          timestamp: data.timestamp?.toDate?.().toISOString() || new Date().toISOString()
        });
      });

      // Sort by timestamp (newest first) in memory
      vitalsList.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

      setVitals(vitalsList);
      return vitalsList;
    } catch (error) {
      console.error('Error fetching vitals:', error);
      setError('Failed to fetch vitals data');
      return [];
    } finally {
      setLoading(false);
    }
  };

  // Save a new vital record
  const saveVital = async (vitalData) => {
    if (!user) {
      setError('User not authenticated. Please log in.');
      return null;
    }

    setLoading(true);
    setError(null);

    try {
      // Prepare data - s'assurer que la structure est correcte
      const data = {
        userId: user.uid,
        vitalType: vitalData.vitalType,
        values: vitalData.values,
        notes: vitalData.notes || '',
        recordMethod: vitalData.recordMethod || 'manual',
        recordType: vitalData.recordType || 'self',
        timestamp: new Date().toISOString()
      };

      // Save directly to Firestore for reliability
      const docRef = await addDoc(collection(firestore, 'vitals'), {
        ...data,
        timestamp: serverTimestamp()
      });

      const newVital = {
        id: docRef.id,
        ...data
      };

      // Update local state with the new vital
      setVitals(prevVitals => [newVital, ...prevVitals]);

      return newVital;
    } catch (error) {
      console.error('Error saving vital to Firebase:', error);
      setError('Failed to save vital data to Firebase');
      return null;
    } finally {
      setLoading(false);
    }
  };

  // Delete a vital record
  const deleteVital = async (vitalId) => {
    if (!user) return false;

    setLoading(true);
    setError(null);

    try {
      // Delete directly from Firestore
      await deleteDoc(doc(firestore, 'vitals', vitalId));

      // Update local state
      setVitals(prevVitals => prevVitals.filter(vital => vital.id !== vitalId));

      return true;
    } catch (error) {
      console.error('Error deleting vital:', error);
      setError('Failed to delete vital record');
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Get latest vitals for each type
  const getLatestVitals = async () => {
    if (!user) return {};

    setLoading(true);
    setError(null);

    try {
      // Use direct Firestore access for reliability
      const vitalTypes = ['heartRate', 'bloodPressure', 'bloodGlucose', 'weight'];
      const latestVitals = {};

      for (const type of vitalTypes) {
        // Query without orderBy to avoid index requirements
        const q = query(
          collection(firestore, 'vitals'),
          where('userId', '==', user.uid),
          where('vitalType', '==', type)
        );

        const querySnapshot = await getDocs(q);

        if (!querySnapshot.empty) {
          // Sort the results in memory instead of using orderBy in the query
          const sortedDocs = querySnapshot.docs.sort((a, b) => {
            const timestampA = a.data().timestamp?.toDate?.() || new Date(0);
            const timestampB = b.data().timestamp?.toDate?.() || new Date(0);
            return timestampB - timestampA; // Sort descending (newest first)
          });

          const doc = sortedDocs[0]; // Get the newest record
          latestVitals[type] = {
            id: doc.id,
            ...doc.data(),
            timestamp: doc.data().timestamp?.toDate?.().toISOString() || new Date().toISOString()
          };
        }
      }

      return latestVitals;
    } catch (error) {
      console.error('Error fetching latest vitals:', error);
      setError('Failed to fetch latest vitals data');
      return {};
    } finally {
      setLoading(false);
    }
  };

  // Get all vitals grouped by type
  const getAllVitals = async () => {
    if (!user) return {};

    setLoading(true);
    setError(null);

    try {
      // Use direct Firestore access for reliability
      const vitalTypes = ['heartRate', 'bloodPressure', 'bloodGlucose', 'weight'];
      const allVitals = {
        heartRate: [],
        bloodPressure: [],
        bloodGlucose: [],
        weight: []
      };

      for (const type of vitalTypes) {
        // Query without orderBy to avoid index requirements
        const q = query(
          collection(firestore, 'vitals'),
          where('userId', '==', user.uid),
          where('vitalType', '==', type)
        );

        const querySnapshot = await getDocs(q);

        if (!querySnapshot.empty) {
          querySnapshot.forEach(doc => {
            allVitals[type].push({
              id: doc.id,
              ...doc.data(),
              timestamp: doc.data().timestamp?.toDate?.().toISOString() || new Date().toISOString()
            });
          });

          // Sort each type's array by timestamp (newest first)
          allVitals[type].sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
        }
      }

      return allVitals;
    } catch (error) {
      console.error('Error fetching all vitals:', error);
      setError('Failed to fetch all vitals data');
      return {
        heartRate: [],
        bloodPressure: [],
        bloodGlucose: [],
        weight: []
      };
    } finally {
      setLoading(false);
    }
  };

  // Load vitals when user changes
  useEffect(() => {
    if (user) {
      fetchVitals();
    } else {
      setVitals([]);
    }
  }, [user]);

  // Context value
  const value = {
    vitals,
    loading,
    error,
    fetchVitals,
    saveVital,
    deleteVital,
    getLatestVitals,
    getAllVitals
  };

  return (
    <VitalsContext.Provider value={value}>
      {children}
    </VitalsContext.Provider>
  );
};

// Custom hook to use the vitals context
export const useVitals = () => {
  const context = useContext(VitalsContext);
  if (context === undefined) {
    throw new Error('useVitals must be used within a VitalsProvider');
  }
  return context;
};
