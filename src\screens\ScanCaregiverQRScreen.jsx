import React, { useState, useEffect } from 'react';
import '../styles/screens/scanCaregiverQRScreen.css';
import { usersAPI } from '../config/api';
import { useAuth } from '../contexts/AuthContext';
import { ROLE_COLORS } from '../config/theme';
import { firebaseCaregiverService } from '../services/firebaseCaregiverService';

const ScanCaregiverQRScreen = () => {
  // State variables
  const [loading, setLoading] = useState(false);
  const [caregiverCode, setCaregiverCode] = useState('');
  const [hasPermission, setHasPermission] = useState(null);
  const [scanned, setScanned] = useState(false);
  const [showMessage, setShowMessage] = useState(null);

  // Navigation and context
  const { user } = useAuth();
  const supervisorColors = ROLE_COLORS.supervisor;

  // Get patient ID from URL params or props
  const urlParams = new URLSearchParams(window.location.search);
  const patientId = urlParams.get('patientId');
  const mode = urlParams.get('mode') || 'manual';
  const returnScreen = urlParams.get('returnScreen');
  const showCamera = mode === 'camera';

  // Show message function
  const displayMessage = (message, type = 'info') => {
    setShowMessage({ ...message, type });
    setTimeout(() => setShowMessage(null), 4000);
  };

  // Navigation functions
  const goBack = () => {
    window.history.back();
  };

  const navigate = (screen, params = {}) => {
    const searchParams = new URLSearchParams(params);
    window.location.href = `/${screen}?${searchParams.toString()}`;
  };

  // Validate code format
  const isValidCode = (code) => {
    return /^[A-Z0-9]{8}$/.test(code);
  };

  // Handle code input change
  const handleCodeChange = (e) => {
    // Convert to uppercase and remove non-alphanumeric characters
    const formattedCode = e.target.value.toUpperCase().replace(/[^A-Z0-9]/g, '');
    setCaregiverCode(formattedCode);
  };

  // Manual code submission
  const handleSubmit = () => {
    if (!isValidCode(caregiverCode)) {
      displayMessage({
        message: 'Invalid Code Format',
        description: 'Please enter a valid 8-character code (letters and numbers only)',
      }, 'danger');
      return;
    }

    processUserCode(caregiverCode);
  };

  // Request camera permissions when component mounts
  useEffect(() => {
    if (showCamera) {
      const getBarCodeScannerPermissions = async () => {
        try {
          const stream = await navigator.mediaDevices.getUserMedia({ video: true });
          setHasPermission(true);
          stream.getTracks().forEach(track => track.stop()); // Stop the stream immediately
        } catch (error) {
          setHasPermission(false);
          displayMessage({
            message: 'Camera Permission Denied',
            description: 'Please grant camera permissions to scan QR codes',
          }, 'warning');
        }
      };

      getBarCodeScannerPermissions();
    }
  }, [showCamera]);

  // Handle taking a picture
  const takePicture = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ video: true });
      
      displayMessage({
        message: 'QR Code Captured',
        description: 'Processing the image...',
      }, 'info');

      setScanned(true);

      // Simulate finding a valid code after a delay
      setTimeout(() => {
        // Generate a random 8-character code
        const randomCode = Math.random().toString(36).substring(2, 10).toUpperCase();
        processUserCode(randomCode);
      }, 2000);

      // Stop the stream
      stream.getTracks().forEach(track => track.stop());
    } catch (error) {
      console.error('Error taking picture:', error);
      displayMessage({
        message: 'Error',
        description: 'Failed to capture image',
      }, 'danger');
    }
  };

  // Process a user code (from QR or manual entry)
  const processUserCode = async (code) => {
    if (!patientId) {
      displayMessage({
        message: 'Error',
        description: 'Patient ID is missing. Please try again.',
      }, 'danger');
      return;
    }

    setLoading(true);

    try {
      console.log('Processing code:', code);

      // Verify the code belongs to a caregiver
      const userResponse = await usersAPI.getUserByCode(code);
      console.log('User found:', userResponse);

      if (!userResponse || userResponse.role !== 'caregiver') {
        displayMessage({
          message: 'Invalid User',
          description: 'This code does not belong to a caregiver',
        }, 'danger');
        setLoading(false);
        setScanned(false);
        return;
      }

      try {
        // Assign caregiver to patient
        const result = await firebaseCaregiverService.assignCaregiverByCode(patientId, code);

        displayMessage({
          message: 'Success',
          description: `Caregiver ${userResponse.displayName} assigned successfully`,
        }, 'success');

        // Go back to the appropriate screen
        setTimeout(() => {
          if (returnScreen === 'SupervisorCaregiverManagement') {
            navigate('SupervisorCaregiverManagement');
          } else {
            goBack();
          }
        }, 1500);
      } catch (assignError) {
        console.error('Error assigning caregiver:', assignError);

        let errorMessage = 'Failed to assign caregiver. Please try again.';

        // Check for specific error conditions
        if (assignError.response?.status === 400) {
          errorMessage = assignError.response.data.error || 'Invalid request. Please check your inputs.';
        } else if (assignError.response?.status === 403) {
          errorMessage = assignError.response.data.error || 'You do not have permission to assign caregivers to this patient.';
        } else if (assignError.response?.status === 404) {
          if (assignError.response.data.error === 'No caregiver found with this code') {
            errorMessage = 'No caregiver found with this code.';
          } else if (assignError.response.data.error === 'Patient not found') {
            errorMessage = 'Patient not found.';
          } else {
            errorMessage = assignError.response.data.error || 'Resource not found.';
          }
        } else if (assignError.response?.data?.error) {
          errorMessage = assignError.response.data.error;
        } else if (assignError.message) {
          errorMessage = assignError.message;
        }

        displayMessage({
          message: 'Error',
          description: errorMessage,
        }, 'danger');
      }
    } catch (error) {
      console.error('Error processing code:', error);
      let errorMessage = 'Failed to find caregiver. Please try again.';

      if (error?.response?.status === 404) {
        errorMessage = 'Invalid code. No caregiver found with this code.';
      } else if (error?.response?.data?.error) {
        errorMessage = error.response.data.error;
      }

      displayMessage({
        message: 'Error',
        description: errorMessage,
      }, 'danger');
    } finally {
      setLoading(false);
      setScanned(false);
    }
  };

  // Message component
  const MessageComponent = ({ message, type }) => {
    if (!message) return null;
    
    return (
      <div className={`message message-${type}`}>
        <div className="message-title">{message.message}</div>
        <div className="message-description">{message.description}</div>
      </div>
    );
  };

  // Camera mode
  if (showCamera) {
    // If we don't have permission yet, show a loading screen
    if (hasPermission === null) {
      return (
        <div className="container">
          <div className="loading-container">
            <div className="spinner"></div>
            <div className="loading-text">Requesting camera permission...</div>
          </div>
          {showMessage && <MessageComponent message={showMessage} type={showMessage.type} />}
        </div>
      );
    }

    // If permission was denied, show an error message
    if (hasPermission === false) {
      return (
        <div className="container">
          <div className="error-container">
            <div className="camera-off-icon">📷</div>
            <div className="error-title">Camera Access Denied</div>
            <div className="error-text">
              Please enable camera access in your device settings to scan QR codes.
            </div>
            <button
              className="button button-primary"
              onClick={goBack}
            >
              Go Back
            </button>
          </div>
          {showMessage && <MessageComponent message={showMessage} type={showMessage.type} />}
        </div>
      );
    }

    // If we have permission, show the camera interface
    return (
      <div className="container">
        <div className="camera-container">
          <div className="scan-area">
            {scanned && !loading && (
              <div className="scanning-animation">
                <div className="spinner"></div>
                <div className="scanning-text">Processing...</div>
              </div>
            )}
          </div>

          <button
            className="capture-button"
            onClick={takePicture}
            disabled={scanned}
          >
            📷
          </button>
        </div>

        {/* Informational note for users */}
        <div className="info-box">
          <div className="info-text">
            Position the caregiver's QR code within the frame to scan.
          </div>
        </div>

        <div className="footer">
          <div className="button-row">
            <button
              className="button button-secondary"
              onClick={goBack}
            >
              Cancel
            </button>

            {scanned && !loading && (
              <button
                className="button button-primary"
                onClick={() => setScanned(false)}
              >
                Scan Again
              </button>
            )}

            <button
              className="button button-secondary"
              onClick={() => navigate('ScanCaregiverQRScreen', { mode: 'manual', patientId })}
            >
              Enter Manually
            </button>
          </div>
        </div>
        {showMessage && <MessageComponent message={showMessage} type={showMessage.type} />}
      </div>
    );
  }

  // Manual entry mode
  return (
    <div className="container">
      <div className="form-container">
        <div className="qr-icon">🔲</div>

        <h1 className="title">Add Caregiver by Code</h1>
        <p className="subtitle">
          Enter the 8-character code from caregiver's profile
        </p>

        <input
          className="input"
          value={caregiverCode}
          onChange={handleCodeChange}
          placeholder="Enter code (e.g. ABCD1234)"
          maxLength={8}
          type="text"
        />

        {loading && (
          <div className="loader">
            <div className="spinner"></div>
          </div>
        )}

        <button
          className={`button ${isValidCode(caregiverCode) && !loading ? 'button-primary' : 'button-disabled'}`}
          onClick={handleSubmit}
          disabled={!isValidCode(caregiverCode) || loading}
        >
          {isValidCode(caregiverCode) ? 'Assign Caregiver' : 'Enter Valid Code'}
        </button>

        <button
          className="cancel-button"
          onClick={goBack}
          disabled={loading}
        >
          Cancel
        </button>
      </div>
      {showMessage && <MessageComponent message={showMessage} type={showMessage.type} />}
    </div>
  );
};

export default ScanCaregiverQRScreen;
