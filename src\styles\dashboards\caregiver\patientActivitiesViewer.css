/* Patient Activities Viewer Styles */
.container {
  flex: 1;
  background-color: #f5f7fa;
}

.filterContainer {
  padding: 12px 16px;
  background-color: #fff;
  border-bottom: 1px solid #e0e0e0;
}

.filterScrollView {
  display: flex;
  flex-direction: row;
  overflow-x: auto;
}

.filterButton {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 6px 12px;
  border-radius: 20px;
  background-color: #f0f0f0;
  margin-right: 8px;
  cursor: pointer;
  border: none;
}

.filterButtonActive {
  background-color: var(--primary-color);
}

.filterButtonText {
  font-size: 12px;
  font-weight: 500;
  color: #757575;
  margin-left: 4px;
}

.filterButtonTextActive {
  color: #fff;
}

.headerContainer {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #fff;
  border-bottom: 1px solid #e0e0e0;
}

.headerTitle {
  font-size: 16px;
  font-weight: bold;
  color: #212121;
}

.addActivityButton {
  padding: 4px;
  background: none;
  border: none;
  cursor: pointer;
}

.loadingContainer {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 24px;
}

.loadingText {
  margin-top: 12px;
  font-size: 16px;
  color: #757575;
}

.list {
  padding: 16px;
}

.emptyList {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 24px;
}

.activityCard {
  margin-bottom: 12px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  background-color: #fff;
}

.activityHeader {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.activityTypeIcon {
  width: 36px;
  height: 36px;
  border-radius: 18px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 12px;
}

.activityHeaderContent {
  flex: 1;
}

.activityType {
  font-size: 16px;
  font-weight: 500;
  color: #212121;
}

.activityTimeContainer {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-top: 4px;
}

.activityTimeIcon {
  margin-right: 4px;
}

.activityTime {
  font-size: 12px;
  color: #757575;
}

.activityStatus {
  padding: 4px 8px;
  border-radius: 4px;
}

.activityStatusText {
  font-size: 10px;
  font-weight: bold;
  color: #fff;
}

.activityContent {
  padding: 12px;
}

.activityDescription {
  font-size: 14px;
  color: #424242;
  margin-bottom: 8px;
}

.activityDuration {
  font-size: 12px;
  color: #757575;
  margin-bottom: 8px;
}

.activityNotes {
  background-color: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
}

.activityNotesLabel {
  font-size: 12px;
  font-weight: 500;
  color: #616161;
  margin-bottom: 4px;
}

.activityNotesText {
  font-size: 12px;
  color: #616161;
}

.emptyContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px;
}

.emptyText {
  font-size: 18px;
  font-weight: bold;
  color: #757575;
  margin-top: 16px;
}

.emptySubtext {
  font-size: 14px;
  color: #9e9e9e;
  text-align: center;
  margin-top: 8px;
  margin-bottom: 24px;
}

.addButton {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 10px 16px;
  border-radius: 8px;
  border: none;
  cursor: pointer;
}

.addButtonText {
  color: #fff;
  font-weight: bold;
  margin-left: 8px;
}

/* Spinner for loading indicator */
.spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border-left-color: var(--primary-color);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
