.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  z-index: 1000;
}

.modalContent {
  width: 100%;
  max-width: 500px;
  background-color: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0px 2px 10px rgba(0, 0, 0, 0.25);
}

.header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #eee;
}

.title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.closeButton {
  padding: 4px;
  cursor: pointer;
}

.optionsContainer {
  padding: 16px;
}

.option {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 16px 0;
  cursor: pointer;
}

.iconContainer {
  width: 50px;
  height: 50px;
  border-radius: 25px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 16px;
}

.blueIconContainer {
  background-color: #E3F2FD;
}

.greenIconContainer {
  background-color: #E8F5E9;
}

.optionTextContainer {
  flex: 1;
}

.optionTitle {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.optionDescription {
  font-size: 14px;
  color: #666;
}

.divider {
  height: 1px;
  background-color: #eee;
  margin: 8px 0;
}

.chevronIcon {
  color: #ccc;
}
