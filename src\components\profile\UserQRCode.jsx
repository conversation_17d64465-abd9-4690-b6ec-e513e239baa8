import React, { useState, useEffect } from 'react';
import { useAuth } from '../../../contexts/AuthContext';
import { Card, Button, Avatar } from '@mui/material';
import { QRCodeSVG } from 'qrcode.react';
import { 
  MedicalServices, 
  Qr<PERSON>odeScanner, 
  LockOutlined, 
  QrCode, 
  Share as ShareIcon 
} from '@mui/icons-material';
import '../../styles/profile/userQRCode.css';

const UserQRCode = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [qrError, setQrError] = useState(false);

  useEffect(() => {
    // Validate if user code exists and has correct format
    if (!user?.userCode || !/^[A-Z0-9]{8}$/.test(user.userCode)) {
      setQrError(true);
    } else {
      setQrError(false);
    }
  }, [user]);

  if (qrError) {
    return (
      <div className="container">
        <div className="card">
          <div className="cardContent">
            <div className="errorIcon">
              <QrCode />
            </div>
            <h2 className="errorText">Code Not Available</h2>
            <p className="errorSubtext">
              Your unique code hasn't been generated yet or is invalid. Please contact support.
            </p>
            <Button 
              variant="contained" 
              style={{ backgroundColor: 'rgba(16, 107, 0, 1)', marginTop: 20 }}
              onClick={() => alert('Please contact our support team for assistance with your user code.')}
            >
              Contact Support
            </Button>
          </div>
        </div>
      </div>
    );
  }

  const handleShareCode = async () => {
    try {
      setLoading(true);
      if (navigator.share) {
        await navigator.share({
          title: 'My NeuroCare Code',
          text: `My NeuroCare code: ${user.userCode}`,
        });
      } else {
        // Fallback for browsers that don't support Web Share API
        await navigator.clipboard.writeText(`My NeuroCare code: ${user.userCode}`);
        alert('Code copied to clipboard!');
      }
    } catch (error) {
      console.error('Error sharing code:', error);
      alert('Could not share your code. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container">
      <div className="contentContainer">
        <div className="card">
          <div className="cardContent">
            <div className="qrContainer">
              {user?.userCode ? (
                <QRCodeSVG
                  value={user.userCode}
                  className="qrCodeSize"
                  fgColor="rgba(16, 107, 0, 1)"
                  bgColor="white"
                  level="M"
                  onError={(e) => {
                    console.error('QR Code Error:', e);
                    setQrError(true);
                  }}
                />
              ) : (
                <div className="placeholderQR">
                  <QrCode style={{ fontSize: '120px', color: '#ccc' }} />
                </div>
              )}
            </div>
            
            <div className="codeContainer">
              <div className="codeLabel">Your Unique Code</div>
              <div className="codeText">{user.userCode}</div>
              <div className="codeDescription">
                Share this code with healthcare providers to connect with them
              </div>
            </div>
            
            <button 
              className="shareButton" 
              onClick={handleShareCode}
              disabled={loading}
            >
              {loading ? (
                <div className="spinner"></div>
              ) : (
                <>
                  <ShareIcon style={{ fontSize: 20 }} />
                  <span className="shareButtonText">Share Code</span>
                </>
              )}
            </button>
          </div>
        </div>
        
        <div className="card infoCard">
          <div className="cardContent">
            <div className="infoTitle">How to use your code</div>
            <div className="infoItem">
              <MedicalServices className="infoIcon" style={{ fontSize: 24 }} />
              <div className="infoText">Healthcare providers can use this code to connect with you</div>
            </div>
            <div className="infoItem">
              <QrCodeScanner className="infoIcon" style={{ fontSize: 24 }} />
              <div className="infoText">They can scan this QR code or enter the code manually</div>
            </div>
            <div className="infoItem">
              <LockOutlined className="infoIcon" style={{ fontSize: 24 }} />
              <div className="infoText">Only share with healthcare providers you trust</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserQRCode;
