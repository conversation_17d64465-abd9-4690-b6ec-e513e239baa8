/* Patient Dashboard Styles */

/* Main Layout */
.scrollView {
  flex: 1;
  background-color: #f8f9fc;
  overflow-y: auto;
  height: 100vh;
}

/* Header Section */
.headerSection {
  width: 100%;
  margin-bottom: 24px;
}

.headerGradient {
  padding: 28px 20px;
  border-bottom-left-radius: 24px;
  border-bottom-right-radius: 24px;
  box-shadow: 0 6px 8px rgba(0, 0, 0, 0.25);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-top-width: 0;
  background: linear-gradient(to right bottom, var(--patient-primary), var(--patient-primary-light), rgba(248, 249, 250, 0.95));
}

.greeting {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.greetingText {
  font-size: 26px;
  font-weight: bold;
  color: white;
  margin-bottom: 6px;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
  letter-spacing: 0.3px;
}

.dateText {
  font-size: 15px;
  color: rgba(255, 255, 255, 0.95);
  margin-top: 4px;
  letter-spacing: 0.2px;
}

/* Quick Actions */
.quickActionsContainer {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding: 0 16px;
  margin-bottom: 24px;
}

.quickActionButton {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 18%;
  margin-bottom: 16px;
  cursor: pointer;
}

.quickActionIcon {
  width: 50px;
  height: 50px;
  border-radius: 25px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.quickActionText {
  font-size: 12px;
  font-weight: bold;
  color: #333;
  text-align: center;
}

/* Section Styles */
.contentSection {
  padding: 0 16px 16px 16px;
}

.sectionHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  margin-top: 8px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.sectionTitleContainer {
  display: flex;
  align-items: center;
}

.sectionIcon {
  width: 32px;
  height: 32px;
  border-radius: 16px;
  background-color: rgba(255, 149, 43, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 8px;
}

.sectionTitle {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.sectionActions {
  display: flex;
}

.actionButton {
  display: flex;
  align-items: center;
  padding: 6px 12px;
  border-radius: 16px;
  background-color: rgba(255, 152, 0, 0.1);
  cursor: pointer;
}

.actionButtonText {
  margin-left: 6px;
  font-size: 14px;
  color: #FF9800;
  font-weight: 500;
}

/* Cards Container */
.cardsContainer {
  margin-bottom: 24px;
  padding: 0 16px;
}

.cardRow {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

/* Loading Container */
.loadingContainer {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 32px 0;
}

.loadingText {
  margin-top: 16px;
  font-size: 16px;
  color: #666;
}

/* Activity Section */
.activitySection {
  padding: 0 16px;
  margin-bottom: 24px;
}

.activityItems {
  background-color: white;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.activityItem {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.activityItem:last-child {
  border-bottom: none;
}

.activityIcon {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 12px;
}

.activityContent {
  flex: 1;
}

.activityTitle {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.activityTimeContainer {
  display: flex;
  align-items: center;
}

.activityTimeIcon {
  margin-right: 4px;
}

.activityTime {
  font-size: 12px;
  color: #757575;
}

/* Empty States */
.emptyActivity {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 32px 0;
}

.emptyActivityText {
  margin-top: 16px;
  font-size: 16px;
  color: #BDBDBD;
}

/* Lists Container */
.listsContainer {
  padding: 0 16px;
  margin-bottom: 24px;
}

/* Fade Animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fadeIn {
  animation: fadeIn 1s ease-out forwards;
}

/* CSS Variables for Theme Colors */
:root {
  --patient-primary: rgba(255, 149, 43, 1);
  --patient-primary-light: rgba(255, 149, 43, 0.8);
  --patient-primary-lighter: rgba(255, 149, 43, 0.1);
}

/* Spinner Animation */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Additional Styles for Tips Section */
.tipsSection {
  padding: 0 16px;
  margin-bottom: 24px;
}

.tipsWrapper {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.tipCard {
  display: flex;
  align-items: center;
  background-color: white;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tipIconContainer {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 12px;
}

.tipContent {
  flex: 1;
}

.tipTitle {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.tipText {
  font-size: 14px;
  color: #666;
}

/* Medication Reminders Section */
.medicationRemindersSection, .prescriptionsSection {
  padding: 0 16px;
  margin-bottom: 24px;
}
