import React, { useState, useEffect } from 'react';
import { Line } from 'react-chartjs-2';
import { Chart as ChartJS, CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend } from 'chart.js';
import { IoMdPulse, IoMdMic, IoMdFitness, IoMdHeart, IoMdWater, IoMdBody, IoMdClose, IoMdChevronForward } from 'react-icons/io';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../../../../contexts/AuthContext';
import { useVitals } from '../../../../../contexts/VitalsContext';
import '../../../../styles/dashboards/patient/healthRecords/healthRecords.css';

// Register ChartJS components
ChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend);

const HealthRecords = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { fetchVitals, vitals: firebaseVitals, loading: vitalsLoading } = useVitals();
  const [vitalsData, setVitalsData] = useState([]);
  const [error, setError] = useState(null);
  const [selectedVital, setSelectedVital] = useState('bloodPressure');
  const [timeRange, setTimeRange] = useState('month');
  const [displayMode, setDisplayMode] = useState('chart'); // 'chart' or 'table'

  const [vitalTypeFilter, setVitalTypeFilter] = useState('all'); // Filter for recorded vitals
  const [modalVisible, setModalVisible] = useState(false); // For the modal view of all vitals

  // Function to fetch vitals from Firebase
  useEffect(() => {
    const fetchAllVitals = async () => {
      try {
        // Fetch ALL vitals from Firebase (sans filtre par type)
        if (user) {
          // Passer null pour récupérer tous les types de données vitales
          await fetchVitals(null);
        }
      } catch (error) {
        console.error('Error fetching vitals:', error);
      }
    };

    fetchAllVitals();
  }, [timeRange, user]); // Removed selectedVital dependency since we fetch all types

  // Process Firebase data
  useEffect(() => {
    if (!firebaseVitals) return;

    try {
      // Transform data to the appropriate format
      const transformedData = [];

      for (const record of firebaseVitals) {
        // Vérifier que l'enregistrement a un timestamp valide
        if (!record.timestamp) {
          continue;
        }

        const date = new Date(record.timestamp);
        let formattedDate;

        if (timeRange === 'year') {
          formattedDate = date.toLocaleString('default', { month: 'short' });
        } else {
          formattedDate = `${date.getMonth() + 1}/${date.getDate()}`;
        }

        // Create a record with the appropriate structure
        const result = {
          date: formattedDate,
          timestamp: record.timestamp,
          recordId: record.id
        };

        // Add the appropriate vital sign data based on type
        if (record.vitalType === 'bloodPressure' && record.values) {
          result.bloodPressure = {
            systolic: parseInt(record.values.systolic) || 0,
            diastolic: parseInt(record.values.diastolic) || 0
          };
        } else if (record.vitalType === 'heartRate' && record.values) {
          result.heartRate = parseInt(record.values.value) || 0;
        } else if (record.vitalType === 'bloodGlucose' && record.values) {
          result.bloodGlucose = parseInt(record.values.value) || 0;
        } else if (record.vitalType === 'weight' && record.values) {
          result.weight = parseInt(record.values.value) || 0;
        } else {
          continue; // Skip this record
        }

        transformedData.push(result);
      }

      // Set the processed data
      setVitalsData(transformedData);
    } catch (error) {
      console.error('Error processing vitals data:', error);
    }
  }, [firebaseVitals, timeRange]);

  // Get vitals data for charts
  const getRecordedVitalsData = () => {
    // Use all vitals that match the selected vital type
    const filteredVitals = vitalsData.filter(item => {
      // Filter vitals by type
      if (selectedVital === 'bloodPressure' && item.bloodPressure) return true;
      if (selectedVital === 'heartRate' && item.heartRate) return true;
      if (selectedVital === 'bloodGlucose' && item.bloodGlucose) return true;
      if (selectedVital === 'weight' && item.weight) return true;
      return false;
    });

    // Sort by date (oldest to newest for charts)
    filteredVitals.sort((a, b) => {
      if (a.timestamp && b.timestamp) {
        return new Date(a.timestamp) - new Date(b.timestamp);
      }
      return 0;
    });

    return filteredVitals;
  };

  // Get data for charts (same as recorded vitals section)
  const getCombinedData = () => {
    // Use all vitals that match the selected vital type
    const filteredVitals = vitalsData.filter(item => {
      if (selectedVital === 'bloodPressure' && item.bloodPressure) return true;
      if (selectedVital === 'heartRate' && item.heartRate) return true;
      if (selectedVital === 'bloodGlucose' && item.bloodGlucose) return true;
      if (selectedVital === 'weight' && item.weight) return true;
      return false;
    });

    // Sort by date (oldest to newest for charts)
    filteredVitals.sort((a, b) => {
      if (a.timestamp && b.timestamp) {
        return new Date(a.timestamp) - new Date(b.timestamp);
      }
      return 0;
    });

    return filteredVitals;
  };

  // Prepare chart data based on selected vital
  const prepareChartData = () => {
    const recordedData = getRecordedVitalsData();
    if (!recordedData || recordedData.length === 0) return null;

    const labels = recordedData.map(item => item.date);

    if (selectedVital === 'bloodPressure') {
      return {
        labels,
        datasets: [
          {
            label: 'Systolic',
            data: recordedData.map(item => item.bloodPressure?.systolic || 0),
            borderColor: 'rgba(136, 132, 216, 1)',
            backgroundColor: 'rgba(136, 132, 216, 0.2)',
            tension: 0.4,
          },
          {
            label: 'Diastolic',
            data: recordedData.map(item => item.bloodPressure?.diastolic || 0),
            borderColor: 'rgba(130, 202, 157, 1)',
            backgroundColor: 'rgba(130, 202, 157, 0.2)',
            tension: 0.4,
          }
        ]
      };
    } else {
      let borderColor, backgroundColor;

      switch(selectedVital) {
        case 'heartRate':
          borderColor = 'rgba(255, 115, 0, 1)';
          backgroundColor = 'rgba(255, 115, 0, 0.2)';
          break;
        case 'bloodGlucose':
          borderColor = 'rgba(0, 136, 254, 1)';
          backgroundColor = 'rgba(0, 136, 254, 0.2)';
          break;
        case 'weight':
          borderColor = 'rgba(0, 196, 159, 1)';
          backgroundColor = 'rgba(0, 196, 159, 0.2)';
          break;
        default:
          borderColor = 'rgba(0, 0, 0, 1)';
          backgroundColor = 'rgba(0, 0, 0, 0.2)';
      }

      return {
        labels,
        datasets: [{
          label: getVitalTitle(),
          data: recordedData.map(item => {
            switch(selectedVital) {
              case 'heartRate': return item.heartRate || 0;
              case 'bloodGlucose': return item.bloodGlucose || 0;
              case 'weight': return item.weight || 0;
              default: return 0;
            }
          }),
          borderColor,
          backgroundColor,
          tension: 0.4,
        }]
      };
    }
  };

  const getVitalTitle = () => {
    switch(selectedVital) {
      case 'bloodPressure': return 'Blood Pressure';
      case 'heartRate': return 'Heart Rate';
      case 'bloodGlucose': return 'Blood Glucose';
      case 'weight': return 'Weight';
      default: return '';
    }
  };

  const getYAxisSuffix = () => {
    switch(selectedVital) {
      case 'bloodPressure': return ' mmHg';
      case 'heartRate': return ' bpm';
      case 'bloodGlucose': return ' mg/dL';
      case 'weight': return ' kg';
      default: return '';
    }
  };

  // Navigate to record vitals screen
  const navigateToRecordVitals = () => {
    navigate('/RecordVitals');
  };

  // Render data table view
  const renderDataTable = () => {
    const combinedData = getCombinedData();
    if (!combinedData || combinedData.length === 0) return null;

    // Determine columns based on vital sign type
    const getTableHeaders = () => {
      if (selectedVital === 'bloodPressure') {
        return ['Date', 'Systolic (mmHg)', 'Diastolic (mmHg)'];
      } else {
        return ['Date', `${getVitalTitle()} ${getYAxisSuffix().trim()}`];
      }
    };

    // Get table headers
    const headers = getTableHeaders();

    return (
      <div className="tableContainer">
        {/* Table Headers */}
        <div className="tableRow">
          {headers.map((header, index) => (
            <div key={index} className={`tableHeaderCell ${index === 0 ? 'dateCell' : 'valueCell'}`}>
              <span className="tableHeaderText">{header}</span>
            </div>
          ))}
        </div>

        {/* Table Data Rows - Fixed Scrolling */}
        <div className="tableBodyContainer">
          <div className="tableBodyContent">
            {getCombinedData().map((item, rowIndex) => (
              <div key={rowIndex} className={`tableRow ${rowIndex % 2 === 0 ? 'evenRow' : ''} recordedRow`}>
                {/* Date Cell */}
                <div className="tableCell dateCell">
                  <span className="tableCellText recordedText">
                    {item.date} 📱
                  </span>
                </div>

                {/* Value Cells (based on vital sign type) */}
                {selectedVital === 'bloodPressure' ? (
                  <>
                    <div className="tableCell valueCell">
                      <span className="tableCellText recordedText">
                        {item.bloodPressure?.systolic || '-'}
                      </span>
                    </div>
                    <div className="tableCell valueCell">
                      <span className="tableCellText recordedText">
                        {item.bloodPressure?.diastolic || '-'}
                      </span>
                    </div>
                  </>
                ) : (
                  <div className="tableCell valueCell">
                    <span className="tableCellText recordedText">
                      {selectedVital === 'heartRate' ? (item.heartRate || '-') :
                       selectedVital === 'bloodGlucose' ? (item.bloodGlucose || '-') :
                       selectedVital === 'weight' ? (item.weight || '-') : '-'}
                    </span>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  };

  const chartData = prepareChartData();

  // Change vital selection
  const handleVitalChange = (vital) => {
    setSelectedVital(vital);
  };

  // Change time range
  const handleTimeRangeChange = (range) => {
    setTimeRange(range);
  };

  // Change display mode
  const handleDisplayModeChange = (mode) => {
    setDisplayMode(mode);
  };

  // Render a section showing all recorded vitals
  const renderRecordedVitalsSection = () => {
    // Get all vitals
    const recordedVitals = vitalsData;

    if (recordedVitals.length === 0) {
      return (
        <div className="card">
          <p className="cardTitle">Your Recorded Vitals</p>
          <div className="emptyRecordsContainer">
            <IoMdPulse size={48} color="#4285F4" />
            <p className="emptyRecordsText">No vital signs recorded yet</p>
            <button
              className="recordVitalsButton"
              onClick={navigateToRecordVitals}
            >
              <IoMdMic size={18} color="#fff" />
              <span className="recordVitalsButtonText">Record Your First Vital</span>
            </button>
          </div>
        </div>
      );
    }

    // Filter vitals by selected type
    const filteredVitals = vitalTypeFilter === 'all'
      ? recordedVitals
      : recordedVitals.filter(vital => {
          switch(vitalTypeFilter) {
            case 'bloodPressure': return !!vital.bloodPressure;
            case 'heartRate': return !!vital.heartRate;
            case 'bloodGlucose': return !!vital.bloodGlucose;
            case 'weight': return !!vital.weight;
            default: return true;
          }
        });

    // Sort vitals by timestamp (newest first)
    filteredVitals.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

    // Format date for display
    const formatDate = (dateString) => {
      const date = new Date(dateString);
      return date.toLocaleString();
    };

    // Get vital value based on type
    const getVitalValue = (vital) => {
      if (vital.bloodPressure) {
        return `${vital.bloodPressure.systolic}/${vital.bloodPressure.diastolic} mmHg`;
      } else if (vital.heartRate) {
        return `${vital.heartRate} bpm`;
      } else if (vital.bloodGlucose) {
        return `${vital.bloodGlucose} mg/dL`;
      } else if (vital.weight) {
        return `${vital.weight} kg`;
      }
      return 'N/A';
    };

    // Get vital type name
    const getVitalTypeName = (vital) => {
      if (vital.bloodPressure) return 'Blood Pressure';
      if (vital.heartRate) return 'Heart Rate';
      if (vital.bloodGlucose) return 'Blood Glucose';
      if (vital.weight) return 'Weight';
      return 'Unknown';
    };

    // Get icon and color for vital type
    const getVitalTypeIcon = (vital) => {
      if (vital.bloodPressure) {
        return { icon: IoMdFitness, color: '#4285F4' };
      } else if (vital.heartRate) {
        return { icon: IoMdHeart, color: '#E91E63' };
      } else if (vital.bloodGlucose) {
        return { icon: IoMdWater, color: '#FBBC05' };
      } else if (vital.weight) {
        return { icon: IoMdBody, color: '#34A853' };
      }
      return { icon: IoMdPulse, color: '#9E9E9E' };
    };

    return (
      <div className="card">
        <div className="cardHeader">
          <p className="cardTitle">Your Recorded Vitals</p>
          <button
            className="recordButton"
            onClick={navigateToRecordVitals}
          >
            <IoMdMic size={18} color="#fff" />
            <span className="recordButtonText">Record New</span>
          </button>
        </div>

        {/* Filter buttons */}
        <div className="filterContainer">
          <div className="filterScrollContainer">
            <button
              className={`filterButton ${vitalTypeFilter === 'all' ? 'filterButtonActive' : ''}`}
              onClick={() => setVitalTypeFilter('all')}
            >
              <span className={`filterButtonText ${vitalTypeFilter === 'all' ? 'filterButtonTextActive' : ''}`}>All</span>
            </button>

            <button
              className={`filterButton ${vitalTypeFilter === 'bloodPressure' ? 'filterButtonActive' : ''}`}
              onClick={() => setVitalTypeFilter('bloodPressure')}
            >
              <IoMdFitness size={16} color={vitalTypeFilter === 'bloodPressure' ? '#fff' : '#4285F4'} />
              <span className={`filterButtonText ${vitalTypeFilter === 'bloodPressure' ? 'filterButtonTextActive' : ''}`}>Blood Pressure</span>
            </button>

            <button
              className={`filterButton ${vitalTypeFilter === 'heartRate' ? 'filterButtonActive' : ''}`}
              onClick={() => setVitalTypeFilter('heartRate')}
            >
              <IoMdHeart size={16} color={vitalTypeFilter === 'heartRate' ? '#fff' : '#E91E63'} />
              <span className={`filterButtonText ${vitalTypeFilter === 'heartRate' ? 'filterButtonTextActive' : ''}`}>Heart Rate</span>
            </button>

            <button
              className={`filterButton ${vitalTypeFilter === 'bloodGlucose' ? 'filterButtonActive' : ''}`}
              onClick={() => setVitalTypeFilter('bloodGlucose')}
            >
              <IoMdWater size={16} color={vitalTypeFilter === 'bloodGlucose' ? '#fff' : '#FBBC05'} />
              <span className={`filterButtonText ${vitalTypeFilter === 'bloodGlucose' ? 'filterButtonTextActive' : ''}`}>Blood Glucose</span>
            </button>

            <button
              className={`filterButton ${vitalTypeFilter === 'weight' ? 'filterButtonActive' : ''}`}
              onClick={() => setVitalTypeFilter('weight')}
            >
              <IoMdBody size={16} color={vitalTypeFilter === 'weight' ? '#fff' : '#34A853'} />
              <span className={`filterButtonText ${vitalTypeFilter === 'weight' ? 'filterButtonTextActive' : ''}`}>Weight</span>
            </button>
          </div>
        </div>

        {/* List of first 4 vitals */}
        <div className="allVitalsContainer">
          {filteredVitals.length === 0 ? (
            <p className="emptyFilterText">No records found for the selected filter</p>
          ) : (
            <>
              {filteredVitals.slice(0, 4).map((vital, index) => {
                const { icon: IconComponent, color: iconColor } = getVitalTypeIcon(vital);

                return (
                  <div key={index} className="vitalCard">
                    <div className="vitalIconContainer" style={{ backgroundColor: iconColor }}>
                      <IconComponent size={24} color="#fff" />
                    </div>
                    <div className="vitalInfo">
                      <p className="vitalType">
                        {getVitalTypeName(vital)}
                      </p>
                      <p className="vitalValue">{getVitalValue(vital)}</p>
                      <p className="vitalDate">{formatDate(vital.timestamp)}</p>
                    </div>
                  </div>
                );
              })}

              {filteredVitals.length > 4 && (
                <button
                  className="seeMoreButton"
                  onClick={() => setModalVisible(true)}
                >
                  <span className="seeMoreButtonText">See More ({filteredVitals.length - 4} more)</span>
                  <IoMdChevronForward size={16} color="#4285F4" />
                </button>
              )}
            </>
          )}
        </div>

        {/* Modal for showing all vitals */}
        {modalVisible && (
          <div className="modalOverlay" onClick={() => setModalVisible(false)}>
            <div className="modalContent" onClick={e => e.stopPropagation()}>
              <div className="modalHeader">
                <p className="modalTitle">Vitals History (Last 30 Days)</p>
                <button
                  className="closeButton"
                  onClick={() => setModalVisible(false)}
                >
                  <IoMdClose size={24} color="#333" />
                </button>
              </div>

              {/* Modal Filter Buttons */}
              <div className="modalFilterContainer">
                <div className="filterScrollContainer">
                  <button
                    className={`modalFilterButton ${vitalTypeFilter === 'all' ? 'modalFilterButtonActive' : ''}`}
                    onClick={() => setVitalTypeFilter('all')}
                  >
                    <span className={`modalFilterButtonText ${vitalTypeFilter === 'all' ? 'modalFilterButtonTextActive' : ''}`}>All</span>
                  </button>

                  <button
                    className={`modalFilterButton ${vitalTypeFilter === 'bloodPressure' ? 'modalFilterButtonActive' : ''}`}
                    onClick={() => setVitalTypeFilter('bloodPressure')}
                  >
                    <IoMdFitness size={16} color={vitalTypeFilter === 'bloodPressure' ? '#fff' : '#4285F4'} />
                    <span className={`modalFilterButtonText ${vitalTypeFilter === 'bloodPressure' ? 'modalFilterButtonTextActive' : ''}`}>Blood Pressure</span>
                  </button>

                  <button
                    className={`modalFilterButton ${vitalTypeFilter === 'heartRate' ? 'modalFilterButtonActive' : ''}`}
                    onClick={() => setVitalTypeFilter('heartRate')}
                  >
                    <IoMdHeart size={16} color={vitalTypeFilter === 'heartRate' ? '#fff' : '#E91E63'} />
                    <span className={`modalFilterButtonText ${vitalTypeFilter === 'heartRate' ? 'modalFilterButtonTextActive' : ''}`}>Heart Rate</span>
                  </button>

                  <button
                    className={`modalFilterButton ${vitalTypeFilter === 'bloodGlucose' ? 'modalFilterButtonActive' : ''}`}
                    onClick={() => setVitalTypeFilter('bloodGlucose')}
                  >
                    <IoMdWater size={16} color={vitalTypeFilter === 'bloodGlucose' ? '#fff' : '#FBBC05'} />
                    <span className={`modalFilterButtonText ${vitalTypeFilter === 'bloodGlucose' ? 'modalFilterButtonTextActive' : ''}`}>Blood Glucose</span>
                  </button>

                  <button
                    className={`modalFilterButton ${vitalTypeFilter === 'weight' ? 'modalFilterButtonActive' : ''}`}
                    onClick={() => setVitalTypeFilter('weight')}
                  >
                    <IoMdBody size={16} color={vitalTypeFilter === 'weight' ? '#fff' : '#34A853'} />
                    <span className={`modalFilterButtonText ${vitalTypeFilter === 'weight' ? 'modalFilterButtonTextActive' : ''}`}>Weight</span>
                  </button>
                </div>
              </div>

              <div className="modalScrollView">
                {filteredVitals
                  .filter(vital => {
                    // Filter for last 30 days
                    const vitalDate = new Date(vital.timestamp);
                    const thirtyDaysAgo = new Date();
                    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
                    return vitalDate >= thirtyDaysAgo;
                  })
                  // Apply type filter
                  .filter(vital => {
                    if (vitalTypeFilter === 'all') return true;
                    if (vitalTypeFilter === 'bloodPressure' && vital.bloodPressure) return true;
                    if (vitalTypeFilter === 'heartRate' && vital.heartRate) return true;
                    if (vitalTypeFilter === 'bloodGlucose' && vital.bloodGlucose) return true;
                    if (vitalTypeFilter === 'weight' && vital.weight) return true;
                    return false;
                  })
                  // Sort by date (newest first)
                  .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
                  // Limit to 30 records
                  .slice(0, 30)
                  .map((vital, index) => {
                    const { icon: IconComponent, color: iconColor } = getVitalTypeIcon(vital);

                    return (
                      <div key={index} className="modalVitalCard">
                        <div className="vitalIconContainer" style={{ backgroundColor: iconColor }}>
                          <IconComponent size={24} color="#fff" />
                        </div>
                        <div className="vitalInfo">
                          <p className="vitalType">
                            {getVitalTypeName(vital)}
                          </p>
                          <p className="vitalValue">{getVitalValue(vital)}</p>
                          <p className="vitalDate">{formatDate(vital.timestamp)}</p>
                        </div>
                      </div>
                    );
                  })
                }
              </div>
            </div>
          </div>
        )}
      </div>
    );
  };

  // Afficher un loader pendant le chargement des données
  if (vitalsLoading) {
    return (
      <div className="loaderContainer">
        <div className="spinner"></div>
        <p className="loaderText">Loading health records...</p>
      </div>
    );
  }

  return (
    <div className="container">
      <div className="headerContainer">
        <h1 className="header">Health Records</h1>
      </div>

      {/* Recorded Vitals Section */}
      {renderRecordedVitalsSection()}

      {/* Trends Section */}
      <div className="card">
        <h2 className="cardTitle">Vital Signs Trends</h2>

        <div className="selectorsContainer">
          <div className="selectorWrapper">
            <p className="label">Vital Sign:</p>
            <div className="buttonRow">
              <button
                className={`button ${selectedVital === 'bloodPressure' ? 'selectedButton' : ''}`}
                onClick={() => handleVitalChange('bloodPressure')}>
                <span className={`buttonText ${selectedVital === 'bloodPressure' ? 'selectedButtonText' : ''}`}>Blood Pressure</span>
              </button>
              <button
                className={`button ${selectedVital === 'heartRate' ? 'selectedButton' : ''}`}
                onClick={() => handleVitalChange('heartRate')}>
                <span className={`buttonText ${selectedVital === 'heartRate' ? 'selectedButtonText' : ''}`}>Heart Rate</span>
              </button>
            </div>
            <div className="buttonRow">
              <button
                className={`button ${selectedVital === 'bloodGlucose' ? 'selectedButton' : ''}`}
                onClick={() => handleVitalChange('bloodGlucose')}>
                <span className={`buttonText ${selectedVital === 'bloodGlucose' ? 'selectedButtonText' : ''}`}>Blood Glucose</span>
              </button>
              <button
                className={`button ${selectedVital === 'weight' ? 'selectedButton' : ''}`}
                onClick={() => handleVitalChange('weight')}>
                <span className={`buttonText ${selectedVital === 'weight' ? 'selectedButtonText' : ''}`}>Weight</span>
              </button>
            </div>
          </div>

          <div className="selectorWrapper">
            <p className="label">Time Period:</p>
            <div className="buttonRow">
              <button
                className={`button ${timeRange === 'week' ? 'selectedButton' : ''}`}
                onClick={() => handleTimeRangeChange('week')}>
                <span className={`buttonText ${timeRange === 'week' ? 'selectedButtonText' : ''}`}>Last Week</span>
              </button>
              <button
                className={`button ${timeRange === 'month' ? 'selectedButton' : ''}`}
                onClick={() => handleTimeRangeChange('month')}>
                <span className={`buttonText ${timeRange === 'month' ? 'selectedButtonText' : ''}`}>Last Month</span>
              </button>
              <button
                className={`button ${timeRange === 'year' ? 'selectedButton' : ''}`}
                onClick={() => handleTimeRangeChange('year')}>
                <span className={`buttonText ${timeRange === 'year' ? 'selectedButtonText' : ''}`}>Last Year</span>
              </button>
            </div>
          </div>

          <div className="selectorWrapper">
            <p className="label">Display Mode:</p>
            <div className="buttonRow">
              <button
                className={`button ${displayMode === 'chart' ? 'selectedButton' : ''}`}
                onClick={() => handleDisplayModeChange('chart')}>
                <span className={`buttonText ${displayMode === 'chart' ? 'selectedButtonText' : ''}`}>Chart</span>
              </button>
              <button
                className={`button ${displayMode === 'table' ? 'selectedButton' : ''}`}
                onClick={() => handleDisplayModeChange('table')}>
                <span className={`buttonText ${displayMode === 'table' ? 'selectedButtonText' : ''}`}>Table</span>
              </button>
            </div>
          </div>
        </div>

        {vitalsLoading ? (
          <p className="message">Loading data...</p>
        ) : error ? (
          <p className="errorMessage">{error}</p>
        ) : chartData ? (
          <div className="chartContainer">
            <h3 className="chartTitle">
              {getVitalTitle()} - {
                timeRange === 'week' ? 'Last Week' :
                timeRange === 'month' ? 'Last Month' :
                'Last Year'
              }
            </h3>

            {displayMode === 'chart' ? (
              <div>
                <div className="chartWrapper">
                  <Line
                    data={chartData}
                    options={{
                      responsive: true,
                      maintainAspectRatio: false,
                      scales: {
                        y: {
                          beginAtZero: selectedVital === 'weight',
                          title: {
                            display: true,
                            text: getYAxisSuffix().trim()
                          }
                        }
                      },
                      plugins: {
                        legend: {
                          display: true,
                          position: 'top'
                        },
                        tooltip: {
                          callbacks: {
                            label: function(context) {
                              return context.dataset.label + ': ' + context.parsed.y + getYAxisSuffix();
                            }
                          }
                        }
                      }
                    }}
                  />
                </div>
                <div className="legendContainer">
                  <div className="legendItem">
                    <div className="legendDot" style={{ backgroundColor: '#4285F4' }}></div>
                    <span className="legendText">Recorded Data</span>
                  </div>
                </div>
              </div>
            ) : (
              renderDataTable()
            )}
          </div>
        ) : (
          <p className="message">No data available</p>
        )}
      </div>
    </div>
  );
};

export default HealthRecords;