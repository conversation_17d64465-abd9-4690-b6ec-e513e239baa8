/* Dashboard Layout CSS */
.dashboard-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  background-color: #f5f7fa;
}

.dashboard-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  z-index: 10;
  position: relative;
}

.header-title {
  font-size: 20px;
  font-weight: bold;
  color: white;
  margin: 0;
}

.menu-button {
  padding: 8px;
  background: none;
  border: none;
  cursor: pointer;
  z-index: 20;
}

.header-right {
  display: flex;
  align-items: center;
}

.voice-button {
  padding: 8px;
  margin-right: 8px;
  background: none;
  border: none;
  cursor: pointer;
}

.dashboard-content {
  flex: 1;
  overflow-y: auto;
  background-color: #f5f7fa;
  position: relative;
}

/* Sidebar styles */
.sidebar {
  position: fixed;
  top: 0;
  left: -300px;
  width: 300px;
  height: 100%;
  background-color: white;
  box-shadow: 2px 0 5px rgba(0, 0, 0, 0.2);
  z-index: 100;
  transition: left 0.3s ease;
  display: flex;
  flex-direction: column;
}

.sidebar.open {
  left: 0;
}

.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 90;
}

.sidebar-header {
  padding: 20px 16px;
  border-bottom: 1px solid #e0e0e0;
}

.profile-section {
  display: flex;
  align-items: center;
}

.profile-icon {
  width: 50px;
  height: 50px;
  border-radius: 25px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 12px;
  position: relative;
}

.profile-icon-text {
  color: white;
  font-size: 18px;
  font-weight: bold;
}

.incomplete-profile-badge {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 18px;
  height: 18px;
  border-radius: 9px;
  background-color: #f44336;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.profile-info {
  flex: 1;
}

.profile-name {
  font-size: 16px;
  font-weight: bold;
  color: #212121;
  margin-bottom: 4px;
}

.role-name {
  font-size: 14px;
  color: #757575;
}

.incomplete-text {
  color: #f44336;
}

.menu-container {
  flex: 1;
  overflow-y: auto;
  padding: 16px 0;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.menu-item:hover {
  background-color: #f5f5f5;
}

.menu-icon {
  margin-right: 12px;
  display: flex;
  align-items: center;
  color: #757575;
}

.menu-item-text {
  font-size: 16px;
  color: #424242;
}

.active-menu-item {
  background-color: #f0f0f0;
  border-left: 4px solid;
}

.active-menu-item-text {
  font-weight: bold;
  color: #212121;
}

.divider {
  height: 1px;
  background-color: #e0e0e0;
  margin: 16px 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .sidebar {
    width: 75%;
    left: -75%;
  }
  
  .header-title {
    font-size: 18px;
  }
}
