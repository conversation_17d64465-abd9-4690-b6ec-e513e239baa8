import React, { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import axios from "axios";
import { IoArrowBack } from "react-icons/io5";
import { showMessage } from 'react-toastify';
import { useAuth } from '../../contexts/AuthContext';
import { firestore, auth, storage } from '../../config/firebase';
import { doc, getDoc, updateDoc, setDoc } from 'firebase/firestore';
import { updateProfile } from 'firebase/auth';
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import { getThemeForRole } from '../../config/theme';
import SharedProfileForm from './SharedProfileForm';
import UserQRCodeSection from './UserQRCodeSection';
import ProfileImageUploader from './ProfileImageUploader';
import { authAPI } from '../../config/api';
import "./profile.css";

// Main component
const Profile = () => {
  const { user, updateUserProfile } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const [loading, setLoading] = useState(false);
  const [initialData, setInitialData] = useState({});
  const [dataLoading, setDataLoading] = useState(true);
  const theme = getThemeForRole(user?.role || 'default');

  // Check if profile completion is required (passed from route params)
  const isRequired = location.state?.required || false;

  // If profile is required, force editing mode
  useEffect(() => {
    if (isRequired) {
      setLoading(true);
    }
  }, [isRequired]);

  // Fetch user data on component mount
  useEffect(() => {
    if (user) {
      fetchUserData();
    } else {
      setDataLoading(false);
    }
  }, [user]);

  const fetchUserData = async () => {
    if (!user) return;

    try {
      const userRef = doc(firestore, 'users', user.uid);
      const userSnap = await getDoc(userRef);

      if (userSnap.exists()) {
        const userData = userSnap.data();
        setInitialData(userData);
      }
    } catch (error) {
      showMessage({
        message: 'Error',
        description: 'Could not load user data',
        type: 'danger',
        backgroundColor: theme.colors.error,
      });
      console.error('Error fetching user data:', error);
    } finally {
      setDataLoading(false);
    }
  };

  const handleSubmit = async (formData) => {
    setLoading(true);

    try {
      // Show a progress message
      showMessage({
        message: 'Saving...',
        description: 'Updating your profile',
        type: 'info',
        backgroundColor: theme.colors.primary,
        duration: 2000,
      });

      // Update user profile in Firestore
      const userRef = doc(firestore, 'users', user.uid);
      const updatedData = {
        ...formData,
        updatedAt: new Date().toISOString()
      };

      // First update Firestore (this is most important)
      await setDoc(userRef, updatedData, { merge: true });

      // Update local user state right after Firestore success
      await updateUserProfile(updatedData);

      // Then try to update the backend API
      try {
        await authAPI.updateProfile(updatedData);
      } catch (apiError) {
        console.warn('API update failed, but Firestore updated successfully:', apiError);
        // Don't throw error here, we already updated Firestore
      }

      showMessage({
        message: 'Success',
        description: 'Your profile has been updated successfully',
        type: 'success',
        backgroundColor: theme.colors.primary,
      });

      // Refresh the data
      await fetchUserData();
    } catch (error) {
      showMessage({
        message: 'Error',
        description: 'Failed to update profile',
        type: 'danger',
        backgroundColor: theme.colors.error,
      });
      console.error('Profile update error:', error);
    } finally {
      setLoading(false);
    }
  };

  if (dataLoading) {
    return (
      <div className="loadingContainer" style={{ backgroundColor: theme.colors.background }}>
        <div className="spinner"></div>
      </div>
    );
  }

  return (
    <div className="container" style={{ backgroundColor: theme.colors.background }}>
      <header className="appHeader" style={{ backgroundColor: theme.colors.primary }}>
        <button className="backButton" onClick={() => navigate(-1)}>
          <IoArrowBack size={24} />
        </button>
        <h1 className="headerTitle">My Profile</h1>
      </header>

      <div className="scrollView">
        <div className="content">
          <UserQRCodeSection />

          <ProfileImageUploader />

          <SharedProfileForm
            initialData={initialData}
            onSubmit={handleSubmit}
            loading={loading}
            requiredFields={[]}
            buttonText="Update Profile"
            theme={theme}
            nonEditableFields={['firstName', 'lastName', 'email']}
          />
        </div>
      </div>
    </div>
  );
};

export default Profile;
