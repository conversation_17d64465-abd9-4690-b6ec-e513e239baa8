import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { format, parseISO } from 'date-fns';
import { fr } from 'date-fns/locale';
import { IoMedical, IoCalendarOutline, IoClose } from 'react-icons/io5';
import { useAuth } from '../../client/src/contexts/AuthContext';
import { firebaseAppointmentsService } from '../../client/src/services/firebaseAppointmentsService';
import { ROLE_COLORS } from '../../client/src/config/theme';
import '../styles/screens/caregiverAppointmentsScreen.css';

// Get caregiver colors for use in styles
const caregiverColors = ROLE_COLORS.caregiver;

const CaregiverAppointmentsScreen = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [appointments, setAppointments] = useState([]);
  const [filteredAppointments, setFilteredAppointments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedAppointment, setSelectedAppointment] = useState(null);
  const [detailsModalVisible, setDetailsModalVisible] = useState(false);
  const [filterStatus, setFilterStatus] = useState('all');

  // Fetch appointments on component mount
  useEffect(() => {
    fetchAppointments();
  }, []);

  // Fetch appointments from Firebase
  const fetchAppointments = async () => {
    try {
      setLoading(true);
      
      // Get appointments for patients linked to this caregiver
      const options = {
        includePatientDetails: true,
        dateOrder: 'asc',  // Upcoming appointments first
        timeOrder: 'asc'   // Earlier times first for same day
      };
      
      const fetchedAppointments = await firebaseAppointmentsService.getCaregiverPatientAppointments(options);
      setAppointments(fetchedAppointments);
      applyFilters(fetchedAppointments, filterStatus);
    } catch (error) {
      console.error('Error fetching appointments:', error);
      alert('Failed to load appointments. Please try again.');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Apply filters to appointments
  const applyFilters = (appointmentsToFilter, status) => {
    let filtered = [...appointmentsToFilter];
    
    // Filter by status if not 'all'
    if (status !== 'all') {
      filtered = filtered.filter(appointment => 
        appointment.status.toLowerCase() === status.toLowerCase()
      );
    }
    
    setFilteredAppointments(filtered);
  };

  // Handle filter change
  const handleFilterChange = (status) => {
    setFilterStatus(status);
    applyFilters(appointments, status);
  };

  // Handle refresh
  const handleRefresh = () => {
    setRefreshing(true);
    fetchAppointments();
  };

  // Handle appointment selection
  const handleAppointmentPress = (appointment) => {
    setSelectedAppointment(appointment);
    setDetailsModalVisible(true);
  };

  // Get status color
  const getStatusColor = (status) => {
    const statusLower = status?.toLowerCase() || '';
    switch (statusLower) {
      case 'confirmed':
      case 'completed':
        return '#4CAF50'; // Green
      case 'pending':
        return '#FF9800'; // Orange
      case 'cancelled':
        return '#F44336'; // Red
      case 'rescheduled':
        return '#2196F3'; // Blue
      default:
        return '#9E9E9E'; // Grey
    }
  };

  // Format date for display
  const formatDate = (dateString) => {
    try {
      if (!dateString) return 'No date';
      return format(parseISO(dateString), 'dd MMMM yyyy', { locale: fr });
    } catch (error) {
      try {
        // Try alternative format
        return format(new Date(dateString), 'dd MMMM yyyy', { locale: fr });
      } catch (e) {
        console.error('Error formatting date:', e);
        return dateString;
      }
    }
  };

  // Render appointment item
  const renderAppointmentItem = (item) => {
    const statusColor = getStatusColor(item.status);
    const patientName = item.patient?.firstName 
      ? `${item.patient.firstName} ${item.patient.lastName || ''}`
      : item.patientName || 'Patient';
    
    const doctorName = item.doctor?.firstName 
      ? `${item.doctor.firstName} ${item.doctor.lastName || ''}`
      : item.doctor || 'Doctor';
    
    const appointmentDate = formatDate(item.date);
    
    return (
      <div 
        className="appointmentCard"
        onClick={() => handleAppointmentPress(item)}
        key={item.id}
      >
        <div className="card">
          <div className="cardHeader">
            <div className="patientInfo">
              <div className="patientName">{patientName}</div>
              <div className="appointmentDate">{appointmentDate}</div>
              <div className="appointmentTime">{item.time}</div>
            </div>
            <div className="statusContainer">
              <div 
                className="statusChip"
                style={{ backgroundColor: `${statusColor}20`, color: statusColor }}
              >
                {item.status.toUpperCase()}
              </div>
            </div>
          </div>
          <div className="doctorContainer">
            <IoMedical size={16} color={caregiverColors.primary} />
            <span className="doctorName">{doctorName}</span>
          </div>
          {item.reason && (
            <div className="reason">Reason: {item.reason}</div>
          )}
        </div>
      </div>
    );
  };

  // Render appointment details modal
  const renderDetailsModal = () => (
    detailsModalVisible && selectedAppointment && (
      <div className="modalOverlay" onClick={() => setDetailsModalVisible(false)}>
        <div className="modalContainer" onClick={(e) => e.stopPropagation()}>
          <div className="modalHeader">
            <h2 className="modalTitle">Appointment Details</h2>
            <button
              className="closeButton"
              onClick={() => setDetailsModalVisible(false)}
            >
              <IoClose size={24} color="#666" />
            </button>
          </div>
          
          <div className="modalContent">
            <div className="detailRow">
              <div className="detailLabel">Patient:</div>
              <div className="detailValue">
                {selectedAppointment.patient?.firstName 
                  ? `${selectedAppointment.patient.firstName} ${selectedAppointment.patient.lastName || ''}`
                  : selectedAppointment.patientName || 'Patient'}
              </div>
            </div>
            
            <div className="detailRow">
              <div className="detailLabel">Doctor:</div>
              <div className="detailValue">
                {selectedAppointment.doctor?.firstName 
                  ? `${selectedAppointment.doctor.firstName} ${selectedAppointment.doctor.lastName || ''}`
                  : selectedAppointment.doctor || 'Doctor'}
              </div>
            </div>
            
            <div className="detailRow">
              <div className="detailLabel">Date:</div>
              <div className="detailValue">
                {formatDate(selectedAppointment.date)}
              </div>
            </div>
            
            <div className="detailRow">
              <div className="detailLabel">Time:</div>
              <div className="detailValue">{selectedAppointment.time}</div>
            </div>
            
            <div className="detailRow">
              <div className="detailLabel">Status:</div>
              <div 
                className="statusChip"
                style={{ 
                  backgroundColor: `${getStatusColor(selectedAppointment.status)}20`,
                  color: getStatusColor(selectedAppointment.status)
                }}
              >
                {selectedAppointment.status.toUpperCase()}
              </div>
            </div>
            
            {selectedAppointment.reason && (
              <div className="detailRow">
                <div className="detailLabel">Reason:</div>
                <div className="detailValue">{selectedAppointment.reason}</div>
              </div>
            )}
            
            {selectedAppointment.notes && (
              <div className="detailRow">
                <div className="detailLabel">Notes:</div>
                <div className="detailValue">{selectedAppointment.notes}</div>
              </div>
            )}
          </div>
          
          <button
            className="closeModalButton"
            onClick={() => setDetailsModalVisible(false)}
          >
            Close
          </button>
        </div>
      </div>
    )
  );

  return (
    <div className="container">
      {renderDetailsModal()}
      
      <div className="filterContainer">
        <div className="filterButton" 
          onClick={() => handleFilterChange('all')}
          style={filterStatus === 'all' ? { backgroundColor: caregiverColors.primary } : {}}
        >
          <span 
            className={filterStatus === 'all' ? 'activeFilterButtonText' : 'filterButtonText'}
          >
            All
          </span>
        </div>
        
        <div 
          className="filterButton"
          onClick={() => handleFilterChange('pending')}
          style={filterStatus === 'pending' ? { backgroundColor: caregiverColors.primary } : {}}
        >
          <span 
            className={filterStatus === 'pending' ? 'activeFilterButtonText' : 'filterButtonText'}
          >
            Pending
          </span>
        </div>
        
        <div 
          className="filterButton"
          onClick={() => handleFilterChange('confirmed')}
          style={filterStatus === 'confirmed' ? { backgroundColor: caregiverColors.primary } : {}}
        >
          <span 
            className={filterStatus === 'confirmed' ? 'activeFilterButtonText' : 'filterButtonText'}
          >
            Confirmed
          </span>
        </div>
        
        <div 
          className="filterButton"
          onClick={() => handleFilterChange('completed')}
          style={filterStatus === 'completed' ? { backgroundColor: caregiverColors.primary } : {}}
        >
          <span 
            className={filterStatus === 'completed' ? 'activeFilterButtonText' : 'filterButtonText'}
          >
            Completed
          </span>
        </div>
        
        <div 
          className="filterButton"
          onClick={() => handleFilterChange('cancelled')}
          style={filterStatus === 'cancelled' ? { backgroundColor: caregiverColors.primary } : {}}
        >
          <span 
            className={filterStatus === 'cancelled' ? 'activeFilterButtonText' : 'filterButtonText'}
          >
            Cancelled
          </span>
        </div>
      </div>
      
      {loading && !refreshing ? (
        <div className="loadingContainer">
          <div className="spinner" style={{ borderTopColor: caregiverColors.primary }}></div>
          <div className="loadingText">Loading appointments...</div>
        </div>
      ) : filteredAppointments.length === 0 ? (
        <div className="emptyContainer">
          <IoCalendarOutline size={64} color="#ccc" />
          <div className="emptyText">No appointments found</div>
          <div className="emptySubText">
            {filterStatus !== 'all'
              ? `No ${filterStatus} appointments found. Try another filter.`
              : 'Your patients have no scheduled appointments.'}
          </div>
        </div>
      ) : (
        <div className="listContainer">
          {filteredAppointments.map(item => renderAppointmentItem(item))}
        </div>
      )}
    </div>
  );
};

export default CaregiverAppointmentsScreen;
