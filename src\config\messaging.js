import { isSupported, getToken, onMessage } from 'firebase/messaging';
import { getMessaging } from 'firebase/messaging';
import { getApp } from 'firebase/app';

/**
 * Initialize Firebase Cloud Messaging for Web
 * 
 * This function handles all the Firebase Cloud Messaging
 * initialization for web platforms.
 */
export const initializeMessaging = async () => {
  try {
    // Check if the browser supports Firebase Cloud Messaging
    if (!await isSupported()) {
      console.log('Firebase messaging is not supported in this browser');
      return { token: null, enabled: false };
    }

    try {
      // Get the messaging instance
      const app = getApp();
      const messaging = getMessaging(app);
      
      // Request permission
      const permission = await Notification.requestPermission();
      const enabled = permission === 'granted';
      
      if (!enabled) {
        console.log('Firebase messaging permissions not granted');
        return { token: null, enabled: false };
      }
      
      // Get FCM token
      // Note: You need to provide your VAPID key in a real implementation
      const token = await getToken(messaging, { 
        vapidKey: process.env.REACT_APP_FIREBASE_VAPID_KEY 
      });
      console.log('Firebase token:', token ? token.substring(0, 10) + '...' : 'null');
      
      // Configure foreground notification handling
      onMessage(messaging, (payload) => {
        console.log('Foreground message received:', payload);
        // Here you can add a custom notification display for foreground messages
        // For example, you could use the Notification API or a custom UI component
        if (permission === 'granted') {
          const notificationTitle = payload.notification.title;
          const notificationOptions = {
            body: payload.notification.body,
            icon: '/logo192.png',
          };
          
          new Notification(notificationTitle, notificationOptions);
        }
      });
      
      // Return the token and enabled status
      return { token, enabled: true };
    } catch (error) {
      console.log('Error initializing Firebase messaging:', error.message);
      return { token: null, enabled: false, error };
    }
  } catch (error) {
    console.log('Error checking Firebase messaging support:', error.message);
    return { token: null, enabled: false, error };
  }
};

/**
 * Register the device's FCM token with your backend
 */
export const registerDeviceForNotifications = async (userId, token, apiToken) => {
  if (!token) {
    console.log('No token provided for registration');
    return false;
  }
  
  try {
    console.log('Registering device for user:', userId);
    
    // Make an API call to your backend to register the FCM token
    const response = await fetch('http://192.168.90.134:3000/api/users/register-device', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiToken}`
      },
      body: JSON.stringify({
        userId,
        token,
        device: 'web'
      })
    });
    
    const data = await response.json();
    console.log('Device registration response:', data);
    return data.success;
  } catch (error) {
    console.error('Error registering device for notifications:', error);
    return false;
  }
};

export default {
  initializeMessaging,
  registerDeviceForNotifications
};
