import React, { useState, useEffect } from 'react';
import { Line } from 'react-chartjs-2';
import { Chart as ChartJS, CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend } from 'chart.js';
import { IoHeart, IoFitness, IoWater, IoBody, IoPulse, IoChevronForward, IoClose } from 'react-icons/io5';
import { firebaseDoctorPatientsService } from '../../../services/firebaseDoctorPatientsService';
import { ROLE_COLORS } from '../../../config/theme';
import '../../../../styles/dashboards/doctor/doctor_patient_detail/patientVitalsViewer.css';

// Register ChartJS components
ChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend);

const PatientVitalsViewer = ({ patientId, patientName }) => {
  const [loading, setLoading] = useState(true);
  const [patientVitals, setPatientVitals] = useState([]);
  const [selectedVital, setSelectedVital] = useState('heartRate');
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedVitalRecord, setSelectedVitalRecord] = useState(null);
  const [timeRange, setTimeRange] = useState('week');
  const doctorColors = ROLE_COLORS.doctor;

  useEffect(() => {
    fetchPatientVitals();
  }, [patientId, selectedVital]);

  const fetchPatientVitals = async () => {
    if (!patientId) return;

    setLoading(true);
    try {
      // Fetch vitals for the selected patient from Firebase
      const vitalsData = await firebaseDoctorPatientsService.getPatientVitals(patientId);
      console.log(`Fetched ${vitalsData.length} vital records for patient ${patientId}`);
      setPatientVitals(vitalsData || []);
    } catch (error) {
      console.error('Error fetching patient vitals from Firebase:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleVitalChange = (vitalType) => {
    setSelectedVital(vitalType);
  };

  const handleTimeRangeChange = (range) => {
    setTimeRange(range);
  };

  const openVitalDetails = (vital) => {
    setSelectedVitalRecord(vital);
    setModalVisible(true);
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return 'Unknown date';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Get vital value based on type
  const getVitalValue = (vital) => {
    if (!vital) return 'N/A';

    if (vital.vitalType === 'bloodPressure') {
      return `${vital.values.systolic}/${vital.values.diastolic} mmHg`;
    } else if (vital.vitalType === 'heartRate') {
      return `${vital.values.value} bpm`;
    } else if (vital.vitalType === 'bloodGlucose') {
      return `${vital.values.value} mg/dL`;
    } else if (vital.vitalType === 'weight') {
      return `${vital.values.value} kg`;
    }
    return 'N/A';
  };

  // Get vital type name
  const getVitalTypeName = (vital) => {
    if (!vital) return 'Unknown';

    switch (vital.vitalType) {
      case 'bloodPressure': return 'Blood Pressure';
      case 'heartRate': return 'Heart Rate';
      case 'bloodGlucose': return 'Blood Glucose';
      case 'weight': return 'Weight';
      default: return 'Unknown';
    }
  };

  // Get icon for vital type
  const getVitalIcon = (vitalType) => {
    switch (vitalType) {
      case 'bloodPressure': return <IoFitness />;
      case 'heartRate': return <IoHeart />;
      case 'bloodGlucose': return <IoWater />;
      case 'weight': return <IoBody />;
      default: return <IoPulse />;
    }
  };

  // Get color for vital type
  const getVitalColor = (vitalType) => {
    switch (vitalType) {
      case 'bloodPressure': return '#4285F4'; // Blue
      case 'heartRate': return '#EA4335';     // Red
      case 'bloodGlucose': return '#FBBC05';  // Yellow/Orange
      case 'weight': return '#34A853';        // Green
      default: return '#4285F4';
    }
  };

  // Get Y-axis suffix for charts
  const getYAxisSuffix = () => {
    switch (selectedVital) {
      case 'bloodPressure': return ' mmHg';
      case 'heartRate': return ' bpm';
      case 'bloodGlucose': return ' mg/dL';
      case 'weight': return ' kg';
      default: return '';
    }
  };

  // Filter vitals by selected type
  const getFilteredVitals = () => {
    return patientVitals.filter(item => item.vitalType === selectedVital)
      .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
  };

  // Get chart data
  const getChartData = () => {
    const filteredVitals = getFilteredVitals();

    // Sort by date (oldest to newest for charts)
    filteredVitals.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));

    // Limit to last 7 days for week view or 30 days for month view
    const daysToShow = timeRange === 'week' ? 7 : 30;
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToShow);

    const recentVitals = filteredVitals.filter(v => new Date(v.timestamp) >= cutoffDate);

    // Prepare data for chart
    const labels = recentVitals.map(v => {
      const date = new Date(v.timestamp);
      return `${date.getMonth() + 1}/${date.getDate()}`;
    });

    // Get the appropriate color based on vital type
    const vitalColor = getVitalColor(selectedVital);

    const datasets = [{
      label: selectedVital === 'bloodPressure' ? 'Systolic' : getVitalTypeName({vitalType: selectedVital}),
      data: recentVitals.map(v => {
        if (selectedVital === 'bloodPressure') {
          return parseInt(v.values.systolic);
        } else {
          return parseInt(v.values.value);
        }
      }),
      borderColor: vitalColor,
      backgroundColor: `${vitalColor}20`,
      tension: 0.4,
      pointRadius: 4,
      pointHoverRadius: 6
    }];

    // If blood pressure, add diastolic as second dataset
    if (selectedVital === 'bloodPressure') {
      datasets.push({
        label: 'Diastolic',
        data: recentVitals.map(v => parseInt(v.values.diastolic)),
        borderColor: `${vitalColor}80`,
        backgroundColor: `${vitalColor}10`,
        tension: 0.4,
        pointRadius: 4,
        pointHoverRadius: 6
      });
    }

    return {
      labels,
      datasets
    };
  };

  // Render the vital records list
  const renderVitalsList = () => {
    const filteredVitals = getFilteredVitals();

    if (filteredVitals.length === 0) {
      return (
        <div className="emptyContainer">
          <span style={{ fontSize: '48px', color: getVitalColor(selectedVital) }}>
            {getVitalIcon(selectedVital)}
          </span>
          <p className="emptyText">No {getVitalTypeName({vitalType: selectedVital})} records found</p>
        </div>
      );
    }

    return (
      <div>
        {filteredVitals.map((item) => (
          <div
            key={item.id}
            className="vitalCard"
            style={{ borderLeftColor: getVitalColor(item.vitalType) }}
            onClick={() => openVitalDetails(item)}
          >
            <div className="vitalIconContainer" style={{ backgroundColor: getVitalColor(item.vitalType) }}>
              <span style={{ color: '#fff', fontSize: '24px' }}>{getVitalIcon(item.vitalType)}</span>
            </div>
            <div className="vitalInfo">
              <div className="vitalType">{getVitalTypeName(item)}</div>
              <div className="vitalValue">{getVitalValue(item)}</div>
              <div className="vitalDate">{formatDate(item.timestamp)}</div>
            </div>
            <IoChevronForward size={24} color="#ccc" />
          </div>
        ))}
      </div>
    );
  };

  // Render the chart section
  const renderChartSection = () => {
    const chartData = getChartData();

    if (chartData.labels.length === 0) {
      return (
        <div className="emptyChartContainer">
          <p className="emptyChartText">Not enough data to display chart</p>
        </div>
      );
    }

    const chartOptions = {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        y: {
          beginAtZero: selectedVital === 'weight',
          title: {
            display: true,
            text: getYAxisSuffix()
          }
        }
      },
      plugins: {
        legend: {
          position: 'bottom'
        }
      }
    };

    return (
      <div className="chartContainer">
        <div style={{ width: '100%', height: '220px' }}>
          <Line data={chartData} options={chartOptions} />
        </div>
      </div>
    );
  };

  // Render the detail modal
  const renderDetailModal = () => {
    if (!selectedVitalRecord || !modalVisible) return null;

    return (
      <div className="modalOverlay">
        <div className="modalContainer">
          <div className="modalHeader">
            <h3 className="modalTitle">Vital Sign Details</h3>
            <button className="closeButton" onClick={() => setModalVisible(false)}>
              <IoClose size={24} color="#333" />
            </button>
          </div>

          <div className="modalContent">
            <div 
              className="modalVitalCard" 
              style={{ borderLeftColor: getVitalColor(selectedVitalRecord.vitalType) }}
            >
              <div 
                className="vitalIconContainer" 
                style={{ backgroundColor: getVitalColor(selectedVitalRecord.vitalType) }}
              >
                <span style={{ color: '#fff', fontSize: '24px' }}>
                  {getVitalIcon(selectedVitalRecord.vitalType)}
                </span>
              </div>
              <div className="vitalInfo">
                <div className="vitalType">{getVitalTypeName(selectedVitalRecord)}</div>
                <div className="vitalValue">{getVitalValue(selectedVitalRecord)}</div>
                <div className="vitalDate">{formatDate(selectedVitalRecord.timestamp)}</div>
              </div>
            </div>

            {selectedVitalRecord.notes && (
              <div className="notesContainer">
                <div className="notesLabel">Notes:</div>
                <div className="notesText">{selectedVitalRecord.notes}</div>
              </div>
            )}

            <div className="metadataContainer">
              <div className="metadataLabel">Record Method:</div>
              <div className="metadataValue">{selectedVitalRecord.recordMethod || 'Manual'}</div>

              <div className="metadataLabel">Record Type:</div>
              <div className="metadataValue">{selectedVitalRecord.recordType || 'Self'}</div>

              <div className="metadataLabel">Record ID:</div>
              <div className="metadataValue">{selectedVitalRecord.id}</div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="container">
      <div className="header">
        <h2 className="headerTitle">
          {patientName ? `${patientName}'s Vitals` : 'Patient Vitals'}
        </h2>
        <p className="headerSubtitle">
          Monitor your patient's health metrics
        </p>
      </div>

      {/* Vital Type Selector */}
      <div className="selectorContainer">
        <div 
          className={`selectorButton ${selectedVital === 'heartRate' ? 'selectedButton' : ''}`}
          style={selectedVital === 'heartRate' ? { borderColor: '#EA4335', backgroundColor: '#FFEBEE' } : {}}
          onClick={() => handleVitalChange('heartRate')}
        >
          <IoHeart
            size={20}
            color={selectedVital === 'heartRate' ? '#EA4335' : '#888'}
          />
          <span 
            className={`selectorText ${selectedVital === 'heartRate' ? 'selectedText' : ''}`}
            style={selectedVital === 'heartRate' ? { color: '#EA4335' } : {}}
          >
            Heart Rate
          </span>
        </div>

        <div
          className={`selectorButton ${selectedVital === 'bloodPressure' ? 'selectedButton' : ''}`}
          style={selectedVital === 'bloodPressure' ? { borderColor: '#4285F4', backgroundColor: '#E3F2FD' } : {}}
          onClick={() => handleVitalChange('bloodPressure')}
        >
          <IoFitness
            size={20}
            color={selectedVital === 'bloodPressure' ? '#4285F4' : '#888'}
          />
          <span 
            className={`selectorText ${selectedVital === 'bloodPressure' ? 'selectedText' : ''}`}
            style={selectedVital === 'bloodPressure' ? { color: '#4285F4' } : {}}
          >
            Blood Pressure
          </span>
        </div>

        <div
          className={`selectorButton ${selectedVital === 'bloodGlucose' ? 'selectedButton' : ''}`}
          style={selectedVital === 'bloodGlucose' ? { borderColor: '#FBBC05', backgroundColor: '#FFF8E1' } : {}}
          onClick={() => handleVitalChange('bloodGlucose')}
        >
          <IoWater
            size={20}
            color={selectedVital === 'bloodGlucose' ? '#FBBC05' : '#888'}
          />
          <span 
            className={`selectorText ${selectedVital === 'bloodGlucose' ? 'selectedText' : ''}`}
            style={selectedVital === 'bloodGlucose' ? { color: '#FBBC05' } : {}}
          >
            Blood Glucose
          </span>
        </div>

        <div
          className={`selectorButton ${selectedVital === 'weight' ? 'selectedButton' : ''}`}
          style={selectedVital === 'weight' ? { borderColor: '#34A853', backgroundColor: '#E8F5E9' } : {}}
          onClick={() => handleVitalChange('weight')}
        >
          <IoBody
            size={20}
            color={selectedVital === 'weight' ? '#34A853' : '#888'}
          />
          <span 
            className={`selectorText ${selectedVital === 'weight' ? 'selectedText' : ''}`}
            style={selectedVital === 'weight' ? { color: '#34A853' } : {}}
          >
            Weight
          </span>
        </div>
      </div>

      {/* Time Range Selector for Chart */}
      <div className="timeRangeContainer">
        <span className="timeRangeLabel">Time Range:</span>
        <div className="timeRangeButtons">
          <div
            className={`timeRangeButton ${timeRange === 'week' ? 'selectedTimeRange' : ''}`}
            onClick={() => handleTimeRangeChange('week')}
          >
            <span className={`timeRangeText ${timeRange === 'week' ? 'selectedTimeRangeText' : ''}`}>
              Week
            </span>
          </div>

          <div
            className={`timeRangeButton ${timeRange === 'month' ? 'selectedTimeRange' : ''}`}
            onClick={() => handleTimeRangeChange('month')}
          >
            <span className={`timeRangeText ${timeRange === 'month' ? 'selectedTimeRangeText' : ''}`}>
              Month
            </span>
          </div>
        </div>
      </div>

      {loading ? (
        <div className="loadingContainer">
          <div className="spinner" style={{ borderTopColor: doctorColors.primary }}></div>
          <p className="loadingText">Loading patient vitals...</p>
        </div>
      ) : (
        <div className="content">
          {/* Chart Section */}
          <div className="section">
            <h3 className="sectionTitle">
              {getVitalTypeName({vitalType: selectedVital})} Trends
            </h3>
            {renderChartSection()}
          </div>

          {/* Records Section */}
          <div className="section">
            <h3 className="sectionTitle">
              {getVitalTypeName({vitalType: selectedVital})} Records
            </h3>
            {renderVitalsList()}
          </div>
        </div>
      )}

      {renderDetailModal()}
    </div>
  );
};

export default PatientVitalsViewer;
