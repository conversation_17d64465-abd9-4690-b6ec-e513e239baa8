.container {
  padding: 20px;
  background-color: #F5F7FA;
  min-height: 100vh;
}

.title {
  font-size: 28px;
  font-weight: bold;
  color: #2C3E50;
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #E0E6ED;
}

.header-actions {
  display: flex;
  align-items: center;
}

.view-mode-buttons {
  display: flex;
  align-items: center;
}

.header-action-button {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-radius: 8px;
  margin-left: 8px;
  background-color: #F5F7FA;
  border: 1px solid #E0E6ED;
  cursor: pointer;
}

.header-action-icon {
  margin-right: 6px;
}

.header-action-text {
  font-weight: 600;
  color: #2C3E50;
}

.search-container {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.search-box {
  display: flex;
  align-items: center;
  flex: 1;
  border: 1px solid #E0E6ED;
  border-radius: 8px;
  padding: 12px;
  background-color: #FFFFFF;
}

.search-icon {
  margin-right: 12px;
}

.search-input {
  flex: 1;
  font-size: 16px;
  color: #2C3E50;
  border: none;
  outline: none;
  background: transparent;
}

.filter-button {
  display: flex;
  align-items: center;
  margin-left: 12px;
  padding: 10px 16px;
  background-color: #3498DB;
  border-radius: 8px;
  border: none;
  color: white;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.filter-button-text {
  color: #FFFFFF;
  font-size: 16px;
  font-weight: 600;
}

.filter-badge {
  background-color: #FFFFFF;
  border-radius: 10px;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8px;
}

.filter-badge-text {
  color: #3498DB;
  font-size: 12px;
  font-weight: bold;
}

.sort-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  margin-top: 10px;
}

.sort-control-wrapper {
  display: flex;
  align-items: center;
}

.sort-label {
  font-size: 14px;
  font-weight: 600;
  margin-right: 8px;
  color: #2C3E50;
}

.loading-indicator {
  width: 20px;
  height: 20px;
  border: 2px solid #4CAF50;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s linear infinite;
  margin-left: 12px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.result-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.result-count {
  color: #7F8C8D;
  font-size: 14px;
}

.selected-count {
  color: #3498DB;
  font-size: 14px;
  font-weight: bold;
}

.card-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 16px;
  padding-bottom: 20px;
}

@media (max-width: 768px) {
  .card-list {
    grid-template-columns: 1fr;
  }
}

.card {
  background-color: #FFFFFF;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: relative;
}

.selected-card {
  border: 2px solid #3498DB;
}

.checkbox-container {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 10;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 20px;
  padding: 5px;
  cursor: pointer;
}

.checkbox {
  width: 24px;
  height: 24px;
  border-radius: 12px;
  border: 2px solid #3498DB;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #FFFFFF;
}

.checkbox-selected {
  background-color: #3498DB;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background-color: #FAFBFC;
  cursor: pointer;
}

.profile-section {
  display: flex;
  align-items: center;
  flex: 1;
}

.profile-picture {
  width: 50px;
  height: 50px;
  border-radius: 25px;
  border-width: 2px;
  border-style: solid;
  background-color: #F5F7FA;
  object-fit: cover;
}

.name-section {
  margin-left: 12px;
  flex: 1;
}

.user-name {
  font-size: 18px;
  font-weight: bold;
  color: #2C3E50;
  margin: 0;
}

.user-email {
  font-size: 14px;
  color: #7F8C8D;
  margin: 0 0 4px 0;
}

.status-container {
  display: flex;
  align-items: center;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 4px;
  margin-right: 6px;
}

.status-text {
  font-size: 12px;
  color: #7F8C8D;
}

.role-badge {
  padding: 6px 12px;
  border-radius: 16px;
  margin-left: 8px;
}

.role-badge-text {
  color: #FFFFFF;
  font-size: 12px;
  font-weight: bold;
}

.card-divider {
  height: 1px;
  background-color: #E0E6ED;
}

.card-content {
  padding: 16px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
}

.info-column {
  flex: 1;
}

.info-label {
  font-size: 12px;
  color: #95A5A6;
  margin-bottom: 4px;
  display: block;
}

.info-value {
  font-size: 14px;
  color: #2C3E50;
  font-weight: 500;
  display: block;
}

.card-actions {
  display: flex;
  background-color: #FAFBFC;
  flex-wrap: wrap;
}

.action-button {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px;
  border-top: 1px solid #E0E6ED;
  min-width: 80px;
  background: none;
  border: none;
  cursor: pointer;
}

.action-icon {
  margin-right: 6px;
}

.action-button-text {
  font-weight: 600;
}

.view-button, .edit-button, .delete-button {
  border-right: 1px solid #E0E6ED;
}

/* Modal styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s, visibility 0.3s;
}

.modal-overlay.visible {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background-color: #FFFFFF;
  border-radius: 12px;
  padding: 24px;
  width: 95%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.bulk-modal {
  max-width: 400px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #E0E6ED;
}

.modal-title {
  font-size: 24px;
  font-weight: bold;
  color: #2C3E50;
  margin: 0;
}

.close-button {
  padding: 8px;
  background: none;
  border: none;
  cursor: pointer;
}

.modal-body {
  max-height: calc(90vh - 100px);
  overflow-y: auto;
}

.form-row {
  display: flex;
  margin-bottom: 20px;
  gap: 16px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group-half {
  flex: 1;
}

.form-label {
  font-size: 14px;
  font-weight: 600;
  color: #2C3E50;
  margin-bottom: 8px;
  display: block;
}

.required-star {
  color: #E74C3C;
  font-weight: bold;
}

.input-container {
  display: flex;
  align-items: center;
  border: 1px solid #E0E6ED;
  border-radius: 8px;
  padding: 12px;
  background-color: #FFFFFF;
}

.input-container.disabled {
  background-color: #F5F7FA;
}

.input-icon {
  margin-right: 12px;
}

.form-input {
  flex: 1;
  font-size: 16px;
  color: #2C3E50;
  border: none;
  background: transparent;
  outline: none;
}

.input-container.disabled .form-input {
  color: #7F8C8D;
}

.role-options {
  display: flex;
  flex-wrap: wrap;
  margin-top: 8px;
  gap: 10px;
}

.role-option {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  border-radius: 8px;
  border: none;
  cursor: pointer;
}

.role-option-selected {
  border: 2px solid #FFFFFF;
}

.role-option-text {
  color: #FFFFFF;
  font-weight: bold;
}

.role-icon {
  margin-right: 8px;
}

.submit-button {
  display: flex;
  padding: 16px;
  background-color: #4CAF50;
  border-radius: 8px;
  align-items: center;
  justify-content: center;
  margin-top: 24px;
  border: none;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.submit-button-text {
  color: #FFFFFF;
  font-size: 18px;
  font-weight: bold;
}

/* User details modal styles */
.user-details-header {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #E0E6ED;
}

.details-profile-picture {
  width: 80px;
  height: 80px;
  border-radius: 40px;
  border: 3px solid #E0E6ED;
  object-fit: cover;
}

.user-details-info {
  margin-left: 16px;
  flex: 1;
}

.details-name {
  font-size: 22px;
  font-weight: bold;
  color: #2C3E50;
  margin: 0 0 4px 0;
}

.details-email {
  font-size: 16px;
  color: #7F8C8D;
  margin: 0 0 12px 0;
}

.details-badge-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.details-badge {
  padding: 6px 12px;
  border-radius: 16px;
}

.details-badge-text {
  color: #FFFFFF;
  font-size: 12px;
  font-weight: bold;
}

.details-section {
  margin-bottom: 24px;
}

.details-section-title {
  font-size: 18px;
  font-weight: bold;
  color: #2C3E50;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #E0E6ED;
}

.details-row {
  margin-bottom: 12px;
}

.details-item {
  margin-bottom: 8px;
}

.details-label {
  font-size: 14px;
  color: #95A5A6;
  margin-bottom: 4px;
  display: block;
}

.details-value {
  font-size: 16px;
  color: #2C3E50;
  font-weight: 500;
}

.details-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 24px;
  gap: 16px;
}

.details-action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px 16px;
  border-radius: 8px;
  flex: 1;
  border: none;
  cursor: pointer;
}

.edit-action-button {
  background-color: #4285F4;
}

.ban-action-button {
  background-color: #FF9800;
}

.unban-action-button {
  background-color: #4CAF50;
}

.details-action-button-text {
  color: #FFFFFF;
  font-weight: bold;
  margin-left: 8px;
}

/* Bulk action modal styles */
.bulk-action-content {
  padding: 16px;
}

.bulk-action-text {
  font-size: 18px;
  font-weight: bold;
  color: #2C3E50;
  margin-bottom: 24px;
  text-align: center;
}

.bulk-action-buttons {
  display: flex;
  justify-content: space-between;
  gap: 16px;
}

.bulk-action-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 16px;
  border-radius: 8px;
  flex: 1;
  border: none;
  cursor: pointer;
}

.bulk-action-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.bulk-delete-button {
  background-color: #F44336;
}

.bulk-ban-button {
  background-color: #FF9800;
}

.bulk-unban-button {
  background-color: #4CAF50;
}

.bulk-action-button-text {
  color: #FFFFFF;
  font-weight: bold;
  margin-top: 8px;
}

/* Loading and error states */
.loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(76, 175, 80, 0.3);
  border-radius: 50%;
  border-top-color: #4CAF50;
  animation: spin 1s linear infinite;
}

.loading-text {
  margin-top: 20px;
  font-size: 18px;
  font-weight: bold;
  color: #2C3E50;
}

.error-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
  padding: 24px;
}

.error-text {
  margin-top: 20px;
  font-size: 18px;
  font-weight: bold;
  color: #E74C3C;
  text-align: center;
}

.retry-button {
  margin-top: 20px;
  padding: 12px 24px;
  background-color: #3498DB;
  border-radius: 8px;
  border: none;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.retry-button-text {
  color: #FFFFFF;
  font-size: 16px;
  font-weight: bold;
}

/* Empty state */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  grid-column: 1 / -1;
}

.empty-text {
  font-size: 18px;
  font-weight: bold;
  color: #7F8C8D;
  margin-top: 16px;
}

.empty-subtext {
  font-size: 14px;
  color: #95A5A6;
  margin-top: 8px;
  text-align: center;
}
