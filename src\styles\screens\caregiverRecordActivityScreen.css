.caregiver-record-activity-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background-color: #fff;
  border-bottom: 1px solid #e0e0e0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.back-button {
  padding: 8px;
  background: none;
  border: none;
  color: #4a90e2;
  font-size: 16px;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.back-button:hover {
  background-color: #f0f0f0;
}

.header-title {
  font-size: 18px;
  font-weight: bold;
  color: #212121;
  margin: 0;
}

.header-right {
  width: 40px;
}

.scroll-view {
  padding-bottom: 24px;
}

.patient-info-container {
  background-color: #fff;
  padding: 16px;
  margin-bottom: 16px;
  border-bottom: 1px solid #e0e0e0;
}

.patient-name {
  font-size: 16px;
  font-weight: 500;
  color: #424242;
  margin: 0;
}

.activity-type-container {
  padding: 16px;
  background-color: #fff;
  margin-bottom: 16px;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #212121;
  margin: 0 0 12px 0;
}

.activity-type-scroll {
  display: flex;
  gap: 12px;
  overflow-x: auto;
  padding-bottom: 8px;
}

.activity-type-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 12px;
  border-radius: 8px;
  background-color: #f5f5f5;
  border: none;
  min-width: 100px;
  cursor: pointer;
  transition: all 0.2s;
}

.activity-type-button:hover {
  background-color: #e8e8e8;
}

.activity-type-button.active {
  background-color: #4a90e2;
  color: #fff;
}

.activity-icon {
  font-size: 24px;
  margin-bottom: 4px;
}

.activity-label {
  font-size: 12px;
  color: #424242;
}

.activity-type-button.active .activity-label {
  color: #fff;
}

.form-container {
  padding: 16px;
  background-color: #fff;
  margin-bottom: 24px;
}

.input-container {
  margin-bottom: 16px;
}

.input-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #424242;
  margin-bottom: 8px;
}

.input {
  width: 100%;
  background-color: #f5f5f5;
  border: none;
  border-radius: 8px;
  padding: 12px;
  font-size: 16px;
  box-sizing: border-box;
  transition: background-color 0.2s;
}

.input:focus {
  outline: none;
  background-color: #e8e8e8;
}

.notes-input {
  min-height: 100px;
  resize: vertical;
  font-family: inherit;
}

.completed-container {
  margin-bottom: 16px;
}

.completed-buttons {
  display: flex;
  gap: 8px;
}

.completed-button {
  flex: 1;
  padding: 12px;
  border-radius: 8px;
  background-color: #f5f5f5;
  border: none;
  font-size: 14px;
  font-weight: 500;
  color: #424242;
  cursor: pointer;
  transition: all 0.2s;
}

.completed-button:hover {
  background-color: #e8e8e8;
}

.completed-button.active {
  background-color: #4a90e2;
  color: #fff;
}

.submit-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  width: 100%;
  padding: 16px;
  border-radius: 8px;
  background-color: #4a90e2;
  border: none;
  color: #fff;
  font-weight: bold;
  font-size: 16px;
  cursor: pointer;
  margin-top: 16px;
  transition: background-color 0.2s;
}

.submit-button:hover:not(:disabled) {
  background-color: #357abd;
}

.submit-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.loading-spinner {
  font-size: 20px;
  animation: spin 1s linear infinite;
}

.save-icon {
  font-size: 20px;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Responsive design */
@media (max-width: 768px) {
  .activity-type-scroll {
    flex-wrap: wrap;
  }
  
  .activity-type-button {
    min-width: 80px;
  }
  
  .completed-buttons {
    flex-direction: column;
  }
}
