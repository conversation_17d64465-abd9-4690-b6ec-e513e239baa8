.container {
  flex: 1;
  background-color: #f5f5f5;
  min-height: 100vh;
  overflow-y: auto;
}

.header {
  background-color: #4285F4;
  padding: 20px;
  padding-top: 40px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;
}

.header-title {
  font-size: 24px;
  font-weight: bold;
  color: #fff;
  margin: 0;
}

.header-subtitle {
  font-size: 14px;
  color: #E1F5FE;
  margin-top: 5px;
  margin-bottom: 0;
}

.history-button {
  display: flex;
  align-items: center;
  background-color: rgba(255,255,255,0.2);
  padding: 8px 12px;
  border-radius: 20px;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s;
}

.history-button:hover {
  background-color: rgba(255,255,255,0.3);
}

.history-button-text {
  color: #fff;
  margin-left: 5px;
  font-size: 14px;
  font-weight: 500;
}

.section {
  background-color: #fff;
  margin: 10px;
  padding: 15px;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 15px;
  color: #333;
  margin-top: 0;
}

.vital-type-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 10px;
}

.vital-type-button {
  display: flex;
  align-items: center;
  width: 48%;
  padding: 15px;
  border-radius: 10px;
  background-color: #f5f5f5;
  border: 1px solid #e0e0e0;
  cursor: pointer;
  transition: all 0.2s;
}

.vital-type-button:hover {
  background-color: #e8e8e8;
}

.vital-type-button.selected {
  background-color: #4285F4;
  border-color: #4285F4;
  color: white;
}

.vital-type-text {
  margin-left: 10px;
  font-size: 16px;
  color: #333;
}

.vital-type-button.selected .vital-type-text {
  color: #fff;
}

.voice-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 15px;
  border-radius: 10px;
  background-color: #f5f5f5;
  border: 1px solid #e0e0e0;
  margin-bottom: 15px;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
}

.voice-button:hover {
  background-color: #e8e8e8;
}

.voice-button.active {
  background-color: #FFEBEE;
  border-color: #EA4335;
}

.voice-button-text {
  margin-left: 10px;
  font-size: 16px;
  color: #4285F4;
}

.voice-button-text.active {
  color: #EA4335;
}

.recording-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #EA4335;
  margin-left: 10px;
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.voice-instructions {
  margin-bottom: 15px;
  background-color: #F9F9F9;
  padding: 12px;
  border-radius: 8px;
}

.voice-instructions-title {
  font-size: 14px;
  font-weight: bold;
  color: #616161;
  margin-bottom: 5px;
  margin-top: 0;
}

.voice-instructions-text {
  font-size: 13px;
  color: #757575;
  line-height: 18px;
  margin: 0;
}

.recording-duration-container {
  background-color: #FFEBEE;
  padding: 8px;
  border-radius: 8px;
  margin-bottom: 10px;
  text-align: center;
}

.recording-duration-text {
  color: #E53935;
  font-weight: bold;
  font-size: 14px;
  margin: 0;
}

.partial-results-box {
  background-color: #F5F5F5;
  padding: 10px;
  border-radius: 8px;
  margin-bottom: 10px;
  border: 1px dashed #E0E0E0;
}

.partial-results-text {
  font-size: 14px;
  color: #757575;
  font-style: italic;
  margin: 0;
}

.result-box {
  background-color: #E8F5E9;
  padding: 10px;
  border-radius: 8px;
  margin-bottom: 10px;
}

.voice-result-text {
  font-size: 14px;
  color: #388E3C;
  margin: 0;
}

.voice-error-text {
  color: #D32F2F;
  margin-bottom: 10px;
  font-size: 14px;
}

.input-group {
  margin-bottom: 15px;
}

.input-label {
  font-size: 14px;
  margin-bottom: 5px;
  color: #666;
  display: block;
}

.input {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 12px;
  font-size: 16px;
  background-color: #fff;
  width: 100%;
  box-sizing: border-box;
}

.input:focus {
  outline: none;
  border-color: #4285F4;
  box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.2);
}

.notes-input {
  min-height: 100px;
  resize: vertical;
  font-family: inherit;
}

.blood-pressure-inputs {
  display: flex;
  justify-content: space-between;
  gap: 15px;
}

.blood-pressure-inputs .input-group {
  flex: 1;
}

.submit-button {
  background-color: #4285F4;
  padding: 15px;
  border-radius: 10px;
  margin: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s;
  width: calc(100% - 20px);
}

.submit-button:hover {
  background-color: #3367D6;
}

.submit-button.submitting {
  background-color: #9E9E9E;
  cursor: not-allowed;
}

.submit-button.success {
  background-color: #34A853;
}

.submit-button-text {
  color: #fff;
  font-size: 18px;
  font-weight: bold;
  margin-left: 10px;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #ffffff;
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.icon {
  font-size: 20px;
}

.icon.heart {
  color: #E91E63;
}

.icon.fitness {
  color: #4285F4;
}

.icon.water {
  color: #FBBC05;
}

.icon.body {
  color: #34A853;
}

.vital-type-button.selected .icon {
  color: #fff;
}

.icon.mic {
  font-size: 30px;
  color: #4285F4;
}

.icon.mic.active {
  color: #EA4335;
}

/* Responsive design */
@media (max-width: 768px) {
  .vital-type-button {
    width: 100%;
  }
  
  .blood-pressure-inputs {
    flex-direction: column;
    gap: 0;
  }
  
  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .history-button {
    align-self: flex-end;
  }
}
