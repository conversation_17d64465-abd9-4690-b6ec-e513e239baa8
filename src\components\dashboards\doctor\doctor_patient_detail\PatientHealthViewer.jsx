import React, { useState, useEffect } from 'react';
import { ROLE_COLORS } from '../../../config/theme';
import PatientVitalsViewer from './PatientVitalsViewer';
import PatientSymptomsViewer from './PatientSymptomsViewer';
import PatientMedicationsViewer from './PatientMedicationsViewer';
import PatientMedicalNotes from './PatientMedicalNotes';
import PatientDetailCard from './PatientDetailCard';
import { localMedicalNotesService } from '../../../services/localMedicalNotesService';
import { firebaseDoctorPatientsService } from '../../../services/firebaseDoctorPatientsService';
import '../../../../styles/dashboards/doctor/doctor_patient_detail/patientHealthViewer.css';

// Import icons
import { IoIosPulse, IoIosDocumentText, IoIosMedkit, IoIosArrowBack, 
  IoIosPeople, IoIosAddCircle, IoIosClose, IoIosChevronForward } from 'react-icons/io';

const PatientHealthViewer = () => {
  const [patients, setPatients] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedPatient, setSelectedPatient] = useState(null);
  const [patientDetails, setPatientDetails] = useState(null);
  const [loadingDetails, setLoadingDetails] = useState(false);
  const [activeTab, setActiveTab] = useState('vitals');
  const [activeCategory, setActiveCategory] = useState('vitals');
  const [showNotes, setShowNotes] = useState(false);
  const [notesModalVisible, setNotesModalVisible] = useState(false);
  const [medicalNote, setMedicalNote] = useState('');
  const [submitting, setSubmitting] = useState(false);
  const doctorColors = ROLE_COLORS.doctor;

  // Fetch doctor's patients from Firebase
  useEffect(() => {
    fetchDoctorPatients();
  }, []);

  const fetchDoctorPatients = async () => {
    setLoading(true);
    try {
      const doctorPatients = await firebaseDoctorPatientsService.getDoctorPatients();
      setPatients(doctorPatients);
    } catch (error) {
      console.error('Error fetching doctor patients:', error);
      alert('Failed to load patients. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleSelectPatient = async (patient) => {
    setSelectedPatient(patient);
    setActiveTab('vitals');
    setActiveCategory('vitals');

    // Fetch detailed patient information
    setLoadingDetails(true);
    try {
      const details = await firebaseDoctorPatientsService.getPatientDetails(patient.id);
      setPatientDetails(details);
    } catch (error) {
      console.error('Error fetching patient details:', error);
      // If we can't get details, use the basic patient info
      setPatientDetails(patient);
    } finally {
      setLoadingDetails(false);
    }
  };

  const handleAddNotes = () => {
    setMedicalNote('');
    setNotesModalVisible(true);
  };

  const toggleNotes = () => {
    setShowNotes(!showNotes);
  };

  const handleSaveNotes = async () => {
    if (!medicalNote.trim()) {
      alert('Please enter some notes before saving');
      return;
    }

    setSubmitting(true);
    try {
      const noteData = {
        patientId: selectedPatient.id,
        content: medicalNote,
        type: activeTab === 'vitals' ? 'vitals' : 'symptoms',
        category: activeTab === 'vitals' ? 'Vital Signs' : 'Symptoms',
        doctorId: 'doctor-user', // In a real app, this would be the logged-in doctor's ID
        patientName: `${selectedPatient.firstName} ${selectedPatient.lastName}`,
        timestamp: new Date().toISOString()
      };

      await localMedicalNotesService.saveMedicalNote(noteData);
      alert(`Medical notes for ${activeTab === 'vitals' ? 'Vital Signs' : 'Symptoms'} saved successfully`);
      setNotesModalVisible(false);
    } catch (error) {
      console.error('Error saving medical notes:', error);
      alert('Failed to save medical notes');
    } finally {
      setSubmitting(false);
    }
  };

  // Render a patient card
  const renderPatientCard = (item) => (
    <div 
      className="patientCard"
      onClick={() => handleSelectPatient(item)}
      key={item.id}
    >
      <div className="patientAvatarContainer">
        <span className="patientAvatarText">
          {item.firstName.charAt(0)}{item.lastName.charAt(0)}
        </span>
      </div>
      <div className="patientInfo">
        <div className="patientName">{item.firstName} {item.lastName}</div>
        <div className="patientEmail">{item.email}</div>
        <div className="patientPhone">{item.phone || 'No phone number'}</div>
      </div>
      <div className="patientActions">
        <button
          className="actionButton"
          style={{ backgroundColor: '#4CAF50' }}
          onClick={(e) => {
            e.stopPropagation();
            handleSelectPatient(item);
            setActiveCategory('vitals');
          }}
        >
          <IoIosPulse size={16} color="#fff" />
          <span className="actionButtonText">Vital Signs</span>
        </button>

        <button
          className="actionButton"
          style={{ backgroundColor: '#2196F3' }}
          onClick={(e) => {
            e.stopPropagation();
            handleSelectPatient(item);
            setActiveCategory('symptoms');
          }}
        >
          <IoIosDocumentText size={16} color="#fff" />
          <span className="actionButtonText">Symptoms</span>
        </button>

        <button
          className="actionButton"
          style={{ backgroundColor: '#FF9800' }}
          onClick={(e) => {
            e.stopPropagation();
            handleSelectPatient(item);
            setActiveCategory('medications');
          }}
        >
          <IoIosMedkit size={16} color="#fff" />
          <span className="actionButtonText">Medicines</span>
        </button>
      </div>
    </div>
  );

  // Render the patient list
  const renderPatientList = () => {
    if (loading) {
      return (
        <div className="loadingContainer">
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
          <div className="loadingText">Loading patients...</div>
        </div>
      );
    }

    if (patients.length === 0) {
      return (
        <div className="emptyContainer">
          <IoIosPeople size={48} color="#ccc" />
          <div className="emptyText">No patients found</div>
          <div className="emptySubtext">You don't have any patients linked to your account yet.</div>
        </div>
      );
    }

    return (
      <div className="patientList">
        {patients.map(patient => renderPatientCard(patient))}
      </div>
    );
  };

  // Render the patient detail view
  const renderPatientDetail = () => {
    if (!selectedPatient) {
      return null;
    }

    return (
      <div className="contentContainer">
        <div className="patientHeader">
          <div className="patientHeaderInfo">
            <div className="patientDetailAvatar">
              <span className="patientDetailAvatarText">
                {selectedPatient.firstName.charAt(0)}{selectedPatient.lastName.charAt(0)}
              </span>
            </div>
            <div>
              <div className="patientDetailName">{selectedPatient.firstName} {selectedPatient.lastName}</div>
              <div className="patientDetailInfo">{selectedPatient.email}</div>
              <div className="patientDetailInfo">{selectedPatient.phone || 'No phone number'}</div>
            </div>
          </div>
        </div>

        {/* Patient Details Card */}
        {loadingDetails ? (
          <div className="loadingContainer">
            <div className="spinner-border text-primary" role="status">
              <span className="visually-hidden">Loading...</span>
            </div>
            <div className="loadingText">Loading patient details...</div>
          </div>
        ) : (
          <PatientDetailCard patient={patientDetails || selectedPatient} />
        )}

        <div className="tabsContainer">
          <div
            className={`tab ${activeTab === 'vitals' ? 'activeTab' : ''}`}
            onClick={() => setActiveTab('vitals')}
          >
            <IoIosPulse
              size={20}
              color={activeTab === 'vitals' ? '#fff' : doctorColors.primary}
            />
            <span className={`tabText ${activeTab === 'vitals' ? 'activeTabText' : ''}`}>
              Vital Signs
            </span>
          </div>

          <div
            className={`tab ${activeTab === 'symptoms' ? 'activeTab' : ''}`}
            onClick={() => setActiveTab('symptoms')}
          >
            <IoIosDocumentText
              size={20}
              color={activeTab === 'symptoms' ? '#fff' : doctorColors.primary}
            />
            <span className={`tabText ${activeTab === 'symptoms' ? 'activeTabText' : ''}`}>
              Symptoms
            </span>
          </div>

          <div
            className={`tab ${activeTab === 'medications' ? 'activeTab' : ''}`}
            onClick={() => setActiveTab('medications')}
          >
            <IoIosMedkit
              size={20}
              color={activeTab === 'medications' ? '#fff' : doctorColors.primary}
            />
            <span className={`tabText ${activeTab === 'medications' ? 'activeTabText' : ''}`}>
              Medicines
            </span>
          </div>
        </div>

        <div className="contentActions">
          <button
            className="toggleNotesButton"
            onClick={toggleNotes}
          >
            {showNotes ? <IoIosPulse size={20} color={doctorColors.primary} /> : 
                         <IoIosDocumentText size={20} color={doctorColors.primary} />}
            <span className="toggleNotesText">
              {showNotes ? 'Show Health Data' : 'Show Notes'}
            </span>
          </button>
        </div>

        {!showNotes ? (
          // Show health data based on active tab
          activeTab === 'vitals' ? (
            <>
              <PatientVitalsViewer
                patientId={selectedPatient.id}
                patientName={`${selectedPatient.firstName} ${selectedPatient.lastName}`}
              />
              <div className="addNotesButtonContainer">
                <button
                  className="addNotesButton"
                  onClick={handleAddNotes}
                >
                  <IoIosAddCircle size={20} color="#fff" />
                  <span className="addNotesButtonText">Add Notes for Vitals</span>
                </button>
              </div>
            </>
          ) : activeTab === 'symptoms' ? (
            <>
              <PatientSymptomsViewer
                patientId={selectedPatient.id}
                patientName={`${selectedPatient.firstName} ${selectedPatient.lastName}`}
              />
              <div className="addNotesButtonContainer">
                <button
                  className="addNotesButton"
                  onClick={handleAddNotes}
                >
                  <IoIosAddCircle size={20} color="#fff" />
                  <span className="addNotesButtonText">Add Notes for Symptoms</span>
                </button>
              </div>
            </>
          ) : (
            // Medications tab
            <PatientMedicationsViewer
              patientId={selectedPatient.id}
              patientName={`${selectedPatient.firstName} ${selectedPatient.lastName}`}
            />
          )
        ) : (
          // Show notes specific to the active tab
          <PatientMedicalNotes
            patientId={selectedPatient.id}
            patientName={`${selectedPatient.firstName} ${selectedPatient.lastName}`}
            noteType={activeTab === 'vitals' ? 'vitals' : 'symptoms'}
          />
        )}
      </div>
    );
  };

  // Main content renderer
  const renderContent = () => {
    if (selectedPatient) {
      return renderPatientDetail();
    } else {
      return renderPatientList();
    }
  };

  // Render the notes modal
  const renderNotesModal = () => {
    if (!notesModalVisible) return null;
    
    return (
      <div className="modalOverlay">
        <div className="modalContainer">
          <div className="modalHeader">
            <div className="modalTitle">
              Add Medical Notes for {activeTab === 'vitals' ? 'Vital Signs' : 'Symptoms'}
            </div>
            <button
              className="closeButton"
              onClick={() => setNotesModalVisible(false)}
            >
              <IoIosClose size={24} color="#333" />
            </button>
          </div>

          <div className="modalContent">
            <div className="modalSubtitle">
              Patient: {selectedPatient?.firstName} {selectedPatient?.lastName}
            </div>
            <div className="modalSubtitle">
              Section: <span className="highlightedText">{activeTab === 'vitals' ? 'Vital Signs' : 'Symptoms'}</span>
            </div>

            <textarea
              className="notesInput"
              value={medicalNote}
              onChange={(e) => setMedicalNote(e.target.value)}
              placeholder="Enter medical notes here..."
              rows={10}
            />

            <button
              className="saveButton"
              onClick={handleSaveNotes}
              disabled={submitting}
            >
              <span className="saveButtonText">
                {submitting ? 'Saving...' : 'Save Notes'}
              </span>
            </button>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="container">
      {selectedPatient && (
        <div className="backButtonContainer">
          <button
            className="backButton"
            onClick={() => setSelectedPatient(null)}
          >
            <IoIosArrowBack size={24} color={doctorColors.primary} />
            <span className="backButtonText">Back to Patients</span>
          </button>
        </div>
      )}
      <div className="header">
        <div className="headerTitle">Patient Health Data</div>
        <div className="headerSubtitle">
          {selectedPatient
            ? `Viewing health data for ${selectedPatient.firstName} ${selectedPatient.lastName}`
            : 'View vital signs, symptoms and medications'}
        </div>
      </div>

      <div className="scrollContainer">
        <div className="scrollContentContainer">
          {renderContent()}
        </div>
      </div>
      {renderNotesModal()}
    </div>
  );
};

export default PatientHealthViewer;
