* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Dark mode for the entire app */
.dark-app {
  background-color: var(--background-light);
  color: var(--text-dark);
}

/* Apply dark mode to html element for global CSS variables */
html.dark {
  --primary: #1e8a00;
  --primary-light: rgba(30, 138, 0, 0.7);
  --primary-dark: #117e00;
  --accent: #88a0ff;
  --accent-light: #a0b5ff;
  --accent-dark: #4a6bff;
  --text-dark: #f7fafc;
  --text-medium: #e2e8f0;
  --text-dark-nav: #2d3748;
  --text-medium-testimonials: #e2e8f0;
  --text-dark-testimonials: #f7fafc;
  --text-light: #cbd5e0;
  --background-light: #1a202c;
  --background-light-features: #1a202c;
  --background-light-testimonials: #1a202c;
  --background-white: #2d3748;
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 20px rgba(0, 0, 0, 0.2);
  --shadow-lg: 0 10px 30px rgba(0, 0, 0, 0.3);
}