@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap");

:root {
  --primary: #106b00;
  --primary-light: rgba(16, 107, 0, 0.7);
  --primary-dark: #117e00;
  --accent: #4a6bff;
  --accent-light: #88a0ff;
  --accent-dark: #3a5ad9;
  --text-dark: #2d3748;
  --text-medium: #4a5568;
  --text-dark-nav: #2d3748;
  --accent-nav: #88a0ff;
  --text-medium-testimonials: #ffffff;
  --text-dark-testimonials: #a0aec0;
  --text-light: #a0aec0;
  --background-light: rgba(16, 107, 0, 0.7);
  --background-light-features: #cbf9c5;
  --background-light-testimonials: #435065;
  --background-white: #ffffff;
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 20px rgba(0, 0, 0, 0.08);
  --shadow-lg: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.dark {
  --primary: #1e8a00;
  --primary-light: rgba(30, 138, 0, 0.7);
  --primary-dark: #117e00;
  --accent: #88a0ff;
  --accent-light: #a0b5ff;
  --accent-dark: #4a6bff;
  --text-dark: #f7fafc;
  --text-medium: #e2e8f0;
  --text-dark-nav: #2d3748;
  --text-medium-testimonials: #e2e8f0;
  --text-dark-testimonials: #f7fafc;
  --text-light: #cbd5e0;
  --background-light: #1a202c;
  --background-light-features: #1a202c;
  --background-light-testimonials: #1a202c;
  --background-white: #2d3748;
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 20px rgba(0, 0, 0, 0.2);
  --shadow-lg: 0 10px 30px rgba(0, 0, 0, 0.3);
}



.landing-page {
  max-width: 100%;
  overflow-x: hidden;
  background-image: url("../../assets/Backgrounds/pexels-pixabay-40568.jpg");
  background-size: cover;
  background-position: center;
  position: relative;
  font-family: "Poppins", sans-serif;
  color: var(--text-dark);
  line-height: 1.6;
}



.landing-page::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--background-light);
  z-index: 0;
}

.header,
main,
footer {
  position: relative;
  z-index: 1;
}

/* Header styles */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 5%;
  background-color: var(--background-white);
  box-shadow: var(--shadow-md);
  position: sticky;
  top: 0;
  z-index: 100;
}

.logo-container {
  display: flex;
  flex-direction: column;
}

.logo {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--primary);
  margin: 0;
}

.tagline {
  font-size: 0.9rem;
  color: var(--text-medium);
  margin: 0;
}

.navigation {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.nav-button {
  background: none;
  border: none;
  padding: 0.5rem 1rem;
  font-size: 1rem;
  color: var(--text-dark-nav);
  cursor: pointer;
  transition: color 0.3s;
}

.nav-button:hover {
  color: var(--accent-nav);
}

.login-button-nav {
  background-color: var(--primary);
  color: white;
  border: none;
  border-radius: 20px;
  padding: 0.5rem 1.5rem;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.3s;
  text-decoration: none;
  display: inline-block;
}

.login-button-nav:hover {
  background-color: var(--primary-dark);
}

.mobile-menu-button {
  display: none;
  flex-direction: column;
  justify-content: space-between;
  width: 30px;
  height: 21px;
  background: transparent;
  border: none;
  cursor: pointer;
}

.mobile-menu-button span {
  width: 100%;
  height: 3px;
  background-color: var(--primary);
  transition: all 0.3s;
}

.mobile-menu-button.active span:nth-child(1) {
  transform: translateY(9px) rotate(45deg);
}

.mobile-menu-button.active span:nth-child(2) {
  opacity: 0;
}

.mobile-menu-button.active span:nth-child(3) {
  transform: translateY(-9px) rotate(-45deg);
}

/* Hero section */
.hero {
  display: flex;
  padding: 6rem 5%;
  background-color: var(--background-white);
  min-height: 600px;
  border-radius: 0 0 30px 30px;
  margin-bottom: 3rem;
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-lg);
}

/* Video background */
.video-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  overflow: hidden;
}

.video-background video {
  width: 105%;
  height: 105%;
  object-fit: cover;
  position: absolute;
  top: -2.5%;
  left: -2.5%;
  transform: scale(1.05);
  transition: transform 0.5s ease-out;
  animation: subtle-zoom 20s infinite alternate ease-in-out;
}

@keyframes subtle-zoom {
  from {
    transform: scale(1.05) translate(0, 0);
  }
  to {
    transform: scale(1.15) translate(-1%, -1%);
  }
}

.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1;
}

/* Overlay plus léger en mode Light */
.light-mode .video-overlay {
  background-color: rgba(0, 0, 0, 0.3);
}

.hero-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: relative;
  z-index: 2;
  animation: slide-in 1s ease-out;
  padding-left: 2rem;
}

@keyframes slide-in {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.hero h2 {
  font-size: 3rem;
  margin-bottom: 1.5rem;
  color: #ffffff;
  line-height: 1.2;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
  font-weight: 700;
  letter-spacing: -0.5px;
}

.hero p {
  font-size: 1.2rem;
  margin-bottom: 2.5rem;
  color: #f0f0f0;
  max-width: 80%;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.7);
  line-height: 1.6;
}

/* Styles spécifiques pour le mode light */
.light-mode .hero h2,
.light-mode .hero p {
  color: #ffffff;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
}

.cta-buttons {
  display: flex;
  gap: 1.5rem;
  animation: fade-up 1.2s ease-out 0.3s backwards;
}

@keyframes fade-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.cta-button {
  padding: 1rem 2.5rem;
  font-size: 1.1rem;
  border-radius: 50px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.cta-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.7s ease;
}

.cta-button:hover::before {
  left: 100%;
}

.cta-button.primary {
  background-color: var(--primary);
  color: white;
  border: none;
}

.cta-button.primary:hover {
  background-color: var(--primary-dark);
  transform: translateY(-3px);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
}

.cta-button.secondary {
  background-color: transparent;
  color: var(--primary);
  border: 1px solid var(--primary);
}

.cta-button.secondary:hover {
  background-color: rgba(16, 107, 0, 0.1);
  transform: translateY(-3px);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.1);
}

/* Styles spécifiques pour les boutons dans la section hero avec vidéo */
.hero .cta-button.secondary {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid white;
  -webkit-backdrop-filter: blur(5px);
  backdrop-filter: blur(5px);
}

.hero .cta-button.secondary:hover {
  background-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-3px);
}

.cta-button.large {
  padding: 1.2rem 3.5rem;
  font-size: 1.2rem;
}

.hero-image {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  z-index: 2;
}

.hero-image {
  animation: fade-in-delay 1.5s ease-out 0.5s backwards;
}

@keyframes fade-in-delay {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.welcome-card {
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 2.5rem;
  width: 85%;
  max-width: 420px;
  text-align: center;
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
  animation: float 6s ease-in-out infinite;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
  100% {
    transform: translateY(0px);
  }
}

.welcome-image {
  width: 130px;
  height: 130px;
  border-radius: 65px;
  margin: 0 auto 1.5rem;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, var(--primary-light), var(--primary));
  box-shadow: 0 8px 20px rgba(16, 107, 0, 0.3);
}

.welcome-image span {
  font-size: 5rem;
  filter: drop-shadow(0 2px 5px rgba(0, 0, 0, 0.2));
}

.welcome-title, .homePage-h3-welcome {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: 1rem;
  text-align: center;
  width: 100%;
}

.welcome-text, .homePage-p-welcome {
  color: var(--text-medium);
  text-align: center;
  width: 100%;
  margin: 0 auto;
  padding: 0 10px;
}

/* Features section */
.features-section {
  padding: 5rem 5%;
  text-align: center;
  background-color: var(--background-light-features);
  border-radius: 20px;
  margin: 0 5% 2rem;
}

.features-section h2 {
  font-size: 2rem;
  margin-bottom: 3rem;
  color: var(--text-dark);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.feature-card {
  background-color: var(--background-white);
  border-radius: 12px;
  padding: 2rem;
  box-shadow: var(--shadow-md);
  transition: transform 0.3s, box-shadow 0.3s;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.feature-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  margin: 0 auto 1.5rem;
  display: flex;
  justify-content: center;
  align-items: center;
}

.feature-card h3 {
  margin-bottom: 1rem;
  color: var(--text-dark);
  font-size: 1.2rem;
  font-weight: 600;
}

.feature-card p {
  color: var(--text-medium);
}

.see-more-button {
  background-color: transparent;
  color: var(--primary);
  border: 1px solid var(--primary);
  padding: 0.8rem 2rem;
  border-radius: 4px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s;
  display: inline-block;
  text-decoration: none;
}

.see-more-button:hover {
  background-color: rgba(16, 107, 0, 0.1);
}

/* Testimonials section */
.testimonials {
  padding: 5rem 5%;
  text-align: center;
  background-color: var(--background-white);
  border-radius: 20px;
  margin: 0 5% 2rem;
}

.testimonials h2 {
  font-size: 2rem;
  margin-bottom: 3rem;
  color: var(--text-dark);
}

.testimonial-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.testimonial-card {
  background-color: var(--background-light-testimonials);
  border-radius: 12px;
  padding: 2rem;
  box-shadow: var(--shadow-md);
  text-align: left;
  transition: transform 0.3s;
}

.testimonial-card:hover {
  transform: translateY(-5px);
}

.testimonial-content {
  font-style: italic;
  margin-bottom: 1.5rem;
  color: var(--text-medium-testimonials);
}

.testimonial-author {
  font-weight: 600;
  color: var(--text-dark-testimonials);
}



/* Footer */
.footer {
  background-color: var(--background-white);
  color: var(--text-medium);
  padding: 4rem 5% 2rem;
  border-radius: 20px 20px 0 0;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.footer-section h4 {
  font-size: 1.2rem;
  margin-bottom: 1.5rem;
  color: var(--text-dark);
}

.footer-section ul {
  list-style: none;
}

.footer-section ul li {
  margin-bottom: 0.8rem;
}

.footer-section a {
  color: var(--text-medium);
  text-decoration: none;
  transition: color 0.3s;
}

.footer-section a:hover {
  color: var(--primary);
}

.footer-bottom {
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  padding-top: 1.5rem;
  text-align: center;
  font-size: 0.9rem;
  color: var(--text-light);
}

/* Theme toggle */
.theme-toggle {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: var(--primary);
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  box-shadow: var(--shadow-md);
  z-index: 100;
  border: none;
}

.theme-toggle:hover {
  background-color: var(--primary-dark);
}

/* Responsive design */
@media (max-width: 768px) {
  .hero {
    flex-direction: column;
  }

  .hero-content {
    margin-bottom: 2rem;
  }

  .hero p {
    max-width: 100%;
  }

  .navigation {
    display: none;
  }

  .navigation.mobile-menu-open {
    display: flex;
    flex-direction: column;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: var(--background-white);
    padding: 1rem;
    box-shadow: var(--shadow-md);
    z-index: 99;
  }

  .mobile-menu-button {
    display: flex;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .testimonial-cards {
    grid-template-columns: 1fr;
  }

  .cta-buttons {
    flex-direction: column;
    width: 100%;
  }

  .cta-button {
    width: 100%;
  }

  .features-section,
  .testimonials,
  .cta-section {
    margin: 0 0 2rem;
    border-radius: 0;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .hero h2 {
    font-size: 2.2rem;
  }

  .hero p {
    max-width: 90%;
  }

  .features-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .header {
    padding: 1rem 5%;
  }

  .logo {
    font-size: 1.5rem;
  }

  .hero {
    padding: 2rem 5%;
  }

  .hero h2 {
    font-size: 2rem;
  }

  .hero p {
    font-size: 1rem;
  }

  .features-section,
  .testimonials,
  .cta-section {
    padding: 3rem 5%;
  }

  .features-section h2,
  .testimonials h2,
  .cta-section h2 {
    font-size: 1.8rem;
    margin-bottom: 2rem;
  }

  .welcome-card {
    width: 100%;
  }

  .footer-content {
    grid-template-columns: 1fr;
  }

  .footer-section {
    margin-bottom: 2rem;
  }
}

/* Animation styles */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.feature-card,
.testimonial-card,
.hero-content,
.hero-image {
  animation: fadeIn 0.6s ease-out forwards;
}

.feature-card:nth-child(2) {
  animation-delay: 0.2s;
}

.feature-card:nth-child(3) {
  animation-delay: 0.4s;
}

.feature-card:nth-child(4) {
  animation-delay: 0.6s;
}

.testimonial-card:nth-child(2) {
  animation-delay: 0.2s;
}

.testimonial-card:nth-child(3) {
  animation-delay: 0.4s;
}

/* Accessibility improvements */
button:focus,
a:focus {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

.cta-button:focus,
.nav-button:focus,
.login-button:focus,
.see-more-button:focus {
  box-shadow: 0 0 0 3px rgba(16, 107, 0, 0.4);
}

