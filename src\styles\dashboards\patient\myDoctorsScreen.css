/* MyDoctorsScreen CSS */
.container {
  flex: 1;
  background-color: #f5f5f5;
  padding-top: 10px;
}

.listContent {
  padding: 16px;
  padding-top: 8px;
}

.emptyListContent {
  flex-grow: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16px;
}

.doctorCard {
  background-color: #fff;
  border-radius: 16px;
  margin-bottom: 20px;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  border: 1px solid #f0f0f0;
  transition: transform 0.2s ease;
}

.cardContent {
  padding: 0;
  cursor: pointer;
}

.cardHeader {
  display: flex;
  padding: 18px;
  align-items: center;
  position: relative;
  border-bottom: 1px solid #f5f5f5;
  background-color: #fcfcfc;
}

.avatarContainer {
  margin-right: 16px;
  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);
}

.avatar {
  width: 75px;
  height: 75px;
  border-radius: 38px;
  border: 2px solid #fff;
  object-fit: cover;
}

.avatarPlaceholder {
  width: 75px;
  height: 75px;
  border-radius: 38px;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 2px solid #fff;
}

.avatarText {
  font-size: 26px;
  font-weight: bold;
  color: #fff;
}

.doctorInfo {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.doctorName {
  font-size: 19px;
  font-weight: bold;
  margin-bottom: 5px;
  color: #333;
  letter-spacing: 0.3px;
}

.doctorSpecialty {
  font-size: 15px;
  color: rgba(170, 86, 255, 1); /* ROLE_COLORS.doctor.primary */
  margin-bottom: 8px;
  font-style: italic;
  opacity: 0.9;
}

.expandIcon {
  position: absolute;
  right: 16px;
  top: 16px;
}

.contactInfo {
  margin-top: 8px;
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 6px 10px;
  align-self: flex-start;
}

.contactItem {
  display: flex;
  align-items: center;
  margin-bottom: 2px;
}

.contactText {
  font-size: 14px;
  color: #555;
  margin-left: 8px;
  font-weight: 500;
}

.detailsContainer {
  padding: 18px;
  padding-top: 10px;
  background-color: #f9f9f9;
  border-top: 1px solid #eee;
}

.detailSection {
  margin-bottom: 16px;
  background-color: #fff;
  border-radius: 10px;
  padding: 12px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.detailTitleContainer {
  display: flex;
  align-items: center;
  margin-bottom: 6px;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 6px;
}

.detailTitle {
  font-size: 15px;
  font-weight: bold;
  color: rgba(170, 86, 255, 1); /* ROLE_COLORS.doctor.primary */
  margin-left: 8px;
  letter-spacing: 0.3px;
}

.detailText {
  font-size: 15px;
  color: #444;
  line-height: 22px;
  padding: 0 4px;
  padding-top: 2px;
}

.noDetailsContainer {
  padding: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  border-radius: 10px;
  margin-top: 5px;
}

.noDetailsText {
  font-size: 15px;
  color: #999;
  font-style: italic;
  text-align: center;
}

.actionsContainer {
  display: flex;
  flex-direction: column;
  border-top: 1px solid #eee;
  padding: 14px;
  background-color: #fff;
}

.secondaryActionsRow {
  display: flex;
  justify-content: space-between;
  margin-top: 10px;
}

.actionButton {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px 14px;
  background-color: #f8f8f8;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  cursor: pointer;
}

.secondaryActionButton {
  flex: 1;
  margin: 0 4px;
  background-color: #fff;
  border: 1px solid rgba(255, 149, 43, 1); /* PATIENT_COLORS.primary */
}

.actionText {
  margin-left: 8px;
  color: rgba(255, 149, 43, 1); /* PATIENT_COLORS.primary */
  font-weight: 600;
  font-size: 15px;
}

.primaryActionButton {
  background-color: rgba(255, 149, 43, 1); /* PATIENT_COLORS.primary */
  border-width: 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.primaryActionText {
  color: #fff;
  font-weight: 700;
}

.loadingContainer {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.loadingText {
  margin-top: 12px;
  font-size: 16px;
  color: #666;
}

.emptyContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.emptyIcon {
  margin-bottom: 16px;
}

.emptyText {
  font-size: 18px;
  font-weight: bold;
  color: #666;
  margin-bottom: 8px;
}

.emptySubtext {
  font-size: 14px;
  color: #999;
  text-align: center;
  padding: 0 32px;
}

/* Animation classes */
.doctorCard:hover {
  transform: scale(1.01);
}

/* Spinner for loading indicator */
.spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border-left-color: rgba(255, 149, 43, 1); /* PATIENT_COLORS.primary */
  animation: spin 1s linear infinite;
}

/* Refresh button */
.refreshButton {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  background-color: #fff;
  border: 1px solid #eee;
  border-radius: 8px;
  margin-bottom: 16px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  color: rgba(255, 149, 43, 1); /* PATIENT_COLORS.primary */
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.refreshButton:hover {
  background-color: #f9f9f9;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.refreshButton svg {
  margin-right: 8px;
}

.refreshIndicator {
  text-align: center;
  padding: 8px;
  color: rgba(255, 149, 43, 1); /* PATIENT_COLORS.primary */
  font-size: 14px;
  margin-bottom: 8px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
