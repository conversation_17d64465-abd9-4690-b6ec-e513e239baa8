:root {
  --caregiver-primary: #5C6BC0; /* Définition de la couleur primaire du caregiver */
}

.container {
  display: flex;
  flex-direction: column;
  flex: 1;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  flex: 1;
  justify-content: center;
  align-items: center;
}

.loadingText {
  margin-top: 10px;
  font-size: 16px;
  color: #666;
}

.spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border-top: 4px solid var(--caregiver-primary);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.filterContainer {
  padding: 10px 15px;
  background-color: #fff;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  overflow-x: auto;
}

.filterButton {
  padding: 8px 16px;
  margin-right: 10px;
  border-radius: 20px;
  background-color: #f0f0f0;
  cursor: pointer;
}

.activeFilterButton {
  background-color: var(--caregiver-primary);
}

.filterButtonText {
  color: #666;
  font-weight: 500;
}

.activeFilterButtonText {
  color: #fff;
}

.listContainer {
  padding: 15px;
}

.appointmentCard {
  margin-bottom: 15px;
}

.card {
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  background-color: white;
  padding: 15px;
}

.cardHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 10px;
}

.patientInfo {
  flex: 1;
}

.patientName {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.appointmentDate {
  font-size: 14px;
  color: #666;
  margin-top: 2px;
}

.appointmentTime {
  font-size: 14px;
  color: #666;
  margin-top: 2px;
}

.statusContainer {
  display: flex;
  align-items: flex-end;
}

.statusChip {
  height: 28px;
  padding: 4px 8px;
  border-radius: 14px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 500;
}

.doctorContainer {
  display: flex;
  align-items: center;
  margin-top: 5px;
}

.doctorName {
  font-size: 14px;
  color: #666;
  margin-left: 5px;
}

.reason {
  font-size: 14px;
  color: #666;
  margin-top: 5px;
}

.emptyContainer {
  display: flex;
  flex-direction: column;
  flex: 1;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.emptyText {
  font-size: 18px;
  font-weight: bold;
  color: #666;
  margin-top: 10px;
}

.emptySubText {
  font-size: 14px;
  color: #999;
  text-align: center;
  margin-top: 5px;
}

.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modalContainer {
  background-color: white;
  margin: 20px;
  border-radius: 10px;
  padding: 20px;
  max-height: 80vh;
  width: 90%;
  max-width: 500px;
  overflow-y: auto;
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.modalTitle {
  font-size: 20px;
  font-weight: bold;
}

.closeButton {
  padding: 5px;
  cursor: pointer;
  background: none;
  border: none;
}

.modalContent {
  margin-bottom: 20px;
}

.detailRow {
  display: flex;
  margin-bottom: 12px;
  align-items: center;
}

.detailLabel {
  width: 80px;
  font-size: 16px;
  font-weight: bold;
  color: #555;
}

.detailValue {
  flex: 1;
  font-size: 16px;
  color: #333;
}

.closeModalButton {
  margin-top: 10px;
  padding: 8px 16px;
  border: 1px solid var(--caregiver-primary);
  background: none;
  border-radius: 4px;
  cursor: pointer;
  color: var(--caregiver-primary);
}
