.addButton {
  border-radius: 8px;
  padding: 12px;
  box-shadow: 0 2px 2px rgba(0, 0, 0, 0.1);
  background-color: rgb(212, 0, 131);
  cursor: pointer;
  border: none;
}

.buttonContent {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}

.buttonText {
  color: #fff;
  font-weight: bold;
  margin-left: 8px;
}

/* Styles pour le modal temporaire */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #eee;
}

.modal-header h3 {
  margin: 0;
  font-size: 18px;
  color: #333;
}

.close-button {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
}

.modal-body {
  padding: 20px;
}

.modal-button {
  background-color: rgb(212, 0, 131);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 10px 16px;
  font-weight: bold;
  cursor: pointer;
  margin-top: 15px;
}

.modal-button:hover {
  background-color: rgb(180, 0, 110);
}
