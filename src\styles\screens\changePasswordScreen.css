.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f7fa;
}

.header {
  display: flex;
  align-items: center;
  padding: 16px;
  background-color: #106b00;
  color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.back-button {
  background: none;
  border: none;
  color: white;
  font-size: 18px;
  cursor: pointer;
  margin-right: 16px;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.back-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.header-title {
  font-size: 20px;
  font-weight: 600;
  margin: 0;
}

.content {
  flex: 1;
  padding: 16px;
}

.form-container {
  max-width: 500px;
  margin: 0 auto;
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-container {
  text-align: center;
  margin-bottom: 32px;
}

.header-icon {
  font-size: 40px;
  color: #106b00;
  margin-bottom: 16px;
}

.header-text {
  font-size: 24px;
  font-weight: bold;
  color: #106b00;
  margin: 0 0 8px 0;
}

.sub-header-text {
  font-size: 16px;
  color: #757575;
  margin: 0;
}

.input-container {
  margin-bottom: 20px;
}

.input-label {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
  color: #212121;
  display: block;
}

.input-box {
  display: flex;
  align-items: center;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 0 12px;
  background-color: white;
  transition: border-color 0.2s;
}

.input-box:focus-within {
  border-color: #106b00;
  box-shadow: 0 0 0 2px rgba(16, 107, 0, 0.1);
}

.input-icon {
  color: #106b00;
  margin-right: 10px;
  font-size: 20px;
}

.input {
  flex: 1;
  border: none;
  outline: none;
  padding: 12px 0;
  font-size: 16px;
  background: transparent;
}

.input::placeholder {
  color: #999;
}

.password-toggle {
  background: none;
  border: none;
  color: #106b00;
  cursor: pointer;
  padding: 4px;
  font-size: 20px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.password-toggle:hover {
  background-color: rgba(16, 107, 0, 0.1);
}

.button {
  width: 100%;
  background-color: #106b00;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 16px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
  margin-bottom: 12px;
}

.button:hover:not(:disabled) {
  background-color: #0d5a00;
}

.button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.cancel-button {
  width: 100%;
  background: none;
  color: #106b00;
  border: 1px solid #106b00;
  border-radius: 8px;
  padding: 16px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
}

.cancel-button:hover:not(:disabled) {
  background-color: rgba(16, 107, 0, 0.1);
}

.cancel-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid #ffffff;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.error-message {
  color: #f44336;
  font-size: 14px;
  margin-top: 4px;
}

.success-message {
  color: #4caf50;
  font-size: 14px;
  margin-top: 4px;
}

/* Responsive design */
@media (max-width: 768px) {
  .content {
    padding: 8px;
  }
  
  .form-container {
    margin: 0;
    border-radius: 0;
    box-shadow: none;
  }
  
  .header {
    padding: 12px 16px;
  }
  
  .header-title {
    font-size: 18px;
  }
}
