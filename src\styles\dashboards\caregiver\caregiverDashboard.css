/* CaregiverDashboard.css */

.scrollView {
  flex: 1;
  background-color: #f5f7fa;
  overflow-y: auto;
  height: 100vh;
}

.headerSection {
  width: 100%;
  margin-bottom: 20px;
}

.headerGradient {
  padding: 25px 20px;
  border-bottom-left-radius: 20px;
  border-bottom-right-radius: 20px;
}

.headerContent {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.contentSection {
  padding: 0 16px 20px 16px;
}

.welcomeText {
  font-size: 24px;
  font-weight: bold;
  color: white;
  margin-bottom: 5px;
}

.dateText {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
  margin-top: 4px;
}

.sectionHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
  margin-bottom: 12px;
}

.sectionTitle {
  font-size: 18px;
  font-weight: bold;
  color: #212121;
  margin-bottom: 8px;
}

.viewAllButton {
  background: none;
  border: none;
  font-size: 14px;
  color: #00A9FF; /* CAREGIVER_COLORS.primary */
  font-weight: 500;
  cursor: pointer;
}

.cardsContainer {
  margin-bottom: 24px;
  padding: 0;
}

.cardRow {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
}

.addPatientContainer {
  padding: 0 16px;
  margin-bottom: 16px;
}

.addPatientButton {
  background-color: #00A9FF; /* CAREGIVER_COLORS.primary */
  padding: 12px 20px;
  border-radius: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  border: none;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  width: 100%;
}

.addPatientText {
  color: #fff;
  font-weight: bold;
  margin-left: 8px;
}

.qrCodeContainer {
  padding: 0 16px;
  margin-bottom: 16px;
}

.qrCodeButton {
  background-color: #00A9FF; /* CAREGIVER_COLORS.primary */
  padding: 12px 20px;
  border-radius: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  border: none;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  width: 100%;
}

.qrCodeText {
  color: #fff;
  font-weight: bold;
  margin-left: 8px;
}

.chartCard {
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  background-color: #ffffff;
  border-radius: 16px;
  overflow: hidden;
  padding: 16px;
}

.chartTitle {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 4px;
}

.chartSubtitle {
  font-size: 14px;
  color: #757575;
  margin-bottom: 16px;
}

.chartContent {
  width: 100%;
}

.upcomingSection {
  margin-bottom: 16px;
}

.spacer {
  height: 30px;
}

/* Responsive styles */
@media (max-width: 768px) {
  .cardRow {
    flex-direction: column;
  }
  
  .cardRow > div {
    width: 100% !important;
    margin-bottom: 12px;
  }
}
