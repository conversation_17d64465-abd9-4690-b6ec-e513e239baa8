/* SharedProfileForm CSS */
.container {
  flex: 1;
  background-color: #f5f5f5;
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.form {
  padding: 15px;
}

.card {
  background-color: #fff;
  border-radius: 12px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
}

.section-header-content {
  display: flex;
  align-items: center;
}

.section-icon {
  margin-right: 10px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.section-content {
  padding: 15px;
}

.field-container {
  margin-bottom: 20px;
}

.label {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
  display: block;
}

.required {
  color: rgba(16, 107, 0, 1);
  margin-left: 2px;
}

.input {
  background-color: #f8f8f8;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 12px;
  font-size: 16px;
  color: #333;
  width: 100%;
  box-sizing: border-box;
}

.date-text {
  color: #000;
}

.placeholder-text {
  color: #999;
}

.picker-container {
  background-color: #f8f8f8;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
  width: 100%;
}

.picker {
  height: 50px;
  width: 100%;
  padding: 12px;
  border: none;
  background-color: #f8f8f8;
  font-size: 16px;
}

.phone-container {
  display: flex;
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
}

.dial-code {
  display: flex;
  align-items: center;
  background-color: rgba(16, 107, 0, 0.1);
  padding: 0 10px;
  border-right: 1px solid #ddd;
}

.dial-code-text {
  margin-left: 8px;
  font-size: 16px;
  color: #333;
}

.phone-input {
  flex: 1;
  padding: 12px;
  font-size: 16px;
  color: #333;
  border: none;
}

.button-container {
  margin-top: 30px;
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
}

.submit-button {
  background-color: rgba(16, 107, 0, 1);
  padding: 15px 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80%;
  box-shadow: 0 3px 5px rgba(0, 0, 0, 0.2);
  border: none;
  cursor: pointer;
}

.submit-button-disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.submit-button-text {
  color: #fff;
  font-size: 18px;
  font-weight: bold;
  letter-spacing: 0.5px;
}

/* Profile image section */
.profile-image-section {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px 0;
}

.profile-image-container {
  position: relative;
  width: 150px;
  height: 150px;
  border-radius: 75px;
}

.profile-image {
  width: 150px;
  height: 150px;
  border-radius: 75px;
  border: 3px solid #FF5722;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  object-fit: cover;
}

.profile-image-placeholder {
  width: 150px;
  height: 150px;
  border-radius: 75px;
  background-color: #f0f0f0;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 3px solid #FF5722;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.profile-image-placeholder-text {
  font-size: 50px;
  font-weight: bold;
  color: #FF5722;
}

.edit-image-button {
  position: absolute;
  bottom: 5px;
  right: 5px;
  background-color: #FF5722;
  width: 45px;
  height: 45px;
  border-radius: 23px;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
  border: 2px solid #FFF;
  cursor: pointer;
}

/* Modal styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: #fff;
  border-radius: 16px;
  padding: 25px;
  width: 90%;
  max-width: 450px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
  min-height: 300px;
}

.modal-title {
  font-size: 20px;
  font-weight: bold;
  color: #333;
  letter-spacing: 0.5px;
  margin-bottom: 20px;
}

.modal-buttons {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
  margin-top: 10px;
  padding: 0 20px;
}

.modal-button-cancel {
  flex: 1;
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #ccc;
  margin-right: 5px;
  text-align: center;
  background-color: #f5f5f5;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  cursor: pointer;
}

.modal-button-confirm {
  flex: 1;
  padding: 15px;
  border-radius: 8px;
  background-color: #FF5722;
  margin-left: 8px;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  cursor: pointer;
  border: none;
}

.modal-button-text-cancel {
  font-size: 16px;
  color: #333;
  font-weight: 600;
}

.modal-button-text-confirm {
  font-size: 16px;
  color: #fff;
  font-weight: bold;
}

.non-editable {
  color: #888;
  font-size: 12px;
  font-style: italic;
}

.input-disabled {
  background-color: #f0f0f0;
  color: #666;
}

/* Responsive design */
@media (max-width: 768px) {
  .container {
    padding: 10px;
  }
  
  .submit-button {
    width: 100%;
  }
}
