import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { usersAPI } from '../config/api';
import { useAuth } from '../contexts/AuthContext';
import { COLORS } from '../config/theme';
import '../styles/screens/scanPatientQRScreen.css';

const ScanPatientQRScreen = () => {
  // State variables
  const [loading, setLoading] = useState(false);
  const [patientCode, setPatientCode] = useState('');
  const [hasPermission, setHasPermission] = useState(null);
  const [scanned, setScanned] = useState(false);
  const [message, setMessage] = useState(null);

  // Navigation and context
  const navigate = useNavigate();
  const location = useLocation();
  const { user } = useAuth();

  // Check if we should show camera or manual entry
  const mode = location.state?.mode || 'manual';
  const showCamera = mode === 'camera';

  // Request camera permission when showing camera
  useEffect(() => {
    if (showCamera) {
      const getBarCodeScannerPermissions = async () => {
        try {
          const stream = await navigator.mediaDevices.getUserMedia({ video: true });
          setHasPermission(true);
          stream.getTracks().forEach(track => track.stop()); // Stop the stream immediately
        } catch (error) {
          setHasPermission(false);
        }
      };

      getBarCodeScannerPermissions();
    }
  }, [showCamera]);

  // Show message function
  const showMessage = (messageData) => {
    setMessage(messageData);
    setTimeout(() => setMessage(null), 3000);
  };

  // Handle manual code changes
  const handleCodeChange = (e) => {
    // Auto uppercase the input
    setPatientCode(e.target.value.toUpperCase());
  };

  // Check if a code is valid
  const isValidCode = (code) => {
    return code && code.length === 8 && /^[A-Z0-9]{8}$/.test(code);
  };

  // Manual code submission
  const handleSubmit = () => {
    if (!isValidCode(patientCode)) {
      showMessage({
        message: 'Invalid Code Format',
        description: 'Please enter a valid 8-character code (letters and numbers only)',
        type: 'danger',
        backgroundColor: COLORS.primary,
      });
      return;
    }

    processUserCode(patientCode);
  };

  // Handle taking a picture
  const takePicture = async () => {
    try {
      // In a real web app, you would use a QR code scanner library
      // For now, we'll just simulate a successful scan
      showMessage({
        message: 'QR Code Captured',
        description: 'Processing the image...',
        type: 'info',
        backgroundColor: COLORS.primary,
      });

      // Simulate finding a valid code after a delay
      setTimeout(() => {
        // Generate a random 8-character code
        const randomCode = Math.random().toString(36).substring(2, 10).toUpperCase();
        processUserCode(randomCode);
      }, 2000);
    } catch (error) {
      console.error('Error taking picture:', error);
      showMessage({
        message: 'Error',
        description: 'Failed to capture image',
        type: 'danger',
        backgroundColor: COLORS.primary,
      });
    }
  };

  // Process a user code (from QR or manual entry)
  const processUserCode = async (code) => {
    setLoading(true);

    try {
      console.log('Processing code:', code);

      // Verify the code belongs to a patient
      const userResponse = await usersAPI.getUserByCode(code);
      console.log('User found:', userResponse);

      if (!userResponse || userResponse.role !== 'patient') {
        showMessage({
          message: 'Invalid User',
          description: 'This code does not belong to a patient',
          type: 'danger',
          backgroundColor: COLORS.primary,
        });
        setLoading(false);
        setScanned(false);
        return;
      }

      // Get relationship type from route params or determine based on user role
      let relationshipType = location.state?.relationshipType || 'doctor-patient';

      // If not provided in route params, determine based on user role
      if (!location.state?.relationshipType) {
        if (user.role === 'nurse') {
          relationshipType = 'nurse-patient';
        } else if (user.role === 'pharmacist') {
          relationshipType = 'pharmacist-patient';
        } else if (user.role === 'supervisor') {
          relationshipType = 'supervisor-patient';
        }
      }

      console.log('Linking user with type:', relationshipType);
      const linkResponse = await usersAPI.linkUser(code, relationshipType);
      console.log('Link response:', linkResponse);

      showMessage({
        message: 'Success',
        description: `Patient ${userResponse.displayName} added successfully`,
        type: 'success',
        backgroundColor: COLORS.primary,
      });

      // Go back to the previous screen
      setTimeout(() => {
        navigate(-1);
      }, 1500);
    } catch (error) {
      console.error('Error processing code:', error);
      let errorMessage = 'Failed to add patient. Please try again.';

      if (error?.responseData?.error === 'User not found with this code') {
        errorMessage = 'Invalid code. No patient found with this code.';
      }

      showMessage({
        message: 'Error',
        description: errorMessage,
        type: 'danger',
        backgroundColor: COLORS.primary,
      });

      setScanned(false);
    } finally {
      setLoading(false);
    }
  };

  // Loading state
  if (loading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <div className="loading-text">Adding patient...</div>
      </div>
    );
  }

  // Camera scanning mode
  if (showCamera) {
    // Handle permission states
    if (hasPermission === null) {
      return (
        <div className="container">
          <div className="permission-text">Requesting camera permission...</div>
        </div>
      );
    }

    if (hasPermission === false) {
      return (
        <div className="container">
          <div className="permission-text">No access to camera</div>
          <button
            className="button primary-button"
            style={{ marginTop: '20px' }}
            onClick={() => navigate('/scan-patient-qr', { state: { mode: 'manual' } })}
          >
            Enter Code Manually
          </button>
        </div>
      );
    }

    return (
      <div className="container">
        {message && (
          <div className={`message ${message.type}`}>
            <strong>{message.message}</strong>
            <p>{message.description}</p>
          </div>
        )}

        <div className="camera-container">
          <div className="scan-area">
            {scanned && !loading && (
              <div className="scanning-animation">
                <div className="loading-spinner"></div>
                <div className="scanning-text">Processing...</div>
              </div>
            )}
          </div>

          <button
            className="capture-button"
            onClick={takePicture}
            disabled={scanned}
          >
            <i className="camera-icon">📷</i>
          </button>
        </div>

        {/* Informational note for users */}
        <div className="info-box">
          <div className="info-text">
            Position the patient's QR code within the frame to scan.
          </div>
        </div>

        <div className="footer">
          <div className="footer-text">
            Position QR code inside the square
          </div>

          <div className="button-row">
            <button
              className="button"
              onClick={() => navigate(-1)}
            >
              Cancel
            </button>

            {scanned && !loading && (
              <button
                className="button"
                onClick={() => setScanned(false)}
              >
                Scan Again
              </button>
            )}

            <button
              className="button"
              onClick={() => navigate('/scan-patient-qr', { state: { mode: 'manual' } })}
            >
              Enter Manually
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Manual entry mode
  return (
    <div className="container">
      {message && (
        <div className={`message ${message.type}`}>
          <strong>{message.message}</strong>
          <p>{message.description}</p>
        </div>
      )}

      <div className="form-container">
        <div className="qr-icon">📱</div>

        <h1 className="title">Add Patient by Code</h1>
        <p className="subtitle">
          Enter the 8-character code from patient's profile
        </p>

        <input
          className="input"
          value={patientCode}
          onChange={handleCodeChange}
          placeholder="Enter code (e.g. ABCD1234)"
          maxLength={8}
          type="text"
        />

        <div className="helper-container">
          <div className="helper-text">
            {patientCode.length}/8 characters
          </div>
          {patientCode.length > 0 && (
            <div className={`validation-text ${isValidCode(patientCode) ? 'valid-text' : 'invalid-text'}`}>
              {isValidCode(patientCode) ? '✓ Valid code' : '✗ Invalid format'}
            </div>
          )}
        </div>

        <button
          className={`button ${isValidCode(patientCode) && !loading ? 'button-valid' : 'button-disabled'}`}
          onClick={handleSubmit}
          disabled={!isValidCode(patientCode) || loading}
        >
          {isValidCode(patientCode) ? 'Connect Patient' : 'Enter Valid Code'}
        </button>

        <button
          className="cancel-button"
          onClick={() => navigate(-1)}
          disabled={loading}
        >
          Cancel
        </button>
      </div>
    </div>
  );
};

export default ScanPatientQRScreen;
