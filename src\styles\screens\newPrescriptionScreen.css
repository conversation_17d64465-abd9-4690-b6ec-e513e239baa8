.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8f9fa;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background-color: white;
  border-bottom: 1px solid #e0e0e0;
}

.header-title {
  font-size: 20px;
  font-weight: bold;
  color: #333;
  margin: 0;
}

.back-button {
  padding: 8px;
  background: none;
  border: none;
  cursor: pointer;
  border-radius: 4px;
}

.back-button:hover {
  background-color: #f5f5f5;
}

.icon-arrow-back {
  font-size: 24px;
  color: #333;
}

.content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.section {
  background-color: white;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin: 0 0 12px 0;
}

.patient-selector {
  width: 100%;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 12px;
  background: white;
  cursor: pointer;
  transition: border-color 0.2s;
}

.patient-selector:hover {
  border-color: #ccc;
}

.selected-patient,
.patient-placeholder {
  display: flex;
  align-items: center;
}

.selected-patient-name {
  font-size: 16px;
  color: #333;
  margin-left: 8px;
}

.patient-placeholder {
  justify-content: center;
}

.patient-placeholder-text {
  font-size: 16px;
  color: #757575;
  margin-left: 8px;
}

.form-group {
  margin-bottom: 16px;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
}

.form-input {
  width: 100%;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 12px;
  font-size: 16px;
  box-sizing: border-box;
}

.form-input:focus {
  outline: none;
  border-color: #2196F3;
}

.text-area {
  min-height: 80px;
  resize: vertical;
}

.add-medication-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 14px;
  border: none;
  border-radius: 8px;
  margin-top: 8px;
  cursor: pointer;
  transition: opacity 0.2s;
}

.add-medication-button:hover {
  opacity: 0.9;
}

.add-medication-button-text {
  color: white;
  font-weight: bold;
  font-size: 16px;
  margin-left: 8px;
}

.medications-list-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin: 0 0 12px 0;
}

.empty-medications {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px;
  background-color: #f5f5f5;
  border-radius: 8px;
}

.empty-medications-text {
  font-size: 14px;
  color: #757575;
  margin-top: 8px;
}

.medication-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 8px;
}

.medication-info {
  flex: 1;
}

.medication-name {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.medication-dosage {
  font-size: 14px;
  color: #616161;
  margin-bottom: 4px;
}

.medication-instructions {
  font-size: 14px;
  color: #757575;
}

.medication-instructions.italic {
  font-style: italic;
}

.medication-action-button {
  padding: 8px;
  margin-left: 4px;
  background: none;
  border: none;
  cursor: pointer;
  border-radius: 4px;
}

.medication-action-button:hover {
  background-color: #e0e0e0;
}

.notes-input {
  width: 100%;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 12px;
  min-height: 100px;
  resize: vertical;
  box-sizing: border-box;
}

.notes-input:focus {
  outline: none;
  border-color: #2196F3;
}

.image-container {
  position: relative;
  margin-bottom: 8px;
}

.prescription-image {
  width: 100%;
  height: 200px;
  border-radius: 8px;
  background-color: #f5f5f5;
  object-fit: cover;
}

.remove-image-button {
  position: absolute;
  top: 8px;
  right: 8px;
  background: white;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  padding: 4px;
}

.image-actions {
  display: flex;
  justify-content: space-between;
  gap: 8px;
}

.image-action-button {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 16px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: opacity 0.2s;
}

.image-action-button:hover {
  opacity: 0.9;
}

.camera-button {
  background-color: #E1F5FE;
}

.upload-button {
  background-color: #E8F5E9;
}

.image-action-text {
  margin-top: 8px;
  font-weight: 500;
}

.camera-text {
  color: #0288D1;
}

.upload-text {
  color: #43A047;
}

.submit-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 16px;
  border: none;
  border-radius: 8px;
  margin-bottom: 32px;
  cursor: pointer;
  transition: opacity 0.2s;
}

.submit-button:hover:not(:disabled) {
  opacity: 0.9;
}

.submit-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.submit-button-text {
  color: white;
  font-weight: bold;
  font-size: 16px;
  margin-left: 8px;
}

.loading-spinner {
  font-size: 16px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Icon styles */
.icon-person,
.icon-person-add,
.icon-add-circle,
.icon-medkit,
.icon-trash,
.icon-close-circle,
.icon-camera,
.icon-image,
.icon-checkmark-circle {
  font-size: 20px;
  display: inline-block;
}

/* Responsive design */
@media (max-width: 768px) {
  .content {
    padding: 12px;
  }
  
  .section {
    padding: 12px;
    margin-bottom: 12px;
  }
  
  .image-actions {
    flex-direction: column;
  }
  
  .image-action-button {
    margin-bottom: 8px;
  }
}
