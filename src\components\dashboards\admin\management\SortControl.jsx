import React, { useState } from 'react';
import { FaChevronDown, FaArrowUp, FaArrowDown, FaCheck } from 'react-icons/fa';
import '../../../../styles/dashboards/admin/sortControl.css';

const SortControl = ({ options, value, onSelect, direction, onDirectionChange }) => {
  const [dropdownVisible, setDropdownVisible] = useState(false);

  // Find the selected option label
  const selectedOption = options.find(option => option.value === value) || options[0];

  return (
    <div className="sort-container">
      <button
        className="dropdown-button"
        onClick={() => setDropdownVisible(true)}
      >
        <span className="dropdown-button-text">
          {selectedOption.label}
        </span>
        <FaChevronDown size={16} color="#555" />
      </button>

      <button
        className="direction-button"
        onClick={() => onDirectionChange(direction === 'asc' ? 'desc' : 'asc')}
      >
        {direction === 'asc' ? (
          <FaArrowUp size={20} color="#fff" />
        ) : (
          <FaArrowDown size={20} color="#fff" />
        )}
      </button>

      {dropdownVisible && (
        <div className="modal-overlay" onClick={() => setDropdownVisible(false)}>
          <div 
            className="dropdown-menu"
            style={{ top: '170px', left: '80px' }} // Adjust position to match the image
            onClick={(e) => e.stopPropagation()}
          >
            {options.map(option => (
              <button
                key={option.value}
                className={`dropdown-item ${value === option.value ? 'selected-dropdown-item' : ''}`}
                onClick={() => {
                  onSelect(option.value);
                  setDropdownVisible(false);
                }}
              >
                <span className={`dropdown-item-text ${value === option.value ? 'selected-dropdown-item-text' : ''}`}>
                  {option.label}
                </span>
                {value === option.value && (
                  <FaCheck size={18} color="#4CAF50" />
                )}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default SortControl;
