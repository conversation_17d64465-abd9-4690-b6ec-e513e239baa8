/* Patient Health Viewer Styles */
.container {
  flex: 1;
  background-color: #f8f9fa;
  min-height: 100vh;
}

.header {
  background-color: #fff;
  padding: 15px 20px;
  border-bottom: 1px solid #eaeaea;
}

.backButtonContainer {
  background-color: #fff;
  padding-top: 15px;
  padding-left: 15px;
  padding-bottom: 5px;
  border-bottom: 1px solid #eaeaea;
}

.headerTitle {
  font-size: 22px;
  font-weight: 600;
  color: #333;
}

.headerSubtitle {
  font-size: 14px;
  color: #666;
  margin-top: 4px;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 24px;
  min-height: 200px;
}

.loadingText {
  font-size: 16px;
  color: #666;
  margin-top: 16px;
}

.emptyContainer {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 24px;
  min-height: 300px;
}

.emptyText {
  font-size: 18px;
  font-weight: 600;
  color: #666;
  text-align: center;
  margin-top: 16px;
}

.emptySubtext {
  font-size: 14px;
  color: #999;
  text-align: center;
  margin-top: 8px;
  max-width: 80%;
}

.patientList {
  padding: 16px;
}

.patientCard {
  background-color: #fff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

.patientAvatarContainer {
  width: 50px;
  height: 50px;
  border-radius: 25px;
  background-color: rgba(170, 86, 255, 1);
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 12px;
}

.patientAvatarText {
  color: #fff;
  font-size: 18px;
  font-weight: bold;
}

.patientInfo {
  margin-bottom: 16px;
}

.patientName {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.patientEmail {
  font-size: 14px;
  color: #666;
  margin-bottom: 2px;
}

.patientPhone {
  font-size: 14px;
  color: #666;
}

.patientActions {
  display: flex;
  justify-content: space-between;
}

.actionButton {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 12px;
  border-radius: 6px;
  flex: 1;
  margin: 0 4px;
  cursor: pointer;
  border: none;
  color: white;
}

.actionButtonText {
  color: #fff;
  font-size: 12px;
  font-weight: 600;
  margin-left: 4px;
}

.patientHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background-color: #fff;
  border-bottom: 1px solid #eaeaea;
}

.patientHeaderInfo {
  display: flex;
  align-items: center;
}

.patientDetailAvatar {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  background-color: rgba(170, 86, 255, 1);
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 12px;
}

.patientDetailAvatarText {
  color: #fff;
  font-size: 16px;
  font-weight: bold;
}

.patientDetailName {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.patientDetailInfo {
  font-size: 12px;
  color: #666;
}

.backButton {
  display: flex;
  align-items: center;
  padding: 8px;
  border-radius: 8px;
  background-color: #f0f7ff;
  border: 1px solid rgba(170, 86, 255, 1);
  cursor: pointer;
}

.backButtonText {
  color: rgba(170, 86, 255, 1);
  font-size: 14px;
  font-weight: 600;
  margin-left: 4px;
}

.contentContainer {
  flex: 1;
}

.tabsContainer {
  display: flex;
  margin: 16px;
}

.tab {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  padding: 12px;
  border-radius: 8px;
  margin: 0 4px;
  border: 1px solid #eaeaea;
  cursor: pointer;
}

.activeTab {
  background-color: rgba(170, 86, 255, 1);
  border-color: rgba(170, 86, 255, 1);
}

.tabText {
  font-size: 14px;
  font-weight: 500;
  color: #666;
  margin-left: 8px;
}

.activeTabText {
  color: #fff;
  font-weight: 600;
}

.scrollContainer {
  flex: 1;
  overflow-y: auto;
}

.scrollContentContainer {
  flex-grow: 1;
}

/* Modal styles */
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.4);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modalContainer {
  width: 90%;
  max-width: 500px;
  max-height: 80%;
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #eaeaea;
  background-color: #fff;
}

.modalTitle {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.closeButton {
  padding: 6px;
  cursor: pointer;
  background: none;
  border: none;
}

.modalContent {
  padding: 16px;
  overflow-y: auto;
}

.modalSubtitle {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.highlightedText {
  color: rgba(170, 86, 255, 1);
  font-weight: bold;
}

.notesInput {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 12px;
  border: 1px solid #eaeaea;
  color: #333;
  font-size: 16px;
  min-height: 150px;
  margin-top: 8px;
  margin-bottom: 16px;
  width: 100%;
  resize: vertical;
}

.saveButton {
  background-color: rgba(170, 86, 255, 1);
  border-radius: 8px;
  padding: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 8px;
  cursor: pointer;
  border: none;
  color: white;
}

.saveButtonText {
  color: #fff;
  font-size: 16px;
  font-weight: 600;
}

.addNotesButtonContainer {
  padding: 16px;
  display: flex;
  justify-content: center;
}

.addNotesButton {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(170, 86, 255, 1);
  padding: 12px 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: none;
  cursor: pointer;
}

.addNotesButtonText {
  color: #fff;
  font-size: 16px;
  font-weight: 600;
  margin-left: 8px;
}

.contentActions {
  display: flex;
  justify-content: flex-end;
  padding: 0 16px;
  margin-bottom: 8px;
}

.toggleNotesButton {
  display: flex;
  align-items: center;
  background-color: #f0f7ff;
  padding: 8px 12px;
  border-radius: 8px;
  border: 1px solid rgba(170, 86, 255, 1);
  cursor: pointer;
}

.toggleNotesText {
  color: rgba(170, 86, 255, 1);
  font-size: 14px;
  font-weight: 600;
  margin-left: 6px;
}
