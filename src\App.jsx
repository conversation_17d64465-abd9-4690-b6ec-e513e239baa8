import { useLocation } from "react-router-dom";
import './App.css'
import { ThemeProvider, useTheme } from "./contexts/ThemeContext";
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import AppNavitor from "./routes/AppNavitor";

// App content component to use the theme context
function AppContent() {
  const location = useLocation();
  const { isDarkMode } = useTheme();

  // Check if current route is login or signup
  const isAuthRoute = location.pathname === "/signup" || location.pathname === "/login";

  return (
    <div className={!isAuthRoute && isDarkMode ? "dark-app" : ""}>
      <AppNavitor/>
    </div>
  );
}

// Main App component that provides the theme context
function App() {
  return (
    <ThemeProvider>
      <AppWithToast />
    </ThemeProvider>
  );
}

// Component to handle toast with theme context
function AppWithToast() {
  const { isDarkMode } = useTheme();

  return (
    <>
      <AppContent />
      <ToastContainer
        position="top-right"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop={true}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme={isDarkMode ? "dark" : "light"}
      />
    </>
  );
}

export default App
