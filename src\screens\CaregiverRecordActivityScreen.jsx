import React, { useState } from 'react';
import '../styles/screens/caregiverRecordActivityScreen.css';

const CaregiverRecordActivityScreen = ({ patient, onBack, onSuccess }) => {
  // Activity types
  const activityTypes = [
    { id: 'medication', label: 'Medication', icon: '💊' },
    { id: 'exercise', label: 'Exercise', icon: '🏃' },
    { id: 'social', label: 'Social', icon: '👥' },
    { id: 'cognitive', label: 'Cognitive', icon: '📚' },
    { id: 'hygiene', label: 'Hygiene', icon: '🚿' },
    { id: 'nutrition', label: 'Nutrition', icon: '🍽️' },
    { id: 'sleep', label: 'Sleep', icon: '🌙' },
    { id: 'other', label: 'Other', icon: '📝' }
  ];

  const [activityType, setActivityType] = useState('medication');
  const [description, setDescription] = useState('');
  const [duration, setDuration] = useState('');
  const [completed, setCompleted] = useState(true);
  const [notes, setNotes] = useState('');
  const [loading, setLoading] = useState(false);

  const handleActivityTypeChange = (type) => {
    setActivityType(type);
  };

  const validateForm = () => {
    if (!description.trim()) {
      return false;
    }

    if (duration && isNaN(parseInt(duration))) {
      return false;
    }

    return true;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      alert('Please enter a valid description and duration (in minutes)');
      return;
    }

    setLoading(true);
    try {
      // Simulate API call - replace with actual service call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Show success message
      if (onSuccess) {
        onSuccess('Activity recorded successfully');
      }
      
      // Navigate back
      if (onBack) {
        onBack();
      }
    } catch (error) {
      console.error('Error recording activity:', error);
      alert('Failed to record activity. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const renderActivityTypeButton = (id, label, icon) => (
    <button
      key={id}
      type="button"
      className={`activity-type-button ${activityType === id ? 'active' : ''}`}
      onClick={() => handleActivityTypeChange(id)}
    >
      <span className="activity-icon">{icon}</span>
      <span className="activity-label">{label}</span>
    </button>
  );

  return (
    <div className="caregiver-record-activity-container">
      <div className="header">
        <button className="back-button" onClick={onBack}>
          ← Back
        </button>
        <h1 className="header-title">Record Activity</h1>
        <div className="header-right"></div>
      </div>

      <div className="scroll-view">
        <div className="patient-info-container">
          <p className="patient-name">
            Patient: {patient?.firstName} {patient?.lastName}
          </p>
        </div>

        <div className="activity-type-container">
          <h2 className="section-title">Select Activity Type</h2>
          <div className="activity-type-scroll">
            {activityTypes.map(type => renderActivityTypeButton(type.id, type.label, type.icon))}
          </div>
        </div>

        <form className="form-container" onSubmit={handleSubmit}>
          <h2 className="section-title">Activity Details</h2>

          <div className="input-container">
            <label className="input-label">Description *</label>
            <input
              type="text"
              className="input"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Enter activity description"
              required
            />
          </div>

          <div className="input-container">
            <label className="input-label">Duration (minutes)</label>
            <input
              type="number"
              className="input"
              value={duration}
              onChange={(e) => setDuration(e.target.value)}
              placeholder="Enter duration in minutes"
              min="0"
            />
          </div>

          <div className="completed-container">
            <label className="input-label">Activity Status</label>
            <div className="completed-buttons">
              <button
                type="button"
                className={`completed-button ${completed ? 'active' : ''}`}
                onClick={() => setCompleted(true)}
              >
                Completed
              </button>
              <button
                type="button"
                className={`completed-button ${!completed ? 'active' : ''}`}
                onClick={() => setCompleted(false)}
              >
                Not Completed
              </button>
            </div>
          </div>

          <div className="input-container">
            <label className="input-label">Notes (Optional)</label>
            <textarea
              className="input notes-input"
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder="Enter any additional notes"
              rows="4"
            />
          </div>

          <button
            type="submit"
            className="submit-button"
            disabled={loading}
          >
            {loading ? (
              <span className="loading-spinner">⏳</span>
            ) : (
              <>
                <span className="save-icon">💾</span>
                <span>Save Activity</span>
              </>
            )}
          </button>
        </form>
      </div>
    </div>
  );
};

export default CaregiverRecordActivityScreen;
