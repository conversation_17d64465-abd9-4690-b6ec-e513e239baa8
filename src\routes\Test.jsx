import { Route, Router, Routes, useLocation } from "react-router-dom";
import HomePage from "../components/homePage/HomePage"
import { SignUp, Login } from "../components/auth"
import Navigation from "../components/navigation/Navigation";
import Footer from "../components/footer/Footer";
import ThemeToggle from "../components/themeToggle/ThemeToggle";
import { useAuth } from "../contexts/AuthContext";
import "../styles/routes/appNavigator.css"
import { useMemo } from "react";
import { ROLE_COLORS } from "../utils/constants";
import { ToastContainer } from "react-toastify";

const Test = () => {
  const location = useLocation();
  const { user, loading, isRegistering } = useAuth();

    // Get the primary color based on user role
  const primaryColor = useMemo(() => {
    if (!user) return ROLE_COLORS.default.primary;
    const role = user.role?.toLowerCase() || 'default';
    return (ROLE_COLORS[role] || ROLE_COLORS.default).primary;
  }, [user]);

  if (loading) {
    return <div className="loading">Loading...</div>;
  }

  const isAuthRoute = location.pathname === "/signup" || location.pathname === "/login";
  return (
    <Router>
        {user && !isRegistering && <Header primaryColor={primaryColor} />}
      <div className="app-container">
      {!isAuthRoute && <Navigation />}
      <Routes>
      {!user || isRegistering ? (
        <>
        <Route exact path="/" element={<HomePage />} />
        <Route exact path="/signup" element={<SignUp />} />
        <Route exact path="/login" element={<Login />} />
        </>
        ) : (
            // App Routes - Only show when user is authenticated AND not in registration process
            <>
              
            </>
          )}
      </Routes>
      {!isAuthRoute && <Footer />}
      {!isAuthRoute && <ThemeToggle />}
    </div>
    <ToastContainer position="top-center" />
    </Router>
  )
}

export default Test