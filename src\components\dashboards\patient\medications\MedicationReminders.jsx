import React, { useState, useEffect } from 'react';
import { useAuth } from '../../../../contexts/AuthContext';
import { useMedications } from '../../../../contexts/MedicationContext';
import '../../../../styles/dashboards/patient/medications/medicationReminders.css';

const MedicationReminders = () => {
  const { user } = useAuth();
  const { reminders, loading, error, updateReminderStatus } = useMedications();
  const [groupedReminders, setGroupedReminders] = useState([]);
  const [localLoading, setLocalLoading] = useState(false);

  useEffect(() => {
    // Group reminders by time whenever reminders change
    if (reminders && reminders.length > 0) {
      groupRemindersByTime(reminders);
    } else {
      setGroupedReminders([]);
    }
  }, [reminders]);

  // Function to group reminders by time
  const groupRemindersByTime = (reminderData) => {
    // Filter only scheduled or overdue reminders
    const activeReminders = reminderData.filter(r =>
      r.status === 'scheduled' ||
      (r.status !== 'completed' && new Date(r.scheduledTime) < new Date())
    );

    // Create a map to group reminders by hour
    const reminderGroups = {};

    activeReminders.forEach(reminder => {
      const reminderTime = new Date(reminder.scheduledTime);

      // Create a key in format "YYYY-MM-DD HH:00" (hour precision)
      const dateKey = reminderTime.toISOString().split(':')[0] + ':00';

      if (!reminderGroups[dateKey]) {
        reminderGroups[dateKey] = {
          title: formatTimeHeader(reminderTime),
          time: reminderTime,
          data: []
        };
      }

      reminderGroups[dateKey].data.push(reminder);
    });

    // Convert to array and sort by time
    const groupedArray = Object.values(reminderGroups);
    groupedArray.sort((a, b) => a.time - b.time);

    // Only show the next 3 time slots
    const limitedGroups = groupedArray.slice(0, 3);

    setGroupedReminders(limitedGroups);
  };

  // Format the time header
  const formatTimeHeader = (time) => {
    const now = new Date();
    const reminderDate = new Date(time);

    // Format the time part
    const timeStr = reminderDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

    // If it's today
    if (reminderDate.toDateString() === now.toDateString()) {
      return `Today at ${timeStr}`;
    }

    // If it's tomorrow
    const tomorrow = new Date(now);
    tomorrow.setDate(tomorrow.getDate() + 1);
    if (reminderDate.toDateString() === tomorrow.toDateString()) {
      return `Tomorrow at ${timeStr}`;
    }

    // Otherwise show the full date
    return `${reminderDate.toLocaleDateString()} at ${timeStr}`;
  };

  const handleMarkAsTaken = async (reminderId) => {
    setLocalLoading(true);
    try {
      await updateReminderStatus(reminderId, 'completed');
    } catch (error) {
      console.error('Error marking medication as taken:', error);
    } finally {
      setLocalLoading(false);
    }
  };

  const handleSkip = async (reminderId) => {
    setLocalLoading(true);
    try {
      await updateReminderStatus(reminderId, 'missed');
    } catch (error) {
      console.error('Error marking medication as skipped:', error);
    } finally {
      setLocalLoading(false);
    }
  };

  const getStatusColor = (status, dueTime) => {
    if (status === 'completed') return '#4CAF50'; // Green
    if (status === 'missed') return '#F44336'; // Red

    // Check if the medication is due or overdue
    const now = new Date();
    const scheduledTime = new Date(dueTime);

    if (scheduledTime < now) {
      // Overdue
      return '#FF9800'; // Orange
    }

    // Calculate if due within the next hour
    const oneHourFromNow = new Date(now.getTime() + 60 * 60 * 1000);
    if (scheduledTime <= oneHourFromNow) {
      return '#2196F3'; // Blue - due soon
    }

    return '#757575'; // Gray - scheduled for later
  };

  const getTimeDisplay = (scheduledTime) => {
    const now = new Date();
    const reminderTime = new Date(scheduledTime);

    // If it's today
    if (reminderTime.toDateString() === now.toDateString()) {
      return `Today at ${reminderTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
    }

    // If it's tomorrow
    const tomorrow = new Date(now);
    tomorrow.setDate(tomorrow.getDate() + 1);
    if (reminderTime.toDateString() === tomorrow.toDateString()) {
      return `Tomorrow at ${reminderTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
    }

    // Otherwise show the full date
    return reminderTime.toLocaleString([], {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getDueStatus = (scheduledTime, status) => {
    if (status === 'completed') return 'Taken';
    if (status === 'missed') return 'Skipped';

    const now = new Date();
    const reminderTime = new Date(scheduledTime);

    if (reminderTime < now) {
      const diffMs = now - reminderTime;
      const diffMins = Math.floor(diffMs / 60000);

      if (diffMins < 60) {
        return `${diffMins} min overdue`;
      } else {
        const diffHours = Math.floor(diffMins / 60);
        return `${diffHours} hour${diffHours > 1 ? 's' : ''} overdue`;
      }
    } else {
      const diffMs = reminderTime - now;
      const diffMins = Math.floor(diffMs / 60000);

      if (diffMins < 60) {
        return `Due in ${diffMins} min`;
      } else {
        const diffHours = Math.floor(diffMins / 60);
        return `Due in ${diffHours} hour${diffHours > 1 ? 's' : ''}`;
      }
    }
  };

  const renderReminderItem = (item) => {
    const statusColor = getStatusColor(item.status, item.scheduledTime);
    const timeDisplay = getTimeDisplay(item.scheduledTime);
    const dueStatus = getDueStatus(item.scheduledTime, item.status);

    return (
      <div className="reminderItem" key={item.id}>
        <div className="reminderHeader">
          <div className="statusIndicator" style={{ backgroundColor: statusColor }} />
          <div className="medicationName">{item.medicationName}</div>
          <div className="dosage">{item.dosage}</div>
        </div>

        <div className="reminderDetails">
          <div className="timeContainer">
            <i className="material-icons" style={{ fontSize: '16px', color: '#757575' }}>schedule</i>
            <span className="timeText">{timeDisplay}</span>
          </div>

          <span className="statusText" style={{ color: statusColor }}>{dueStatus}</span>
        </div>

        <div className="instructions">{item.instructions}</div>

        {item.status === 'scheduled' && (
          <div className="actionButtons">
            <button
              className="actionButton takenButton"
              onClick={() => handleMarkAsTaken(item.id)}
            >
              <i className="material-icons" style={{ fontSize: '16px', color: '#fff' }}>check</i>
              <span className="actionButtonText">Mark as Taken</span>
            </button>

            <button
              className="actionButton skipButton"
              onClick={() => handleSkip(item.id)}
            >
              <i className="material-icons" style={{ fontSize: '16px', color: '#fff' }}>close</i>
              <span className="actionButtonText">Skip</span>
            </button>
          </div>
        )}
      </div>
    );
  };

  if (loading || localLoading) {
    return (
      <div className="loadingContainer">
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
        <div className="loadingText">Loading reminders...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="errorContainer">
        <i className="material-icons" style={{ fontSize: '24px', color: '#F44336' }}>error</i>
        <div className="errorText">{error}</div>
      </div>
    );
  }

  if (!reminders || reminders.length === 0) {
    return (
      <div className="emptyContainer">
        <i className="material-icons" style={{ fontSize: '48px', color: '#BDBDBD' }}>medical_services</i>
        <div className="emptyText">No medication reminders</div>
      </div>
    );
  }

  return (
    <div className="container">
      {groupedReminders.map((section) => (
        <div key={section.title}>
          <div className="sectionHeader">
            <div className="timeHeaderContainer">
              <i className="material-icons" style={{ fontSize: '20px', color: '#4285F4' }}>schedule</i>
              <span className="timeHeader">{section.title}</span>
            </div>
            <span className="medicationCount">
              {section.data.length} medication{section.data.length !== 1 ? 's' : ''}
            </span>
          </div>
          {section.data.map(renderReminderItem)}
        </div>
      ))}
    </div>
  );
};

export default MedicationReminders;
