import { Route, Routes, useLocation } from "react-router-dom";
import HomePage from "../components/homePage/HomePage"
import { SignUp, Login } from "../components/auth"
import Navigation from "../components/navigation/Navigation";
import Footer from "../components/footer/Footer";
import ThemeToggle from "../components/themeToggle/ThemeToggle";

const AppNavitor = () => {
  const location = useLocation();

  const isAuthRoute = location.pathname === "/signup" || location.pathname === "/login";
  return (
    <div>
      {!isAuthRoute && <Navigation />}
      <Routes>
        <Route
          exact
          path="/"
          element={<HomePage />}
        />
        <Route exact path="/signup" element={<SignUp />} />
        <Route exact path="/login" element={<Login />} />
      </Routes>
      {!isAuthRoute && <Footer />}
      {!isAuthRoute && <ThemeToggle />}
    </div>
  )
}

export default AppNavitor