import React, { createContext, useContext, useState, useEffect } from 'react';
import '../styles/theme.css';

// Define role-specific colors
export const ROLE_COLORS = {
  patient: {
    primary: 'rgba(255, 149, 43, 1)', // Orange for patients
    primaryLight: 'rgba(255, 149, 43, 0.8)',
    primaryLighter: 'rgba(255, 149, 43, 0.1)'
  },
  doctor: {
    primary: 'rgba(170, 86, 255, 1)', // Purple for doctors
    primaryLight: 'rgba(170, 86, 255, 0.8)',
    primaryLighter: 'rgba(170, 86, 255, 0.1)'
  },
  supervisor: {
    primary: 'rgb(255, 0, 242)', // Bright magenta/pink for supervisors
    primaryLight: 'rgba(255, 0, 242, 0.8)',
    primaryLighter: 'rgba(255, 0, 242, 0.1)'
  },
  caregiver: {
    primary: 'rgba(0, 169, 255, 1)', // Blue for caregivers
    primaryLight: 'rgba(0, 169, 255, 0.8)',
    primaryLighter: 'rgba(0, 169, 255, 0.1)'
  },
  admin: {
    primary: 'rgba(16, 107, 0, 1)', // Green for admin
    primaryLight: 'rgba(16, 107, 0, 0.8)',
    primaryLighter: 'rgba(16, 107, 0, 0.1)'
  },
  default: {
    primary: 'rgba(16, 107, 0, 1)', // Default green
    primaryLight: 'rgba(16, 107, 0, 0.8)',
    primaryLighter: 'rgba(16, 107, 0, 0.1)'
  }
};

// Export patient colors for direct access
export const PATIENT_COLORS = ROLE_COLORS.patient;

export const COLORS = {
  success: '#4CAF50',
  error: '#F44336',
  warning: '#FFA726',
  info: '#29B6F6',
  textDark: '#212121',
  textMedium: '#757575',
  textLight: '#BDBDBD',
  background: '#f5f7fa',
  surface: '#FFFFFF',
  border: '#E0E0E0',
};

// Create a theme context
const ThemeContext = createContext();

// Theme provider component
export const ThemeProvider = ({ children, initialRole = 'default' }) => {
  const [currentRole, setCurrentRole] = useState(initialRole);
  
  // Get theme for the current role
  const getThemeForRole = (role = 'default') => {
    // Get role-specific colors or default if role doesn't exist
    const roleColors = ROLE_COLORS[role] || ROLE_COLORS.default;
    
    // Base theme applied to the entire application
    return {
      roundness: 8,
      colors: {
        primary: roleColors.primary,
        primaryLight: roleColors.primaryLight,
        primaryLighter: roleColors.primaryLighter,
        accent: COLORS.success,
        background: COLORS.background,
        surface: COLORS.surface,
        error: COLORS.error,
        text: COLORS.textDark,
        placeholder: COLORS.textLight,
        backdrop: 'rgba(0, 0, 0, 0.5)',
        notification: COLORS.info,
      },
    };
  };
  
  const theme = getThemeForRole(currentRole);
  
  // Apply theme class to body element
  useEffect(() => {
    // Remove all theme classes
    document.body.classList.forEach(className => {
      if (className.startsWith('theme-')) {
        document.body.classList.remove(className);
      }
    });
    
    // Add current theme class
    document.body.classList.add(`theme-${currentRole}`);
  }, [currentRole]);
  
  return (
    <ThemeContext.Provider value={{ theme, currentRole, setCurrentRole }}>
      {children}
    </ThemeContext.Provider>
  );
};

// Custom hook to use the theme
export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

// Export default for backward compatibility
export default ThemeProvider;
