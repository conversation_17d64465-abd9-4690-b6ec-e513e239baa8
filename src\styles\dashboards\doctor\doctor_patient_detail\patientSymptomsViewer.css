/* Patient Symptoms Viewer Styles */
:root {
  --doctor-primary: #4a90e2;
  --doctor-secondary: #2c3e50;
  --doctor-accent: #1abc9c;
}
.container {
  flex: 1;
  background-color: #f8f9fa;
  height: 100%;
}

.header {
  background-color: #fff;
  padding: 16px;
  border-bottom: 1px solid #eaeaea;
}

.headerTitle {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.headerSubtitle {
  font-size: 14px;
  color: #666;
  margin-top: 4px;
}

.timeRangeContainer {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin: 16px 16px 12px 16px;
}

.timeRangeLabel {
  font-size: 14px;
  color: #666;
  margin-right: 10px;
}

.timeRangeButtons {
  display: flex;
  flex-direction: row;
}

.timeRangeButton {
  padding: 8px 14px;
  margin: 0 4px;
  border-radius: 8px;
  background-color: #f8f9fa;
  border: 1px solid #eaeaea;
  cursor: pointer;
}

.selectedTimeRange {
  background-color: #f0f7ff;
  border-color: var(--doctor-primary);
}

.timeRangeText {
  font-size: 13px;
  color: #666;
  font-weight: 500;
}

.selectedTimeRangeText {
  color: var(--doctor-primary);
  font-weight: 600;
}

.content {
  flex: 1;
  padding: 16px;
}

.section {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  border: 1px solid #eaeaea;
}

.sectionTitle {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 12px;
  color: #333;
}

.symptomCard {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 10px;
  border: 1px solid #eaeaea;
  position: relative;
  cursor: pointer;
}

.symptomHeader {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin-bottom: 8px;
}

.dateContainer, .moodContainer {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.dateText, .moodText {
  font-size: 12px;
  color: #666;
  margin-left: 4px;
}

.symptomsContainer {
  margin-bottom: 8px;
}

.symptomItem {
  margin-bottom: 8px;
}

.symptomNameContainer {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 2px;
}

.symptomName {
  font-size: 14px;
  font-weight: bold;
  color: #333;
  margin-right: 8px;
}

.severityBadge {
  padding: 2px 6px;
  border-radius: 4px;
}

.severityText {
  font-size: 10px;
  color: #fff;
  font-weight: bold;
}

.symptomDescription {
  font-size: 12px;
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.moreSymptoms {
  font-size: 12px;
  color: var(--doctor-primary);
  font-style: italic;
}

.notes {
  font-size: 12px;
  color: #666;
  font-style: italic;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.chevron {
  position: absolute;
  right: 16px;
  top: 50%;
  margin-top: -12px;
}

.emptyContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px;
}

.emptyText {
  font-size: 14px;
  color: #666;
  text-align: center;
  margin-top: 8px;
}

.loadingContainer {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 24px;
}

.loadingText {
  font-size: 14px;
  color: #666;
  margin-top: 8px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top: 4px solid var(--doctor-primary);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Modal styles */
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.4);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modalContainer {
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
}

.modalHeader {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #eaeaea;
  background-color: #fff;
}

.modalTitle {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.closeButton {
  padding: 6px;
  cursor: pointer;
}

.modalContent {
  padding: 16px;
  max-height: calc(80vh - 60px);
  overflow-y: auto;
}

.modalDateContainer {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 16px;
}

.modalDateText {
  font-size: 16px;
  color: #333;
  margin-left: 8px;
  font-weight: bold;
}

.modalSection {
  margin-bottom: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 14px;
  border: 1px solid #eaeaea;
}

.modalSectionTitle {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
}

.modalMoodContainer {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.modalMoodText {
  font-size: 16px;
  color: #333;
  margin-left: 8px;
}

.modalSymptomItem {
  margin-bottom: 12px;
  border-left: 3px solid #ccc;
  padding-left: 8px;
}

.modalSymptomHeader {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.modalSymptomName {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.modalSeverityBadge {
  padding: 4px 8px;
  border-radius: 4px;
}

.modalSeverityText {
  font-size: 12px;
  color: #fff;
  font-weight: bold;
}

.modalSymptomDescription {
  font-size: 14px;
  color: #666;
}

.modalActivitiesContainer {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}

.modalActivityItem {
  display: flex;
  flex-direction: row;
  align-items: center;
  background-color: #eee;
  border-radius: 16px;
  padding: 6px 10px;
  margin-right: 8px;
  margin-bottom: 8px;
}

.modalActivityText {
  font-size: 14px;
  color: #333;
  margin-left: 4px;
}

.modalNotesText {
  font-size: 14px;
  color: #666;
  font-style: italic;
}
