import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Io<PERSON>earch, IoVideocam, IoPeople } from 'react-icons/io5';
import { ROLE_COLORS, COLORS } from '../../config/theme';
import { auth, db } from '../../config/firebase';
import { collection, query, where, getDocs } from 'firebase/firestore';
import '../../styles/chat/patientConsultationList.css';

const PatientConsultationList = () => {
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState('');
  const [patients, setPatients] = useState([]);
  const [loading, setLoading] = useState(true);

  // Fetch patients from Firebase
  useEffect(() => {
    const fetchPatients = async () => {
      try {
        setLoading(true);

        // Get current user (doctor)
        const currentUser = auth.currentUser;
        if (!currentUser) {
          console.error('No authenticated user found');
          setLoading(false);
          return;
        }

        // Get users collection from Firestore
        const usersCollection = collection(db, 'users');
        const usersSnapshot = await getDocs(usersCollection);

        if (usersSnapshot.empty) {
          console.log('No users found in Firestore');
          setPatients([]);
          setLoading(false);
          return;
        }

        // Filter users to get only patients linked to this doctor
        const patientsList = [];
        usersSnapshot.forEach((doc) => {
          const userData = doc.data();
          if (userData.role && userData.role.toLowerCase() === 'patient') {
            // Check if this patient is linked to the current doctor
            const linkedDoctors = userData.linkedDoctors || [];
            if (linkedDoctors.includes(currentUser.uid)) {
              patientsList.push({
                id: doc.id,
                firstName: userData.firstName || '',
                lastName: userData.lastName || '',
                age: userData.age || 0,
                condition: userData.condition || 'Not specified',
                lastConsultation: userData.lastVisit || new Date().toISOString().split('T')[0],
                status: Math.random() > 0.5 ? 'online' : 'offline', // Simulate online status randomly
                profileImage: userData.profileImage || null
              });
            }
          }
        });

        setPatients(patientsList);
      } catch (error) {
        console.error('Error fetching patients from Firebase:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchPatients();
  }, []);

  // Filter patients based on search query
  const filteredPatients = patients.filter(patient => {
    const fullName = `${patient.firstName} ${patient.lastName}`.toLowerCase();
    return fullName.includes(searchQuery.toLowerCase());
  });

  // Start consultation with a patient
  const startConsultation = (patient) => {
    // Ajouter un petit délai pour l'effet visuel
    setTimeout(() => {
      navigate('/chatroom', { state: { patientInfo: patient } });
    }, 150);
  };

  // Render patient item
  const renderPatientItem = (item) => {
    return (
      <div 
        className="patient-card"
        onClick={() => startConsultation(item)}
        key={item.id}
      >
        <div className="patient-info">
          <div className="avatar-container">
            {item.profileImage ? (
              <img src={item.profileImage} className="avatar" alt={`${item.firstName} ${item.lastName}`} />
            ) : (
              <div className="avatar-placeholder">
                <span className="avatar-text">
                  {item.firstName.charAt(0)}{item.lastName.charAt(0)}
                </span>
              </div>
            )}
            <div className={`status-indicator ${item.status === 'online' ? 'status-online' : 'status-offline'}`} />
          </div>

          <div className="patient-details">
            <div className="patient-name">
              {item.firstName} {item.lastName}
            </div>
            <div className="patient-condition">
              {item.condition}
            </div>
            <div className="patient-age">
              Age: {item.age}
            </div>
          </div>
        </div>

        <div className="consultation-info">
          <div className="last-consultation">
            Last: {new Date(item.lastConsultation).toLocaleDateString()}
          </div>

          <button
            className={`consult-button ${item.status === 'online' ? 'consult-button-active' : 'consult-button-disabled'}`}
            onClick={(e) => {
              e.stopPropagation();
              if (item.status === 'online') startConsultation(item);
            }}
            disabled={item.status !== 'online'}
          >
            <IoVideocam size={18} color="#fff" />
            <span className="consult-button-text">
              {item.status === 'online' ? 'Consult Now' : 'Offline'}
            </span>
          </button>
        </div>
      </div>
    );
  };

  return (
    <div className="container">
      <div className="gradient-background">
        <div className="header">
          <h1 className="title">Patient Consultations</h1>
          <p className="subtitle">Start a video consultation with your patients</p>
        </div>

        <div className="search-container">
          <IoSearch size={20} color="#757575" className="search-icon" />
          <input
            className="search-input"
            placeholder="Search patients..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        <div className="filter-container">
          <button className="filter-button active-filter">
            <span className="active-filter-text">All Patients</span>
          </button>

          <button className="filter-button">
            <span className="filter-text">Online</span>
          </button>

          <button className="filter-button">
            <span className="filter-text">Recent</span>
          </button>
        </div>

        {loading ? (
          <div className="loading-container">
            <div className="spinner"></div>
            <div className="loading-text">Loading patients...</div>
          </div>
        ) : (
          <div className="list-content">
            {filteredPatients.length > 0 ? (
              filteredPatients.map(patient => renderPatientItem(patient))
            ) : (
              <div className="empty-container">
                <IoPeople size={64} color="#ccc" />
                <div className="empty-text">No patients found</div>
                <div className="empty-sub-text">
                  Patients linked to you will appear here
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default PatientConsultationList;
