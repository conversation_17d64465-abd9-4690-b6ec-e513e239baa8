/* Footer styles */
.footer {
  background-color: var(--background-white);
  color: var(--text-medium);
  padding: 4rem 5% 2rem;
  border-radius: 20px 20px 0 0;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.footer-section h4, .footer-h4-title, .footer-h4-links, .footer-h4-contact {
  font-size: 1.2rem;
  margin-bottom: 1.5rem;
  color: var(--text-dark);
}

.footer-section ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-section ul li {
  margin-bottom: 0.8rem;
}

.footer-section a {
  color: var(--text-medium);
  text-decoration: none;
  transition: color 0.3s;
}

.footer-section a:hover {
  color: var(--primary);
}

.footer-p-tagline, .footer-p-email, .footer-p-phone {
  margin-bottom: 0.5rem;
}

.footer-bottom {
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  padding-top: 1.5rem;
  text-align: center;
  font-size: 0.9rem;
  color: var(--text-light);
}

.footer-p-copyright {
  margin: 0;
}

/* Responsive design for footer */
@media (max-width: 768px) {
  .footer {
    border-radius: 0;
  }
}

@media (max-width: 480px) {
  .footer-content {
    grid-template-columns: 1fr;
  }

  .footer-section {
    margin-bottom: 2rem;
  }
}

/* Accessibility improvements */
.footer-section a:focus {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}
