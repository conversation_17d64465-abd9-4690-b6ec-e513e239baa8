.scrollView {
  flex: 1;
  background-color: #f5f7fa;
  overflow-y: auto;
  height: 100%;
}

.headerSection {
  width: 100%;
  margin-bottom: 20px;
}

.headerGradient {
  padding: 20px 15px;
  border-bottom-left-radius: 20px;
  border-bottom-right-radius: 20px;
  background: linear-gradient(to right bottom, var(--primary-light), #f5f5f5);
}

.headerContent {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
}

.headerButtonContainer {
  margin-left: 5px;
}

.contentSection {
  padding: 0 16px 20px 16px;
}

.welcomeContainer {
  flex: 1;
  margin-right: 5px;
}

.welcomeText {
  font-size: 22px;
  font-weight: bold;
  color: white;
  margin-bottom: 5px;
}

.dateText {
  font-size: 13px;
  color: rgba(255, 255, 255, 0.9);
  margin-top: 4px;
}

.sectionHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
  margin-bottom: 8px;
}

.sectionTitle {
  font-size: 20px;
  font-weight: bold;
  color: #343a40;
  margin-bottom: 12px;
  letter-spacing: 0.3px;
}

.statsContainer {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding: 8px;
  margin-bottom: 16px;
}

.chartCard {
  margin-bottom: 24px;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
  background-color: #ffffff;
  border-radius: 16px;
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.03);
}

.chartTitle {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.chartSubtitle {
  font-size: 14px;
  color: #666;
}

.chartContent {
  padding: 10px 5px;
}

.reportContainer {
  padding: 16px;
  margin-bottom: 16px;
}

.quickActions {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.actionButton {
  width: 48%;
  background-color: #fff;
  padding: 16px;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.03);
  cursor: pointer;
  text-decoration: none;
}

.actionIconContainer {
  width: 56px;
  height: 56px;
  border-radius: 28px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 8px;
}

.actionText {
  margin-top: 8px;
  color: #333;
  font-weight: 600;
  font-size: 14px;
  text-align: center;
}

.upcomingSection {
  margin-bottom: 24px;
  padding: 0 16px;
}

.scheduleList {
  border-radius: 16px;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(0, 0, 0, 0.03);
}

.spacer {
  height: 30px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .actionButton {
    width: 100%;
  }
  
  .statsContainer > div {
    width: 100% !important;
    margin-bottom: 12px;
  }
}
