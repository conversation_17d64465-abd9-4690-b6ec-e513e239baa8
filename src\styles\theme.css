/* Theme CSS variables */
:root {
  /* Success, error, warning, info colors */
  --color-success: #4CAF50;
  --color-error: #F44336;
  --color-warning: #FFA726;
  --color-info: #29B6F6;
  
  /* Text colors */
  --color-text-dark: #212121;
  --color-text-medium: #757575;
  --color-text-light: #BDBDBD;
  
  /* Background and surface colors */
  --color-background: #f5f7fa;
  --color-surface: #FFFFFF;
  --color-border: #E0E0E0;
  
  /* Default theme (admin) */
  --color-primary: rgba(16, 107, 0, 1);
  --color-primary-light: rgba(16, 107, 0, 0.8);
  --color-primary-lighter: rgba(16, 107, 0, 0.1);
  
  /* Backdrop */
  --color-backdrop: rgba(0, 0, 0, 0.5);
}

/* Role-specific theme classes */
.theme-patient {
  --color-primary: rgba(255, 149, 43, 1);
  --color-primary-light: rgba(255, 149, 43, 0.8);
  --color-primary-lighter: rgba(255, 149, 43, 0.1);
}

.theme-doctor {
  --color-primary: rgba(170, 86, 255, 1);
  --color-primary-light: rgba(170, 86, 255, 0.8);
  --color-primary-lighter: rgba(170, 86, 255, 0.1);
}

.theme-supervisor {
  --color-primary: rgb(255, 0, 242);
  --color-primary-light: rgba(255, 0, 242, 0.8);
  --color-primary-lighter: rgba(255, 0, 242, 0.1);
}

.theme-caregiver {
  --color-primary: rgba(0, 169, 255, 1);
  --color-primary-light: rgba(0, 169, 255, 0.8);
  --color-primary-lighter: rgba(0, 169, 255, 0.1);
}

.theme-admin {
  --color-primary: rgba(16, 107, 0, 1);
  --color-primary-light: rgba(16, 107, 0, 0.8);
  --color-primary-lighter: rgba(16, 107, 0, 0.1);
}
