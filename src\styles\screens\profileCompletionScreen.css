/* ProfileCompletionScreen Styles */

.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #fff;
  padding: 0;
  margin: 0;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #fff;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #106b00;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.header {
  font-size: 24px;
  font-weight: bold;
  color: #212121;
  margin-top: 20px;
  margin-bottom: 8px;
  padding-left: 20px;
  padding-right: 20px;
  margin-left: 0;
  margin-right: 0;
}

.subheader {
  font-size: 16px;
  color: #757575;
  margin-bottom: 20px;
  padding-left: 20px;
  padding-right: 20px;
  margin-top: 0;
  line-height: 1.4;
}

/* Responsive design */
@media (max-width: 768px) {
  .container {
    padding: 0 10px;
  }
  
  .header {
    font-size: 20px;
    padding-left: 15px;
    padding-right: 15px;
  }
  
  .subheader {
    font-size: 14px;
    padding-left: 15px;
    padding-right: 15px;
  }
}

@media (max-width: 480px) {
  .header {
    font-size: 18px;
    padding-left: 10px;
    padding-right: 10px;
  }
  
  .subheader {
    font-size: 13px;
    padding-left: 10px;
    padding-right: 10px;
  }
}
