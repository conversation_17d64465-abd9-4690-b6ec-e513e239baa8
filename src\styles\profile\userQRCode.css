.container {
  flex: 1;
  background-color: #f5f5f5;
}

.contentContainer {
  padding: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.card {
  width: 100%;
  margin-bottom: 16px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  background-color: white;
}

.cardContent {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px;
}

.qrContainer {
  padding: 24px;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin: 20px 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.codeContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 24px;
  width: 100%;
}

.codeLabel {
  font-size: 16px;
  color: #666;
  margin-bottom: 8px;
}

.codeText {
  font-size: 28px;
  font-weight: bold;
  letter-spacing: 2px;
  color: rgba(16, 107, 0, 1);
  margin-bottom: 8px;
  text-align: center;
}

.codeDescription {
  font-size: 14px;
  color: #777;
  text-align: center;
  padding: 0 16px;
}

.shareButton {
  background-color: rgba(16, 107, 0, 1);
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 12px 24px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  border: none;
}

.shareButton:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.shareButtonText {
  color: white;
  font-weight: bold;
  font-size: 16px;
  margin-left: 8px;
}

.infoCard {
  background-color: #fff;
}

.infoTitle {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 16px;
  color: rgba(16, 107, 0, 0.8);
}

.infoItem {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 16px;
}

.infoIcon {
  margin-right: 12px;
  color: rgba(16, 107, 0, 1);
}

.infoText {
  font-size: 14px;
  color: #555;
  flex: 1;
}

.errorIcon {
  background-color: rgba(16, 107, 0, 1);
  margin-bottom: 16px;
  color: #fff;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 30px;
}

.errorText {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 8px;
  color: #555;
}

.errorSubtext {
  font-size: 14px;
  color: #777;
  text-align: center;
}

.placeholderQR {
  padding: 24px;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin: 20px 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.qrCodeSize {
  width: 60vw;
  height: 60vw;
  max-width: 300px;
  max-height: 300px;
}

/* Spinner for loading state */
.spinner {
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top: 3px solid white;
  width: 20px;
  height: 20px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
