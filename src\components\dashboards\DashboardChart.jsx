import React from 'react';
import { Bar, Line, Pie } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import '../../styles/dashboards/dashboardchart.css';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend
);

const DashboardChart = ({
  type = 'line',
  data = [],
  labels = [],
  title,
  subtitle,
  height = 220,
  width = '100%',
  backgroundColor = '#fff',
  chartConfig = {},
  color = 'rgba(29, 85, 168, 1)',
  style,
}) => {
  // Ensure data is valid
  const safeData = Array.isArray(data.datasets?.[0]?.data) 
    ? data.datasets[0].data 
    : Array.isArray(data) 
      ? data 
      : [];
  
  const safeLabels = Array.isArray(data.labels) 
    ? data.labels 
    : Array.isArray(labels) 
      ? labels 
      : [];
  
  // Make sure we have at least one data point
  if (safeData.length === 0) {
    safeData.push(0);
  }
  
  // Make sure we have labels for each data point
  while (safeLabels.length < safeData.length) {
    safeLabels.push(`Item ${safeLabels.length + 1}`);
  }

  // Default chart configuration
  const defaultOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: true,
        position: 'top',
      },
      tooltip: {
        enabled: true,
      },
    },
    scales: {
      y: {
        beginAtZero: true,
      },
    },
  };

  // Format data for Chart.js
  const formatChartData = () => {
    // If data is already in Chart.js format, use it
    if (data.datasets) {
      return data;
    }

    // For line and bar charts
    if (type === 'line' || type === 'bar') {
      return {
        labels: safeLabels,
        datasets: [
          {
            label: data.legend?.[0] || title || 'Data',
            data: safeData,
            backgroundColor: color.replace('1)', '0.2)'),
            borderColor: color,
            borderWidth: 2,
            tension: type === 'line' ? 0.4 : 0, // Add bezier curve for line charts
          },
        ],
      };
    }

    // For pie charts
    if (type === 'pie') {
      return {
        labels: safeLabels,
        datasets: [
          {
            data: safeData,
            backgroundColor: safeData.map((_, index) => 
              `rgba(${Math.floor(Math.random() * 255)}, ${Math.floor(Math.random() * 255)}, ${Math.floor(Math.random() * 255)}, 0.7)`
            ),
            borderColor: safeData.map((_, index) => 
              `rgba(${Math.floor(Math.random() * 255)}, ${Math.floor(Math.random() * 255)}, ${Math.floor(Math.random() * 255)}, 1)`
            ),
            borderWidth: 1,
          },
        ],
      };
    }

    return {
      labels: safeLabels,
      datasets: [
        {
          label: 'Data',
          data: safeData,
          backgroundColor: 'rgba(75, 192, 192, 0.2)',
          borderColor: 'rgba(75, 192, 192, 1)',
          borderWidth: 1,
        },
      ],
    };
  };

  const chartData = formatChartData();
  const options = { ...defaultOptions, ...chartConfig };

  const renderChart = () => {
    try {
      switch (type) {
        case 'line':
          return <Line data={chartData} options={options} height={height} />;
        case 'bar':
          return <Bar data={chartData} options={options} height={height} />;
        case 'pie':
          return <Pie data={chartData} options={options} height={height} />;
        default:
          return <div className="chart-error">Unsupported chart type</div>;
      }
    } catch (error) {
      console.error('Chart rendering error:', error);
      return (
        <div className="chart-error">
          <p>Unable to display chart</p>
        </div>
      );
    }
  };

  return (
    <div 
      className="dashboard-chart"
      style={{ 
        backgroundColor, 
        ...style 
      }}
    >
      {(title || subtitle) && (
        <div className="chart-header">
          {title && <h3 className="chart-title">{title}</h3>}
          {subtitle && <p className="chart-subtitle">{subtitle}</p>}
        </div>
      )}
      <div 
        className="chart-container"
        style={{ height: `${height}px` }}
      >
        {renderChart()}
      </div>
    </div>
  );
};

export default DashboardChart;
