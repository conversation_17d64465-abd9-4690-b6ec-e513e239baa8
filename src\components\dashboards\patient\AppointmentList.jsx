import React, { useState, useEffect } from 'react';
import { format } from 'date-fns';
import axios from 'axios';
import { API_URL } from '../../../../config/constants';
import '../../../styles/dashboards/patient/appointmentList.css';

const ITEMS_PER_PAGE = 10;

const AppointmentList = ({ token, onAppointmentSelected }) => {
  const [appointments, setAppointments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [page, setPage] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [selectedAppointment, setSelectedAppointment] = useState(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);

  useEffect(() => {
    fetchAppointments();
  }, [page]);

  const fetchAppointments = async () => {
    try {
      setLoading(true);
      const response = await axios.get(`${API_URL}/api/appointments/patient`, {
        headers: { Authorization: `Bearer ${token}` },
        params: {
          page,
          limit: ITEMS_PER_PAGE,
        },
      });
      setAppointments(response.data.appointments);
      setTotalPages(Math.ceil(response.data.total / ITEMS_PER_PAGE));
    } catch (error) {
      console.error('Error fetching appointments:', error);
      alert('Failed to fetch appointments');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    setPage(0);
    await fetchAppointments();
    setRefreshing(false);
  };

  const handleCancelAppointment = async (appointmentId) => {
    try {
      setLoading(true);
      await axios.post(
        `${API_URL}/api/appointments/${appointmentId}/cancel`,
        { reason: 'Canceled by patient' },
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );
      await fetchAppointments();
      setShowDetailsModal(false);
      alert('Appointment canceled successfully');
    } catch (error) {
      console.error('Error canceling appointment:', error);
      alert('Failed to cancel appointment');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return '#FFA500';
      case 'confirmed':
        return '#4CAF50';
      case 'cancelled':
        return '#F44336';
      case 'completed':
        return '#2196F3';
      default:
        return '#757575';
    }
  };

  const renderAppointmentCard = (appointment) => (
    <div 
      key={appointment.id}
      className="card"
      onClick={() => {
        setSelectedAppointment(appointment);
        setShowDetailsModal(true);
        if (onAppointmentSelected) {
          onAppointmentSelected(appointment);
        }
      }}
    >
      <div className="card-content">
        <div className="cardHeader">
          <h2>{format(new Date(appointment.date), 'MMMM dd, yyyy')}</h2>
          <span 
            className="statusChip"
            style={{ backgroundColor: getStatusColor(appointment.status) }}
          >
            {appointment.status.toUpperCase()}
          </span>
        </div>
        <p>Time: {appointment.time}</p>
        <p>Type: {appointment.type}</p>
        <p>Doctor: {appointment.doctor.displayName}</p>
      </div>
    </div>
  );

  const renderDetailsModal = () => (
    showDetailsModal && (
      <div className="modalBackdrop" onClick={() => setShowDetailsModal(false)}>
        <div className="modal" onClick={(e) => e.stopPropagation()}>
          {selectedAppointment && (
            <div>
              <h2>Appointment Details</h2>
              <div className="divider" />

              <div className="listItem">
                <div className="listItemTitle">Date</div>
                <div className="listItemDescription">
                  {format(new Date(selectedAppointment.date), 'MMMM dd, yyyy')}
                </div>
              </div>
              
              <div className="listItem">
                <div className="listItemTitle">Time</div>
                <div className="listItemDescription">{selectedAppointment.time}</div>
              </div>
              
              <div className="listItem">
                <div className="listItemTitle">Type</div>
                <div className="listItemDescription">{selectedAppointment.type}</div>
              </div>
              
              <div className="listItem">
                <div className="listItemTitle">Doctor</div>
                <div className="listItemDescription">{selectedAppointment.doctor.displayName}</div>
              </div>
              
              <div className="listItem">
                <div className="listItemTitle">Reason</div>
                <div className="listItemDescription">
                  {selectedAppointment.reason || 'No reason provided'}
                </div>
              </div>

              <div className="divider" />

              {(selectedAppointment.status === 'pending' || selectedAppointment.status === 'confirmed') && (
                <button
                  className="cancelButton"
                  onClick={() => handleCancelAppointment(selectedAppointment.id)}
                >
                  Cancel Appointment
                </button>
              )}
            </div>
          )}
        </div>
      </div>
    )
  );

  if (loading && !refreshing) {
    return (
      <div className="loadingContainer">
        <div className="spinner"></div>
      </div>
    );
  }

  return (
    <div className="container">
      <div>
        <button onClick={handleRefresh} disabled={refreshing}>
          {refreshing ? 'Refreshing...' : 'Refresh'}
        </button>
        
        {appointments.length === 0 ? (
          <p className="noAppointments">No appointments found</p>
        ) : (
          <>
            {appointments.map(renderAppointmentCard)}
            <div className="pagination">
              {Array.from({ length: totalPages }, (_, i) => (
                <button
                  key={i}
                  onClick={() => setPage(i)}
                  disabled={page === i}
                  style={{
                    margin: '0 4px',
                    padding: '4px 8px',
                    backgroundColor: page === i ? '#e0e0e0' : 'transparent',
                  }}
                >
                  {i + 1}
                </button>
              ))}
            </div>
          </>
        )}
      </div>
      {renderDetailsModal()}
    </div>
  );
};

export default AppointmentList;
