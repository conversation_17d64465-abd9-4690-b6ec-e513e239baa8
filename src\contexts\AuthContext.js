import React, { createContext, useState, useContext, useEffect } from 'react';
import {
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  sendPasswordResetEmail,
  sendEmailVerification,
  signOut,
  onAuthStateChanged
} from 'firebase/auth';
import { getAuth, EmailAuthProvider, reauthenticateWithCredential, updatePassword } from 'firebase/auth';
import { doc, setDoc, getDoc } from 'firebase/firestore';
import {
  auth,
  db,
  updateProfile
} from '../config/firebase';

// Function to generate a unique 8-character alphanumeric code
const generateUserCode = () => {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let code = '';
  for (let i = 0; i < 8; i++) {
    code += characters.charAt(Math.floor(Math.random() * characters.length));
  }
  return code;
};

const AuthContext = createContext({});

export const useAuth = () => useContext(AuthContext);

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [isRegistering, setIsRegistering] = useState(false);

  // Register new user
  const register = async (email, password, displayName, role = 'patient') => {
    try {
      setIsRegistering(true);

      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        throw new Error('Invalid email format');
      }

      // Create user in Firebase Auth
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      const newUser = userCredential.user;

      // Update profile
      await updateProfile(newUser, { displayName });

      // Generate a unique user code
      const userCode = generateUserCode();

      // Prepare user data
      const userData = {
        uid: newUser.uid,
        email,
        firstName: displayName.split(' ')[0],
        lastName: displayName.split(' ').slice(1).join(' '),
        displayName,
        emailVerified: false,
        createdAt: new Date().toISOString(),
        role: role,
        userCode,
        userCodeGenerated: new Date().toISOString(),
        profileCompleted: false,
      };

      // Try to save to Firestore, but continue even if it fails
      try {
        // Check if db is properly initialized before using it
        if (db && typeof db !== 'undefined') {
          // Wrap Firestore operations in a try/catch block
          const userRef = doc(db, 'users', newUser.uid);
          await setDoc(userRef, userData).catch(e => {
            console.log("Ignoring Firestore error:", e.message);
          });
        } else {
          console.log("Skipping Firestore operations: database not initialized");
        }
      } catch (firestoreError) {
        // Just log the error but don't let it fail the registration process
        console.error("Firestore error during registration (caught):", firestoreError);
      }

      // Immediately sign out the user
      await signOut(auth);

      // Reset the registration flag
      setIsRegistering(false);

      return { ...userData, uid: newUser.uid };
    } catch (error) {
      setIsRegistering(false);
      console.error("Registration error:", error);

      if (error.code === 'auth/email-already-in-use') {
        throw new Error('This email is already registered. Please try logging in instead.');
      }
      if (error.code === 'auth/invalid-email') {
        throw new Error('Please enter a valid email address.');
      }
      if (error.code === 'auth/weak-password') {
        throw new Error('Password should be at least 6 characters long.');
      }
      throw error;
    }
  };

  // Login
  const login = async (email, password) => {
    try {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        throw { code: 'auth/invalid-email', message: 'Invalid email format' };
      }

      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      const currentUser = userCredential.user;

      try {
        // Wrap Firestore operations in a try/catch to handle potential errors
        const userRef = doc(db, 'users', currentUser.uid);
        const userSnap = await getDoc(userRef).catch(e => {
          console.log("Ignoring Firestore error during login:", e.message);
          return { exists: () => false, data: () => ({}) };
        });

        if (userSnap.exists && userSnap.exists()) {
          const userData = userSnap.data();
          setUser({ ...currentUser, ...userData });
          return { ...currentUser, ...userData };
        }
      } catch (firestoreError) {
        console.warn("Error accessing Firestore during login:", firestoreError);
        // Continue with just the auth user if Firestore fails
      }

      // If we reach here, either Firestore failed or the user doc doesn't exist
      setUser(currentUser);
      return currentUser;
    } catch (error) {
      throw error;
    }
  };

  // Logout
  const logout = async () => {
    try {
      await signOut(auth);
      setUser(null);
    } catch (error) {
      throw new Error('Failed to log out. Please try again.');
    }
  };

  // Password reset
  const resetPassword = async (email) => {
    try {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        throw new Error('Please enter a valid email address.');
      }
      await sendPasswordResetEmail(auth, email);
    } catch (error) {
      if (error.code === 'auth/user-not-found') {
        throw new Error('No account found with this email address.');
      }
      throw error;
    }
  };

  // Update user profile
  const updateUserProfile = async (data) => {
    try {
      const currentUser = auth.currentUser;
      if (!currentUser) {
        throw new Error('You must be logged in to update your profile.');
      }

      // Update Firebase Auth profile if displayName or photoURL is provided
      if (data.displayName || data.photoURL) {
        try {
          const authUpdateData = {};
          if (data.displayName) authUpdateData.displayName = data.displayName;
          if (data.photoURL) authUpdateData.photoURL = data.photoURL;

          await updateProfile(currentUser, authUpdateData);
        } catch (authError) {
          console.warn('Failed to update auth profile, but will continue with Firestore update:', authError);
        }
      }

      // Try to update Firestore - this is the most important part
      try {
        const userRef = doc(db, 'users', currentUser.uid);
        await setDoc(userRef, data, { merge: true });

        // Get the updated user data
        const userSnap = await getDoc(userRef);
        if (userSnap.exists()) {
          const updatedUser = { ...currentUser, ...userSnap.data() };
          setUser(updatedUser);
          return updatedUser; // Return the updated user data
        }
      } catch (firestoreError) {
        console.error('Firestore update failed:', firestoreError);
        throw new Error('Failed to update profile in the database. Please try again.');
      }
    } catch (error) {
      console.error('Profile update error:', error);
      throw error;
    }
  };

  // Change password
  const changePassword = async (currentPassword, newPassword) => {
    try {
      // Get the current user directly from auth
      const user = auth.currentUser;
      if (!user) {
        throw new Error('You must be logged in to change your password.');
      }

      // Validate new password
      if (newPassword.length < 6) {
        throw new Error('New password should be at least 6 characters long.');
      }

      // Re-authenticate user before changing password
      try {
        console.log("Creating credential for email:", user.email);

        // Create credential
        const credential = EmailAuthProvider.credential(
          user.email,
          currentPassword
        );

        console.log("Credential created, attempting reauthentication");

        // Reauthenticate with the credential
        await reauthenticateWithCredential(user, credential);

        console.log("Reauthentication successful");
      } catch (reauthError) {
        console.error('Reauthentication failed:', reauthError);
        if (reauthError.code === 'auth/wrong-password') {
          throw new Error('Current password is incorrect.');
        }
        throw new Error('Authentication failed. Please try again.');
      }

      // Update password
      console.log("Updating password");
      await updatePassword(user, newPassword);
      console.log("Password updated successfully");

      return { success: true };
    } catch (error) {
      console.error('Change password error:', error);
      throw error;
    }
  };

  // Listen to auth state changes
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (authUser) => {
      try {
        // If we're in registration process, don't update user state
        if (isRegistering) {
          console.log('Ignoring auth state change during registration');
          setLoading(false);
          return;
        }

        if (authUser) {
          // Process auth state when not registering
          try {
            const userRef = doc(db, 'users', authUser.uid);
            const userSnap = await getDoc(userRef).catch(e => {
              console.log("Ignoring Firestore error in auth state change:", e.message);
              return { exists: () => false, data: () => ({}) };
            });

            if (userSnap.exists && userSnap.exists()) {
              setUser({ ...authUser, ...userSnap.data() });
            } else {
              setUser(authUser);
            }
          } catch (firestoreError) {
            console.warn("Error accessing Firestore in auth state change:", firestoreError);
            setUser(authUser); // Fall back to just auth user
          }
        } else {
          setUser(null);
        }
      } catch (error) {
        console.warn('Error in auth state change:', error);
        if (!isRegistering) setUser(authUser); // Fall back to basic auth user data only if not registering
      } finally {
        setLoading(false);
      }
    });

    return unsubscribe;
  }, [isRegistering]); // Add isRegistering to dependencies

  const value = {
    user,
    loading,
    login,
    register,
    logout,
    resetPassword,
    updateUserProfile,
    changePassword,
    isRegistering // Export this state
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export default AuthContext;
