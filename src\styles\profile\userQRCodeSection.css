.card {
  margin-bottom: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.titleContainer {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.title {
  font-size: 20px;
  font-weight: 600;
}

.userInfo {
  margin-top: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.username {
  font-size: 18px;
  font-weight: bold;
  margin-top: 10px;
}

.userRole {
  font-size: 14px;
  color: #666;
  margin-top: 4px;
}

.userEmail {
  font-size: 14px;
  color: #666;
  margin-top: 4px;
  text-align: center;
}

.codeContainer {
  margin-top: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.codeLabel {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.codeBlock {
  border-width: 2px;
  border-style: solid;
  border-radius: 8px;
  padding: 10px 20px;
  margin-bottom: 12px;
}

.codeText {
  font-size: 22px;
  font-weight: bold;
  letter-spacing: 2px;
}

.codeDescription {
  text-align: center;
  font-size: 12px;
  color: #666;
  padding: 0 20px;
}

.viewQrButton {
  padding: 6px 12px;
  border-radius: 6px;
  cursor: pointer;
}

.viewQrButtonText {
  color: white;
  font-size: 12px;
  font-weight: 600;
}

.actions {
  display: flex;
  justify-content: center;
  padding-top: 8px;
  padding-bottom: 16px;
}

.avatar {
  width: 70px;
  height: 70px;
  border-radius: 35px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 24px;
  font-weight: bold;
  color: white;
}

.cardContent {
  padding: 16px;
}

.cardActions {
  padding: 8px 16px 16px;
}

.button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  color: white;
  font-weight: 600;
  cursor: pointer;
}
