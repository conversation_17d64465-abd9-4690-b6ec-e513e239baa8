import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { CssVarsProvider, extendTheme, useColorScheme } from '@mui/joy/styles';
import GlobalStyles from '@mui/joy/GlobalStyles';
import CssBaseline from '@mui/joy/CssBaseline';
import Box from '@mui/joy/Box';
import Button from '@mui/joy/Button';
import Checkbox from '@mui/joy/Checkbox';
import Divider from '@mui/joy/Divider';
import FormControl from '@mui/joy/FormControl';
import FormLabel from '@mui/joy/FormLabel';
import IconButton from '@mui/joy/IconButton';
import Link from '@mui/joy/Link';
import Input from '@mui/joy/Input';
import Typography from '@mui/joy/Typography';
import Stack from '@mui/joy/Stack';
import DarkModeRoundedIcon from '@mui/icons-material/DarkModeRounded';
import LightModeRoundedIcon from '@mui/icons-material/LightModeRounded';
import GoogleIcon from '../icons/GoogleIcon';
import '../../../styles/auth/auth.css';

function ColorSchemeToggle(props) {
  const { onClick, ...other } = props;
  const { mode, setMode } = useColorScheme();
  const [mounted, setMounted] = React.useState(false);

  React.useEffect(() => setMounted(true), []);

  return (
    <IconButton
      aria-label="toggle light/dark mode"
      size="sm"
      variant="outlined"
      disabled={!mounted}
      onClick={(event) => {
        setMode(mode === 'light' ? 'dark' : 'light');
        onClick?.(event);
      }}
      {...other}
    >
      {mode === 'light' ? <DarkModeRoundedIcon /> : <LightModeRoundedIcon />}
    </IconButton>
  );
}

const customTheme = extendTheme({ defaultColorScheme: 'dark' });

export default function Login() {

  // Initialize navigate hook
    const navigate = useNavigate();

    // States to manage form data
    const [email, setEmail] = useState('');
    const [password, setPassword] = useState('');
    const [rememberMe, setRememberMe] = useState(false);
    const [loading, setLoading] = useState(false);

    // Check for saved email on component mount
      useEffect(() => {
        const checkSavedEmail = () => {
          try {
            const savedEmail = localStorage.getItem('userEmail');
            if (savedEmail) {
              setEmail(savedEmail);
              setRememberMe(true);
            }
          } catch (error) {
            console.error('Error checking saved email:', error);
          }
        };

        checkSavedEmail();
      }, []);

      // Function to handle login
      const handleLogin = (e) => {
        e.preventDefault();

        if (!email.trim() || !password.trim()) {
          alert('Please enter both email and password');
          return;
        }

        setLoading(true);

        // Simulate login request
        setTimeout(() => {
          // Save email to localStorage if remember me is checked
          if (rememberMe) {
            localStorage.setItem('userEmail', email);
          } else {
            localStorage.removeItem('userEmail');
          }

          // Show success message
          console.log('Login successful');

          // Simulate navigation after successful login
          setLoading(false);
          alert('Login successful!');

          // Navigate to dashboard after successful login
          navigate('/')
        }, 1500);
      };

      // Function to navigate to signup page
      const navigateToSignup = () => {
        console.log('Redirecting to signup page');
        navigate('/signup');
      };

      // Function to navigate to forgot password page
      const navigateToForgotPassword = () => {
        console.log('Redirecting to forgot password page');
        navigate('/forgot-password');
      };


  return (
    <CssVarsProvider theme={customTheme} disableTransitionOnChange>
      <CssBaseline />
      <GlobalStyles
        styles={{
          ':root': {
            '--Form-maxWidth': '800px',
            '--Transition-duration': '0.4s', // set to `none` to disable transition
          },
        }}
      />
      <Box
        sx={(theme) => ({
          width: { xs: '100%', md: '50vw' },
          transition: 'width var(--Transition-duration)',
          transitionDelay: 'calc(var(--Transition-duration) + 0.1s)',
          position: 'relative',
          zIndex: 1,
          display: 'flex',
          justifyContent: 'flex-end',
          backdropFilter: 'blur(12px)',
          backgroundColor: 'rgba(255 255 255 / 0.2)',
          [theme.getColorSchemeSelector('dark')]: {
            backgroundColor: 'rgba(19 19 24 / 0.4)',
          },
        })}
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            minHeight: '100dvh',
            width: '100%',
            px: 2,
          }}
        >
          <Box
            component="header"
            sx={{ py: 3, display: 'flex', justifyContent: 'space-between' }}
          >
            <Box sx={{ gap: 2, display: 'flex', alignItems: 'center' }}>
              <img src='../../../../assets/LogoNeuroCare/Neuro_Care_rounded_3.png' alt="Logo" width={50} height={50}/>
              <Typography level="title-lg" className="brand-title">Neur Care</Typography>
            </Box>
            <ColorSchemeToggle />
          </Box>
          <Box
            component="main"
            sx={{
              my: 'auto',
              py: 2,
              pb: 5,
              display: 'flex',
              flexDirection: 'column',
              gap: 2,
              width: 400,
              maxWidth: '100%',
              mx: 'auto',
              borderRadius: 'sm',
              '& form': {
                display: 'flex',
                flexDirection: 'column',
                gap: 2,
              },
              [`& .MuiFormLabel-asterisk`]: {
                visibility: 'hidden',
              },
            }}
          >
            <Stack sx={{ gap: 4, mb: 2 }}>
              <Stack sx={{ gap: 1 }}>
                <Typography component="h1" level="h3">
                  Login
                </Typography>
                <Typography level="body-sm">
                Don't have an account?{' '}
                  <Link
                    onClick={navigateToSignup}
                    level="title-sm"
                    className="auth-link"
                  >
                    Sign up!
                  </Link>
                </Typography>
              </Stack>
              <Button
                variant="soft"
                color="neutral"
                fullWidth
                startDecorator={<GoogleIcon />}
              >
                Continue with Google
              </Button>
            </Stack>
            <Divider
              sx={(theme) => ({
                [theme.getColorSchemeSelector('light')]: {
                  color: { xs: '#FFF', md: 'text.tertiary' },
                },
              })}
            >
              or
            </Divider>
            <Stack sx={{ gap: 4, mt: 2 }}>
              <form
                onSubmit={handleLogin}
              >
                <FormControl required>
                  <FormLabel>Email</FormLabel>
                  <Input type="email" name="email" value={email} onChange={(e) => setEmail(e.target.value)} />
                </FormControl>
                <FormControl required>
                  <FormLabel>Password</FormLabel>
                  <Input type="password" name="password" value={password} onChange={(e) => setPassword(e.target.value)} />
                </FormControl>
                <Stack sx={{ gap: 4, mt: 2 }}>
                  <Box
                    sx={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                    }}
                  >
                    <Checkbox size="sm" label="Remember me" name="persistent" />
                    <Link
                      level="title-sm"
                      onClick={navigateToForgotPassword}
                      className="auth-link"
                    >
                      Forgot your password?
                    </Link>
                  </Box>
                  <button
                    type="submit"
                    className="auth-button"
                    disabled={loading}
                  >
                    <span style={{ position: 'relative', zIndex: 2 }}>
                      {loading ? 'Logging in...' : 'Login'}
                    </span>
                    <span className="button-overlay" />
                  </button>
                </Stack>
              </form>
            </Stack>
          </Box>
          <Box component="footer" sx={{ py: 3 }}>
            <Typography level="body-xs" sx={{ textAlign: 'center' }}>
              © Your company {new Date().getFullYear()}
            </Typography>
          </Box>
        </Box>
      </Box>
      <Box
        sx={(theme) => ({
          height: '100%',
          position: 'fixed',
          right: 0,
          top: 0,
          bottom: 0,
          left: { xs: 0, md: '50vw' },
          transition:
            'background-image var(--Transition-duration), left var(--Transition-duration) !important',
          transitionDelay: 'calc(var(--Transition-duration) + 0.1s)',
          backgroundColor: 'background.level1',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
          backgroundImage:
            'url(../../../../assets/Backgrounds/pexels-pixabay-40568.jpg)',
          [theme.getColorSchemeSelector('dark')]: {
            backgroundImage:
              'linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url(../../../../assets/Backgrounds/pexels-pixabay-40568.jpg)',
          },
        })}
      />

    </CssVarsProvider>
  );
}
