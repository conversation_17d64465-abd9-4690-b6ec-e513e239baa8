:root {
    --primary-green: #106b00;
    --primary-green-transparent: rgba(16, 107, 0, 0.895);
    --primary-green-hover: rgba(18, 121, 0, 0.771);
  }

  .brand-title {
    color: var(--primary-green) !important;
    font-weight: 600;
  }

  .auth-button {
    width: 100%;
    height: 50px;
    background-color: var(--primary-green-transparent) !important; /* Vert clair */
    color: #fff !important;
    border: none;
    border-radius: 40px;
    font-size: 1rem;
    font-weight: bold;
    cursor: pointer;
    margin-top: 1rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }

  .auth-button:hover {
    background-color: var(--primary-green-hover) !important; /* Vert légèrement plus foncé */
  }

  .auth-button:hover .button-overlay {
    opacity: 1 !important;
  }

  .auth-button:disabled {
    background-color: #C5E1A5 !important; /* Vert très clair */
    color: #689F38 !important; /* Vert foncé */
    cursor: not-allowed;
    opacity: 0.8;
  }

  .button-overlay {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.1);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1;
  }


  /* Loading overlay */
  .loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(16, 107, 0, 0.7);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 1000;
  }

  .loading-spinner {
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top: 4px solid #fff;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
  }

  .loading-text {
    color: #fff;
    margin-top: 1rem;
    font-size: 1rem;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  /* Styles pour les liens */
  .auth-link {
    cursor: pointer;
    color: var(--primary-green-transparent) !important;
    text-decoration: none;
    transition: color 0.3s ease;
  }

  .auth-link:hover {
    color: var(--primary-green) !important;
    text-decoration: underline;
  }

  /* Styles pour supprimer la bordure verte des composants Select */
  .MuiSelect-root:focus-visible {
    outline: none !important;
    border-color: transparent !important;
    box-shadow: none !important;
  }

  .MuiSelect-root.Mui-focused {
    outline: none !important;
    border-color: transparent !important;
    box-shadow: none !important;
  }

  /* Styles pour les options du Select */
  .MuiOption-root {
    transition: background-color 0.2s ease;
  }

  .MuiOption-root.Mui-selected {
    background-color: rgba(16, 107, 0, 0.1) !important;
  }

  .MuiOption-root:hover {
    background-color: rgba(16, 107, 0, 0.05) !important;
  }

  .MuiOption-root:focus-visible {
    outline: none !important;
    border-color: transparent !important;
    box-shadow: none !important;
  }

  /* Responsive styles */
  @media (max-width: 768px) {
    .login-title {
      font-size: 2rem;
    }

    .login-form {
      max-width: 100%;
    }
  }
