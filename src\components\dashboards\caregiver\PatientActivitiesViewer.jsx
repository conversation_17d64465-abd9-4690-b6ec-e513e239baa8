import React, { useState, useEffect } from 'react';
import { IoCalendarOutline, IoAdd, IoAddCircle, IoList, IoMedkit, IoFitnessOutline, 
  IoPeople, IoBook, IoWaterOutline, IoRestaurant, IoMoon } from 'react-icons/io5';
import { ROLE_COLORS } from '../../../config/theme';
import { firebaseCaregiverService } from '../../../services/firebaseCaregiverService';
import '../../../styles/dashboards/caregiver/patientactivitiesviewer.css';

const PatientActivitiesViewer = ({ patientId, patientName, onAddActivity }) => {
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [activities, setActivities] = useState([]);
  const [selectedType, setSelectedType] = useState('all');
  const caregiverColors = ROLE_COLORS.caregiver;

  // Activity type filters
  const activityTypes = [
    { id: 'all', label: 'All', icon: <IoList /> },
    { id: 'medication', label: 'Medication', icon: <IoMedkit /> },
    { id: 'exercise', label: 'Exercise', icon: <IoFitnessOutline /> },
    { id: 'social', label: 'Social', icon: <IoPeople /> },
    { id: 'cognitive', label: 'Cognitive', icon: <IoBook /> },
    { id: 'hygiene', label: 'Hygiene', icon: <IoWaterOutline /> },
    { id: 'nutrition', label: 'Nutrition', icon: <IoRestaurant /> },
    { id: 'sleep', label: 'Sleep', icon: <IoMoon /> },
    { id: 'other', label: 'Other', icon: <IoList /> }
  ];

  // Load activities on component mount
  useEffect(() => {
    loadActivities();
  }, [patientId]);

  // Load activities when filter changes
  useEffect(() => {
    loadActivities();
  }, [selectedType]);

  const loadActivities = async () => {
    if (!patientId) return;

    try {
      setLoading(true);
      const activityType = selectedType === 'all' ? null : selectedType;
      const data = await firebaseCaregiverService.getPatientActivities(patientId, activityType);
      setActivities(data);
    } catch (error) {
      console.error('Error loading patient activities:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleRefresh = () => {
    setRefreshing(true);
    loadActivities();
  };

  const handleFilterChange = (type) => {
    setSelectedType(type);
  };

  // Get icon for activity type
  const getActivityIcon = (type) => {
    const activityType = activityTypes.find(t => t.id === type);
    return activityType ? activityType.icon : <IoList />;
  };

  // Format date for display
  const formatDate = (dateString) => {
    const date = new Date(dateString);

    // Format date in a more readable way
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    let dateLabel;
    if (date.toDateString() === today.toDateString()) {
      dateLabel = 'Today';
    } else if (date.toDateString() === yesterday.toDateString()) {
      dateLabel = 'Yesterday';
    } else {
      dateLabel = date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric'
      });
    }

    // Format time
    const timeString = date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    });

    return { dateLabel, timeString };
  };

  // Render activity item
  const renderActivityItem = (item) => {
    const { dateLabel, timeString } = formatDate(item.timestamp);

    return (
      <div className="activityCard" key={item.id}>
        <div className="activityHeader">
          <div className="activityTypeIcon" style={{ backgroundColor: caregiverColors.primary }}>
            {getActivityIcon(item.type)}
          </div>
          <div className="activityHeaderContent">
            <span className="activityType">{item.type.charAt(0).toUpperCase() + item.type.slice(1)}</span>
            <div className="activityTimeContainer">
              <IoCalendarOutline className="activityTimeIcon" size={12} color="#757575" />
              <span className="activityTime">{dateLabel} at {timeString}</span>
            </div>
          </div>
          <div className="activityStatus" style={{ backgroundColor: item.completed ? '#4CAF50' : '#FFC107' }}>
            <span className="activityStatusText">{item.completed ? 'Completed' : 'Not Completed'}</span>
          </div>
        </div>
        <div className="activityContent">
          <p className="activityDescription">{item.description}</p>
          {item.duration > 0 && (
            <p className="activityDuration">Duration: {item.duration} minutes</p>
          )}
          {item.notes && (
            <div className="activityNotes">
              <span className="activityNotesLabel">Notes:</span>
              <p className="activityNotesText">{item.notes}</p>
            </div>
          )}
        </div>
      </div>
    );
  };

  // Render empty state
  const renderEmptyState = () => (
    <div className="emptyContainer">
      <IoCalendarOutline size={60} color="#ccc" />
      <span className="emptyText">No activities recorded</span>
      <span className="emptySubtext">
        Record daily activities to track patient progress
      </span>
      <button
        className="addButton"
        style={{ backgroundColor: caregiverColors.primary }}
        onClick={onAddActivity}
      >
        <IoAdd size={20} color="#fff" />
        <span className="addButtonText">Record New Activity</span>
      </button>
    </div>
  );

  return (
    <div className="container">
      <div className="filterContainer">
        <div className="filterScrollView">
          {activityTypes.map((type) => (
            <button
              key={type.id}
              className={`filterButton ${selectedType === type.id ? 'filterButtonActive' : ''}`}
              style={selectedType === type.id ? { backgroundColor: caregiverColors.primary } : {}}
              onClick={() => handleFilterChange(type.id)}
            >
              <span style={{ color: selectedType === type.id ? '#fff' : '#757575' }}>
                {type.icon}
              </span>
              <span
                className={`filterButtonText ${selectedType === type.id ? 'filterButtonTextActive' : ''}`}
                style={{ color: selectedType === type.id ? '#fff' : '#757575' }}
              >
                {type.label}
              </span>
            </button>
          ))}
        </div>
      </div>

      {loading && !refreshing ? (
        <div className="loadingContainer">
          <div className="spinner" style={{ borderLeftColor: caregiverColors.primary }}></div>
          <span className="loadingText">Loading activities...</span>
        </div>
      ) : (
        <>
          <div className="headerContainer">
            <span className="headerTitle">
              {selectedType === 'all' ? 'All Activities' : `${selectedType.charAt(0).toUpperCase() + selectedType.slice(1)} Activities`}
            </span>
            <button
              className="addActivityButton"
              onClick={onAddActivity}
            >
              <IoAddCircle size={24} color={caregiverColors.primary} />
            </button>
          </div>

          <div className={activities.length === 0 ? "emptyList" : "list"}>
            {activities.length === 0 ? (
              renderEmptyState()
            ) : (
              activities.map(item => renderActivityItem(item))
            )}
          </div>
        </>
      )}
    </div>
  );
};

export default PatientActivitiesViewer;
