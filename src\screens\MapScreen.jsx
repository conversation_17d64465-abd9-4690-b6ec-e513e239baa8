import React, { useState, useEffect, useRef } from 'react';
import '../styles/screens/mapScreen.css';

const MapScreen = () => {
  // State variables
  const [loading, setLoading] = useState(true);
  const [currentLocation, setCurrentLocation] = useState(null);
  const [savedLocations, setSavedLocations] = useState([]);
  const [savedRoutes, setSavedRoutes] = useState([]);
  const [showSaveLocationModal, setShowSaveLocationModal] = useState(false);
  const [showSaveRouteModal, setShowSaveRouteModal] = useState(false);
  const [locationName, setLocationName] = useState('');
  const [locationNotes, setLocationNotes] = useState('');
  const [routeName, setRouteName] = useState('');
  const [routeNotes, setRouteNotes] = useState('');
  const [showSavedLocations, setShowSavedLocations] = useState(false);
  const [showSavedRoutes, setShowSavedRoutes] = useState(false);
  const [isTrackingRoute, setIsTrackingRoute] = useState(false);
  const [currentRouteCoordinates, setCurrentRouteCoordinates] = useState([]);
  const [locationToSave, setLocationToSave] = useState(null);
  const [errorMessage, setErrorMessage] = useState(null);
  const [navigationActive, setNavigationActive] = useState(false);
  const [navigationDestination, setNavigationDestination] = useState(null);
  const [navigationInstructions, setNavigationInstructions] = useState(null);
  const [showNavigationModal, setShowNavigationModal] = useState(false);

  // Map specific states
  const [mapRegion, setMapRegion] = useState(null);
  const [destinationCoords, setDestinationCoords] = useState(null);
  const [routeCoordinates, setRouteCoordinates] = useState([]);
  const [routeDistance, setRouteDistance] = useState(null);
  const [routeDuration, setRouteDuration] = useState(null);

  // Refs
  const mapRef = useRef(null);

  // Storage keys
  const SAVED_LOCATIONS_KEY = 'neurocare_saved_locations';
  const SAVED_ROUTES_KEY = 'neurocare_saved_routes';

  // Load initial data
  useEffect(() => {
    const loadInitialData = async () => {
      try {
        // Request location permissions
        if (navigator.geolocation) {
          navigator.geolocation.getCurrentPosition(
            (position) => {
              const currentCoords = {
                latitude: position.coords.latitude,
                longitude: position.coords.longitude,
              };

              setCurrentLocation(currentCoords);

              // Set initial map region
              const initialRegion = {
                ...currentCoords,
                latitudeDelta: 0.01,
                longitudeDelta: 0.01,
              };

              setMapRegion(initialRegion);
            },
            (error) => {
              console.error('Error getting location:', error);
              setErrorMessage('Permission to access location was denied');

              // Default location if user location is not available
              const defaultCoords = {
                latitude: 48.8566, // Paris coordinates as fallback
                longitude: 2.3522,
              };

              setCurrentLocation(defaultCoords);
              setMapRegion({
                ...defaultCoords,
                latitudeDelta: 0.01,
                longitudeDelta: 0.01,
              });
            }
          );
        } else {
          setErrorMessage('Geolocation is not supported by this browser');

          // Default location
          const defaultCoords = {
            latitude: 48.8566,
            longitude: 2.3522,
          };

          setCurrentLocation(defaultCoords);
          setMapRegion({
            ...defaultCoords,
            latitudeDelta: 0.01,
            longitudeDelta: 0.01,
          });
        }

        // Load saved locations and routes from localStorage
        const savedLocationsJson = localStorage.getItem(SAVED_LOCATIONS_KEY);
        const savedRoutesJson = localStorage.getItem(SAVED_ROUTES_KEY);

        setSavedLocations(savedLocationsJson ? JSON.parse(savedLocationsJson) : []);
        setSavedRoutes(savedRoutesJson ? JSON.parse(savedRoutesJson) : []);

      } catch (error) {
        console.error('Error loading initial data:', error);
        setErrorMessage('Failed to load map data. Please try again.');

        // Set default location in case of error
        setCurrentLocation({
          latitude: 48.8566,
          longitude: 2.3522,
        });
      } finally {
        setLoading(false);
      }
    };

    loadInitialData();
  }, []);

  // Handle saving a location
  const handleSaveLocation = async () => {
    if (!locationName.trim()) {
      alert('Please enter a name for this location');
      return;
    }

    try {
      // Get current location if locationToSave is not set
      const locationData = locationToSave || currentLocation;

      if (!locationData) {
        alert('Unable to determine location. Please try again.');
        return;
      }

      const newLocation = {
        id: Date.now().toString(),
        name: locationName.trim(),
        latitude: locationData.latitude,
        longitude: locationData.longitude,
        notes: locationNotes.trim(),
        createdAt: new Date().toISOString(),
      };

      // Get existing saved locations
      const savedLocationsJson = localStorage.getItem(SAVED_LOCATIONS_KEY);
      const existingLocations = savedLocationsJson ? JSON.parse(savedLocationsJson) : [];

      // Add the new location
      const updatedLocations = [...existingLocations, newLocation];

      // Save to localStorage
      localStorage.setItem(SAVED_LOCATIONS_KEY, JSON.stringify(updatedLocations));

      // Update state
      setSavedLocations(updatedLocations);
      setShowSaveLocationModal(false);
      setLocationName('');
      setLocationNotes('');
      setLocationToSave(null);

      alert('Location saved successfully');
    } catch (error) {
      console.error('Error saving location:', error);
      alert('Failed to save location. Please try again.');
    }
  };

  // Start tracking a route
  const startRouteTracking = async () => {
    try {
      // Reset current route
      setCurrentRouteCoordinates([]);

      // Get current location
      if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
          (position) => {
            const initialCoordinate = {
              latitude: position.coords.latitude,
              longitude: position.coords.longitude,
              timestamp: new Date().toISOString(),
            };

            setCurrentRouteCoordinates([initialCoordinate]);
            setIsTrackingRoute(true);

            // Start watching position
            const watchId = navigator.geolocation.watchPosition(
              (newPosition) => {
                setCurrentRouteCoordinates(prevCoordinates => [
                  ...prevCoordinates,
                  {
                    latitude: newPosition.coords.latitude,
                    longitude: newPosition.coords.longitude,
                    timestamp: new Date().toISOString(),
                  }
                ]);
              },
              (error) => {
                console.error('Error watching position:', error);
              },
              {
                enableHighAccuracy: true,
                timeout: 5000,
                maximumAge: 0
              }
            );

            // Store the watch ID for cleanup
            return watchId;
          },
          (error) => {
            console.error('Error getting current location:', error);
            alert('Could not get current location. Please try again.');
          }
        );
      } else {
        alert('Geolocation is not supported by this browser.');
      }
    } catch (error) {
      console.error('Error starting route tracking:', error);
      alert('Failed to start route tracking. Please try again.');
    }
  };

  // Stop tracking a route
  const stopRouteTracking = () => {
    setIsTrackingRoute(false);

    if (currentRouteCoordinates.length > 1) {
      setShowSaveRouteModal(true);
    } else {
      alert('The route is too short to save.');
      setCurrentRouteCoordinates([]);
    }
  };

  // Handle saving a route
  const handleSaveRoute = async () => {
    if (!routeName.trim()) {
      alert('Please enter a name for this route');
      return;
    }

    if (currentRouteCoordinates.length < 2) {
      alert('Route is too short to save');
      return;
    }

    try {
      const newRoute = {
        id: Date.now().toString(),
        name: routeName.trim(),
        coordinates: currentRouteCoordinates,
        notes: routeNotes.trim(),
        createdAt: new Date().toISOString(),
      };

      // Get existing saved routes
      const savedRoutesJson = localStorage.getItem(SAVED_ROUTES_KEY);
      const existingRoutes = savedRoutesJson ? JSON.parse(savedRoutesJson) : [];

      // Add the new route
      const updatedRoutes = [...existingRoutes, newRoute];

      // Save to localStorage
      localStorage.setItem(SAVED_ROUTES_KEY, JSON.stringify(updatedRoutes));

      // Update state
      setSavedRoutes(updatedRoutes);
      setShowSaveRouteModal(false);
      setRouteName('');
      setRouteNotes('');
      setCurrentRouteCoordinates([]);

      alert('Route saved successfully');
    } catch (error) {
      console.error('Error saving route:', error);
      alert('Failed to save route. Please try again.');
    }
  };

  // Handle deleting a location
  const handleDeleteLocation = async (locationId) => {
    try {
      // Get existing saved locations
      const savedLocationsJson = localStorage.getItem(SAVED_LOCATIONS_KEY);
      const existingLocations = savedLocationsJson ? JSON.parse(savedLocationsJson) : [];

      // Filter out the location to delete
      const updatedLocations = existingLocations.filter(location => location.id !== locationId);

      // Save to localStorage
      localStorage.setItem(SAVED_LOCATIONS_KEY, JSON.stringify(updatedLocations));

      // Update state
      setSavedLocations(updatedLocations);

      alert('Location deleted successfully');
    } catch (error) {
      console.error('Error deleting location:', error);
      alert('Failed to delete location. Please try again.');
    }
  };

  // Handle deleting a route
  const handleDeleteRoute = async (routeId) => {
    try {
      // Get existing saved routes
      const savedRoutesJson = localStorage.getItem(SAVED_ROUTES_KEY);
      const existingRoutes = savedRoutesJson ? JSON.parse(savedRoutesJson) : [];

      // Filter out the route to delete
      const updatedRoutes = existingRoutes.filter(route => route.id !== routeId);

      // Save to localStorage
      localStorage.setItem(SAVED_ROUTES_KEY, JSON.stringify(updatedRoutes));

      // Update state
      setSavedRoutes(updatedRoutes);

      alert('Route deleted successfully');
    } catch (error) {
      console.error('Error deleting route:', error);
      alert('Failed to delete route. Please try again.');
    }
  };

  // Handle completing navigation
  const handleCompleteNavigation = async () => {
    setNavigationActive(false);
    setShowNavigationModal(false);
    alert('Navigation completed successfully!');
  };

  // Render loading state
  if (loading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <div className="loading-text">Loading map...</div>
      </div>
    );
  }

  return (
    <div className="container">
      {/* Map View */}
      <div className="map-container">
        {mapRegion ? (
          <>
            {/* Google Maps Embed */}
            <div className="map">
              <iframe
                ref={mapRef}
                src={`https://www.google.com/maps/embed/v1/view?key=YOUR_API_KEY&center=${currentLocation?.latitude || 48.8566},${currentLocation?.longitude || 2.3522}&zoom=14`}
                width="100%"
                height="100%"
                style={{ border: 0 }}
                allowFullScreen=""
                loading="lazy"
                referrerPolicy="no-referrer-when-downgrade"
                title="Map"
              ></iframe>
            </div>

            {/* Custom Map Controls */}
            <div className="map-controls">
              <button
                className="map-control-button"
                onClick={() => {
                  // Zoom in functionality would be implemented with Google Maps API
                  console.log('Zoom in');
                }}
              >
                <span className="icon">+</span>
              </button>

              <button
                className="map-control-button"
                onClick={() => {
                  // Zoom out functionality would be implemented with Google Maps API
                  console.log('Zoom out');
                }}
              >
                <span className="icon">-</span>
              </button>

              <button
                className="map-control-button"
                onClick={async () => {
                  try {
                    if (navigator.geolocation) {
                      navigator.geolocation.getCurrentPosition(
                        (position) => {
                          const currentCoords = {
                            latitude: position.coords.latitude,
                            longitude: position.coords.longitude,
                          };

                          setCurrentLocation(currentCoords);
                        },
                        (error) => {
                          console.error('Error getting current location:', error);
                          alert('Failed to get current location. Please try again.');
                        }
                      );
                    }
                  } catch (error) {
                    console.error('Error getting current location:', error);
                    alert('Failed to get current location. Please try again.');
                  }
                }}
              >
                <span className="icon">📍</span>
              </button>
            </div>

            {/* Navigation Info Overlay (if in navigation mode) */}
            {navigationActive && (
              <div className="navigation-overlay">
                <div className="navigation-title">
                  Navigating to: {navigationDestination}
                </div>

                {/* Route Information */}
                {routeDistance && routeDuration && (
                  <div className="route-info-container">
                    <div className="route-info-item">
                      <span className="icon">🚗</span>
                      <span className="route-info-text">
                        {routeDistance.toFixed(2)} km
                      </span>
                    </div>
                    <div className="route-info-item">
                      <span className="icon">⏱️</span>
                      <span className="route-info-text">
                        {routeDuration > 60
                          ? `${Math.floor(routeDuration / 60)}h ${routeDuration % 60}min`
                          : `${routeDuration} min`}
                      </span>
                    </div>
                  </div>
                )}

                {/* Instructions */}
                {navigationInstructions && (
                  <div className="instructions-container">
                    <div className="instructions-title">Instructions:</div>
                    <div className="instructions-text">{navigationInstructions}</div>
                  </div>
                )}
              </div>
            )}
          </>
        ) : (
          <div className="map-error-container">
            <div className="map-error-text">Unable to load map</div>
            <button
              className="map-error-button"
              onClick={async () => {
                setLoading(true);
                try {
                  if (navigator.geolocation) {
                    navigator.geolocation.getCurrentPosition(
                      (position) => {
                        const currentCoords = {
                          latitude: position.coords.latitude,
                          longitude: position.coords.longitude,
                        };

                        setCurrentLocation(currentCoords);
                        setMapRegion({
                          ...currentCoords,
                          latitudeDelta: 0.01,
                          longitudeDelta: 0.01,
                        });
                      },
                      (error) => {
                        console.error('Error getting location:', error);
                        alert('Failed to get location. Please check your permissions.');

                        // Default location
                        const defaultCoords = {
                          latitude: 48.8566,
                          longitude: 2.3522,
                        };

                        setCurrentLocation(defaultCoords);
                        setMapRegion({
                          ...defaultCoords,
                          latitudeDelta: 0.01,
                          longitudeDelta: 0.01,
                        });
                      }
                    );
                  }
                } catch (error) {
                  console.error('Error getting location:', error);
                  alert('Failed to get location. Please check your permissions.');
                } finally {
                  setLoading(false);
                }
              }}
            >
              Retry
            </button>
          </div>
        )}
      </div>

      {/* Action Buttons */}
      <div className="action-buttons-container">
        {navigationActive ? (
          <>
            {/* Current Location Button (Navigation Mode) */}
            <button
              className="action-button primary"
              onClick={async () => {
                try {
                  if (navigator.geolocation) {
                    navigator.geolocation.getCurrentPosition(
                      (position) => {
                        setCurrentLocation({
                          latitude: position.coords.latitude,
                          longitude: position.coords.longitude,
                        });
                      },
                      (error) => {
                        console.error('Error getting current location:', error);
                        alert('Failed to get current location. Please try again.');
                      }
                    );
                  }
                } catch (error) {
                  console.error('Error getting current location:', error);
                  alert('Failed to get current location. Please try again.');
                }
              }}
            >
              <span className="icon">📍</span>
            </button>

            {/* Show Navigation Instructions */}
            <button
              className="action-button purple"
              onClick={() => setShowNavigationModal(true)}
            >
              <span className="icon">ℹ️</span>
            </button>

            {/* Complete Navigation */}
            <button
              className="action-button green"
              onClick={handleCompleteNavigation}
            >
              <span className="icon">✅</span>
            </button>

            {/* Cancel Navigation */}
            <button
              className="action-button red"
              onClick={() => {
                if (window.confirm('Are you sure you want to cancel navigation?')) {
                  setNavigationActive(false);
                }
              }}
            >
              <span className="icon">❌</span>
            </button>
          </>
        ) : (
          <>
            {/* Current Location Button */}
            <button
              className="action-button primary"
              onClick={async () => {
                try {
                  if (navigator.geolocation) {
                    navigator.geolocation.getCurrentPosition(
                      (position) => {
                        setCurrentLocation({
                          latitude: position.coords.latitude,
                          longitude: position.coords.longitude,
                        });
                      },
                      (error) => {
                        console.error('Error getting current location:', error);
                        alert('Failed to get current location. Please try again.');
                      }
                    );
                  }
                } catch (error) {
                  console.error('Error getting current location:', error);
                  alert('Failed to get current location. Please try again.');
                }
              }}
            >
              <span className="icon">📍</span>
            </button>

            {/* Save Current Location Button */}
            <button
              className="action-button secondary"
              onClick={() => {
                if (currentLocation) {
                  setLocationToSave(currentLocation);
                  setShowSaveLocationModal(true);
                } else {
                  alert('Unable to determine current location. Please try again.');
                }
              }}
            >
              <span className="icon">🔖</span>
            </button>

            {/* Saved Locations Button */}
            <button
              className="action-button tertiary"
              onClick={() => setShowSavedLocations(!showSavedLocations)}
            >
              <span className="icon">📋</span>
            </button>

            {/* Route Tracking Button */}
            <button
              className={`action-button ${isTrackingRoute ? 'red' : 'green'}`}
              onClick={async () => {
                if (isTrackingRoute) {
                  stopRouteTracking();
                } else {
                  await startRouteTracking();
                }
              }}
            >
              <span className="icon">{isTrackingRoute ? '⏹️' : '▶️'}</span>
            </button>
          </>
        )}
      </div>

      {/* Navigation Instructions Modal */}
      {showNavigationModal && (
        <div className="modal-container" onClick={() => setShowNavigationModal(false)}>
          <div className="modal-content" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <div className="modal-title">Navigation Guidance</div>
              <button
                className="modal-close-button"
                onClick={() => setShowNavigationModal(false)}
              >
                ✕
              </button>
            </div>

            <div className="modal-body">
              <div className="navigation-details">
                <div className="navigation-detail-row">
                  <span className="navigation-detail-label">Destination:</span>
                  <span className="navigation-detail-value">{navigationDestination}</span>
                </div>

                {navigationInstructions && (
                  <div className="instructions-container">
                    <div className="instructions-label">Instructions:</div>
                    <div className="instructions-text">{navigationInstructions}</div>
                  </div>
                )}
              </div>
            </div>

            <div className="modal-footer">
              <button
                className="modal-button cancel-button"
                onClick={() => setShowNavigationModal(false)}
              >
                Close
              </button>

              <button
                className="modal-button complete-button"
                onClick={handleCompleteNavigation}
              >
                Complete
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Saved Locations Panel */}
      {showSavedLocations && (
        <div className="saved-items-panel">
          <div className="saved-items-header">
            <div className="saved-items-title">Saved Locations</div>
            <button
              className="panel-close-button"
              onClick={() => setShowSavedLocations(false)}
            >
              ✕
            </button>
          </div>
          <div className="saved-items-list">
            {savedLocations.length === 0 ? (
              <div className="empty-list-text">No saved locations yet</div>
            ) : (
              savedLocations.map((location) => (
                <div
                  key={location.id}
                  className="saved-item-card"
                  onClick={() => {
                    setCurrentLocation({
                      latitude: location.latitude,
                      longitude: location.longitude,
                    });
                    setShowSavedLocations(false);
                  }}
                >
                  <div className="saved-item-info">
                    <div className="saved-item-name">{location.name}</div>
                    <div className="saved-item-address">
                      {`Lat: ${location.latitude.toFixed(6)}, Lng: ${location.longitude.toFixed(6)}`}
                    </div>
                    {location.notes && (
                      <div className="saved-item-notes">{location.notes}</div>
                    )}
                  </div>
                  <button
                    className="delete-button"
                    onClick={(e) => {
                      e.stopPropagation();
                      if (window.confirm(`Are you sure you want to delete "${location.name}"?`)) {
                        handleDeleteLocation(location.id);
                      }
                    }}
                  >
                    🗑️
                  </button>
                </div>
              ))
            )}
          </div>
        </div>
      )}

      {/* Save Location Modal */}
      {showSaveLocationModal && (
        <div className="modal-container" onClick={() => setShowSaveLocationModal(false)}>
          <div className="modal-content" onClick={(e) => e.stopPropagation()}>
            <div className="modal-title">Save Location</div>

            <input
              className="input"
              type="text"
              placeholder="Location Name"
              value={locationName}
              onChange={(e) => setLocationName(e.target.value)}
              autoFocus
            />

            <textarea
              className="input text-area"
              placeholder="Notes (optional)"
              value={locationNotes}
              onChange={(e) => setLocationNotes(e.target.value)}
              rows={3}
            />

            <div className="modal-buttons">
              <button
                className="modal-button cancel-button"
                onClick={() => {
                  setShowSaveLocationModal(false);
                  setLocationName('');
                  setLocationNotes('');
                  setLocationToSave(null);
                }}
              >
                Cancel
              </button>

              <button
                className="modal-button save-button"
                onClick={handleSaveLocation}
              >
                Save
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Save Route Modal */}
      {showSaveRouteModal && (
        <div className="modal-container" onClick={() => setShowSaveRouteModal(false)}>
          <div className="modal-content" onClick={(e) => e.stopPropagation()}>
            <div className="modal-title">Save Route</div>

            <input
              className="input"
              type="text"
              placeholder="Route Name"
              value={routeName}
              onChange={(e) => setRouteName(e.target.value)}
              autoFocus
            />

            <textarea
              className="input text-area"
              placeholder="Notes (optional)"
              value={routeNotes}
              onChange={(e) => setRouteNotes(e.target.value)}
              rows={3}
            />

            <div className="modal-buttons">
              <button
                className="modal-button cancel-button"
                onClick={() => {
                  setShowSaveRouteModal(false);
                  setRouteName('');
                  setRouteNotes('');
                  setCurrentRouteCoordinates([]);
                }}
              >
                Cancel
              </button>

              <button
                className="modal-button save-button"
                onClick={handleSaveRoute}
              >
                Save
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MapScreen;
