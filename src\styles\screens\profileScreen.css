.profile-screen-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #fff;
}

.loading-container {
  display: flex;
  flex: 1;
  justify-content: center;
  align-items: center;
  background-color: #fff;
  min-height: 100vh;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid rgba(16, 107, 0, 1);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive design */
@media (max-width: 768px) {
  .profile-screen-container {
    padding: 10px;
  }
}

@media (max-width: 480px) {
  .profile-screen-container {
    padding: 5px;
  }
}
