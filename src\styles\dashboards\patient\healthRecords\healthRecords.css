.container {
  background-color: #f5f5f5;
  padding-bottom: 20px;
}

.loaderContainer {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: #f5f5f5;
  padding: 20px;
  min-height: 300px;
}

.loaderText {
  margin-top: 10px;
  font-size: 16px;
  color: #4285F4;
}

.headerContainer {
  padding: 16px;
  background-color: #ffffff;
}

.header {
  font-size: 24px;
  font-weight: bold;
}

.card {
  margin: 16px;
  padding: 16px;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.cardTitle {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 16px;
}

.selectorsContainer {
  margin-bottom: 16px;
}

.selectorWrapper {
  margin-bottom: 16px;
}

.label {
  font-size: 16px;
  margin-bottom: 8px;
  font-weight: 500;
}

.buttonRow {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 8px;
}

.button {
  background-color: #f0f0f0;
  padding: 10px;
  border-radius: 4px;
  margin-right: 8px;
  margin-bottom: 8px;
  cursor: pointer;
  border: none;
}

.selectedButton {
  background-color: rgba(16, 107, 0, 1);
  color: #fff;
}

.buttonText {
  color: #333;
}

.selectedButtonText {
  color: #fff;
  font-weight: bold;
}

.chartContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 8px;
}

.chartTitle {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 12px;
  text-align: center;
}

.chart {
  border-radius: 8px;
  margin: 8px 0;
}

.legendContainer {
  display: flex;
  justify-content: center;
  margin-top: 10px;
}

.legendItem {
  display: flex;
  align-items: center;
  margin: 0 10px;
}

.legendDot {
  width: 10px;
  height: 10px;
  border-radius: 5px;
  margin-right: 5px;
}

.legendText {
  font-size: 12px;
  color: #666;
}

.message {
  font-size: 16px;
  text-align: center;
  margin: 20px;
}

.errorMessage {
  font-size: 16px;
  text-align: center;
  margin: 20px;
  color: red;
}

/* Table styles */
.tableContainer {
  margin-top: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #fff;
  width: 100%;
  max-height: 400px;
  overflow: auto;
}

.tableBodyContainer {
  max-height: 300px;
  overflow-y: auto;
}

.tableRow {
  display: flex;
  border-bottom: 1px solid #ddd;
}

.tableHeaderCell {
  padding: 10px;
  background-color: rgba(16, 107, 0, 0.1);
}

.tableCell {
  padding: 10px;
}

.dateCell {
  flex: 2;
  justify-content: flex-start;
}

.valueCell {
  flex: 1.5;
  justify-content: center;
}

.tableHeaderText {
  font-weight: bold;
  font-size: 14px;
}

.tableCellText {
  font-size: 14px;
}

.evenRow {
  background-color: #f9f9f9;
}

.recordedRow {
  border-left: 3px solid #4285F4;
}

.recordedText {
  font-weight: 500;
  color: #4285F4;
}

.cardHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.recordButton {
  display: flex;
  align-items: center;
  background-color: #4285F4;
  padding: 8px 12px;
  border-radius: 20px;
  cursor: pointer;
  border: none;
  color: #fff;
}

.recordButtonText {
  color: #fff;
  margin-left: 5px;
  font-size: 14px;
  font-weight: 500;
}

.filterContainer {
  margin-top: 10px;
  margin-bottom: 15px;
  display: flex;
  overflow-x: auto;
}

.filterButton {
  display: flex;
  align-items: center;
  background-color: #f0f0f0;
  padding: 8px 12px;
  border-radius: 20px;
  margin-right: 8px;
  cursor: pointer;
  border: none;
}

.filterButtonActive {
  background-color: #4285F4;
  color: #fff;
}

.filterButtonText {
  font-size: 14px;
  margin-left: 4px;
}

.filterButtonTextActive {
  color: #fff;
  font-weight: 500;
}

.filterScrollContainer {
  display: flex;
  overflow-x: auto;
  padding-bottom: 5px;
}

.allVitalsContainer {
  margin-top: 5px;
}

.emptyFilterText {
  text-align: center;
  color: #757575;
  padding: 20px;
  font-style: italic;
}

.vitalCard {
  display: flex;
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 10px;
  border-left: 3px solid #4285F4;
}

.vitalIconContainer {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 12px;
}

.vitalInfo {
  flex: 1;
}

.vitalType {
  font-size: 14px;
  font-weight: bold;
  color: #333;
  margin: 0;
}

.vitalValue {
  font-size: 18px;
  font-weight: 500;
  color: #4285F4;
  margin: 2px 0;
}

.vitalDate {
  font-size: 12px;
  color: #757575;
  margin: 0;
}

.emptyRecordsContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
}

.emptyRecordsText {
  font-size: 16px;
  color: #757575;
  margin-top: 10px;
  margin-bottom: 20px;
}

.recordVitalsButton {
  display: flex;
  align-items: center;
  background-color: #4285F4;
  padding: 10px 16px;
  border-radius: 8px;
  cursor: pointer;
  border: none;
}

.recordVitalsButtonText {
  color: #fff;
  margin-left: 8px;
  font-size: 16px;
  font-weight: 500;
}

.seeMoreButton {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px;
  margin-top: 5px;
  background-color: #f5f5f5;
  border-radius: 8px;
  border: none;
  cursor: pointer;
  width: 100%;
}

.seeMoreButtonText {
  color: #4285F4;
  font-size: 14px;
  font-weight: 500;
  margin-right: 5px;
}

/* Modal styles */
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.5);
  display: flex;
  justify-content: center;
  align-items: flex-end;
  z-index: 1000;
}

.modalContent {
  background-color: #fff;
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
  padding: 20px;
  width: 100%;
  max-width: 600px;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #eee;
}

.modalTitle {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin: 0;
}

.closeButton {
  padding: 5px;
  background: none;
  border: none;
  cursor: pointer;
}

.modalScrollView {
  margin-top: 10px;
  overflow-y: auto;
  max-height: 60vh;
}

.modalVitalCard {
  display: flex;
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 10px;
  border-left: 3px solid #4285F4;
}

.modalFilterContainer {
  padding: 10px 5px;
  border-bottom: 1px solid #eee;
  overflow-x: auto;
}

.modalFilterButton {
  display: flex;
  align-items: center;
  background-color: #f0f0f0;
  padding: 8px 12px;
  border-radius: 20px;
  margin-right: 8px;
  cursor: pointer;
  border: none;
}

.modalFilterButtonActive {
  background-color: #4285F4;
}

.modalFilterButtonText {
  font-size: 14px;
  margin-left: 4px;
}

.modalFilterButtonTextActive {
  color: #fff;
  font-weight: 500;
}

/* Chart wrapper for web */
.chartWrapper {
  width: 100%;
  height: 300px;
  max-width: 800px;
}

/* Spinner for loading */
.spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border-left-color: #4285F4;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
