import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { getThemeForRole } from '../../config/theme';
import { storage, auth, updateProfile } from '../../config/firebase';
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import { IoCamera, IoClose, IoCameraOutline, IoImageOutline } from 'react-icons/io5';
import '../../styles/profile/profileImageUploader.css';

const ProfileImageUploader = () => {
  const { user, updateUserProfile } = useAuth();
  const theme = getThemeForRole(user?.role || 'default');
  const [profileImage, setProfileImage] = useState(user?.photoURL || null);
  const [showImagePickerModal, setShowImagePickerModal] = useState(false);
  const [selectedImage, setSelectedImage] = useState(null);
  const [uploading, setUploading] = useState(false);

  // Request permissions when component mounts
  useEffect(() => {
    // Web doesn't need explicit permissions for file access
    // This is a placeholder for the React Native permissions code
  }, []);

  // Upload image to Firebase Storage and update user profile
  const uploadImageToFirebase = async (file) => {
    if (!file) {
      console.log('No file provided for upload');
      return null;
    }

    try {
      console.log('Starting image upload process...');
      setUploading(true);

      // Create a URL for the file for immediate display
      const localUri = URL.createObjectURL(file);
      setProfileImage(localUri);

      // Create a reference to the storage location
      const currentUser = auth.currentUser;
      if (!currentUser) {
        console.error('No current user found');
        throw new Error('User not authenticated');
      }
      console.log('Current user ID:', currentUser.uid);

      // Try to upload to Firebase
      try {
        console.log('Creating storage reference...');
        const storageRef = ref(storage, `profile_images/${currentUser.uid}`);
        console.log('Storage reference created:', storageRef);

        // Upload the file
        console.log('Starting upload to Firebase Storage...');
        const uploadResult = await uploadBytes(storageRef, file);
        console.log('Upload successful:', uploadResult);

        // Get the download URL
        console.log('Getting download URL...');
        const downloadURL = await getDownloadURL(storageRef);
        console.log('Download URL received:', downloadURL);

        // Update Firebase Auth profile
        console.log('Updating Firebase Auth profile...');
        await updateProfile(currentUser, { photoURL: downloadURL });
        console.log('Firebase Auth profile updated');

        // Update Firestore user document
        console.log('Updating Firestore user document...');
        await updateUserProfile({ photoURL: downloadURL });
        console.log('Firestore user document updated');

        console.log('Firebase upload completed successfully');
        return downloadURL;
      } catch (firebaseError) {
        console.error('Error with Firebase upload:', firebaseError);
        console.error('Error details:', firebaseError.message);

        // Even if Firebase upload fails, we still have the local image
        console.log('Using local image as fallback');

        // Update user profile with local URI
        await updateUserProfile({ photoURL: localUri });

        // Show a warning but don't treat it as a complete failure
        alert('Image saved locally but could not be uploaded to cloud storage. It may not be available on other devices.');

        return localUri;
      }
    } catch (error) {
      console.error('Error in image processing:', error);
      console.error('Error details:', error.message);
      
      alert('Failed to process profile image: ' + error.message);
      return null;
    } finally {
      setUploading(false);
    }
  };

  // Handle file input change (for web)
  const handleFileChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      const imageUrl = URL.createObjectURL(file);
      setSelectedImage({ uri: imageUrl, file: file });

      if (window.confirm('Do you want to use this photo as your profile picture?')) {
        uploadImageToFirebase(file).then(downloadURL => {
          if (downloadURL) {
            setProfileImage(downloadURL);
            setShowImagePickerModal(false);
          }
        });
      }
    }
  };

  // Take a photo with the camera (web version - opens file dialog)
  const handleTakePhoto = () => {
    // In web, we'll just use the file input with accept="image/*" capture="camera"
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.accept = 'image/*';
    fileInput.capture = 'camera';
    fileInput.onchange = handleFileChange;
    fileInput.click();
  };

  // Pick an image from the gallery (web version - opens file dialog)
  const handlePickImage = () => {
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.accept = 'image/*';
    fileInput.onchange = handleFileChange;
    fileInput.click();
  };

  return (
    <div className="card">
      <div className="cardContent">
        <div className="titleContainer">
          <h2 className="title" style={{ color: theme.colors.primary }}>Profile Picture</h2>
        </div>

        <div className="profileImageContainer">
          {profileImage ? (
            <img
              src={profileImage}
              alt="Profile"
              className="profileImage"
            />
          ) : (
            <div 
              className="avatarText"
              style={{ backgroundColor: theme.colors.primary }}
            >
              {user?.displayName?.substring(0, 2).toUpperCase() || "U"}
            </div>
          )}

          <button
            className="editButton"
            style={{ backgroundColor: theme.colors.primary }}
            onClick={() => setShowImagePickerModal(true)}
          >
            <IoCamera size={20} color="white" />
          </button>
        </div>

        <p className="helperText">
          Click on the camera icon to change your profile picture
        </p>
      </div>

      {/* Image selection modal */}
      {showImagePickerModal && (
        <div className="modalOverlay">
          <div className="profileModalContent">
            <div className="modalHeaderBar">
              <h3 className="modalTitle">Choose Profile Picture</h3>
              <button
                className="closeButton"
                onClick={() => setShowImagePickerModal(false)}
              >
                <IoClose size={24} color="#666" />
              </button>
            </div>

            {uploading ? (
              <div className="loadingContainer">
                <div className="spinner" style={{ borderTopColor: theme.colors.primary }}></div>
                <p className="loadingText">Uploading image...</p>
              </div>
            ) : (
              <>
                <div className="modalInstructions">
                  <p className="modalInstructionsText">
                    Select a method to set your profile picture
                  </p>
                </div>

                <div className="modalButtons">
                  <button
                    className="profileModalButton"
                    onClick={handleTakePhoto}
                  >
                    <div className="profileButtonIconContainer">
                      <IoCameraOutline size={32} color="#555555" />
                    </div>
                    <span className="profileModalButtonText">Take Photo</span>
                  </button>

                  <button
                    className="profileModalButton"
                    onClick={handlePickImage}
                  >
                    <div className="profileButtonIconContainer">
                      <IoImageOutline size={32} color="#555555" />
                    </div>
                    <span className="profileModalButtonText">Gallery</span>
                  </button>
                </div>

                {selectedImage && (
                  <div className="previewContainer">
                    <p className="previewTitle">Preview</p>
                    <img
                      src={selectedImage.uri}
                      alt="Preview"
                      className="previewImage"
                    />
                  </div>
                )}

                <button
                  className="cancelButton"
                  onClick={() => setShowImagePickerModal(false)}
                  disabled={uploading}
                >
                  <span className="cancelButtonText">Cancel</span>
                </button>
              </>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default ProfileImageUploader;
