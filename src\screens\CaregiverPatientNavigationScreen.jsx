import React, { useState, useEffect, useRef } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  <PERSON><PERSON>ontaine<PERSON>,
  TileLayer,
  <PERSON><PERSON>,
  Popup,
  <PERSON>yline,
  useMap
} from 'react-leaflet';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import { ROLE_COLORS } from '../config/theme';
import {
  collection,
  query,
  where,
  onSnapshot,
  orderBy,
  limit,
  doc,
  getDoc,
  addDoc,
  serverTimestamp
} from 'firebase/firestore';
import { db, auth } from '../config/firebase';
import { firebaseLocationService } from '../services/firebaseLocationService';
import { firebaseNavigationService } from '../services/firebaseNavigationService';
import '../styles/screens/caregiverPatientNavigationScreen.css';

// Custom marker icons
const createCustomIcon = (color, iconName) => {
  return L.divIcon({
    className: 'custom-marker',
    html: `<div style="background-color: ${color}; width: 36px; height: 36px; border-radius: 50%; display: flex; align-items: center; justify-content: center; border: 2px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3);">
             <i class="fas fa-${iconName}" style="color: white; font-size: 18px;"></i>
           </div>`,
    iconSize: [36, 36],
    iconAnchor: [18, 18],
  });
};

const CaregiverPatientNavigationScreen = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const caregiverColors = ROLE_COLORS.caregiver;
  const mapRef = useRef(null);

  // Get patient data from route state if available
  const { patient } = location.state || {};

  // State variables
  const [loading, setLoading] = useState(true);
  const [currentLocation, setCurrentLocation] = useState(null);
  const [patientLocation, setPatientLocation] = useState(null);
  const [mapCenter, setMapCenter] = useState([51.505, -0.09]); // Default to London
  const [errorMessage, setErrorMessage] = useState(null);
  const [destinationCoords, setDestinationCoords] = useState(null);
  const [destination, setDestination] = useState('');
  const [routeType, setRouteType] = useState('walking'); // walking, driving, transit
  const [routeCoordinates, setRouteCoordinates] = useState([]);
  const [routeDistance, setRouteDistance] = useState(null);
  const [routeDuration, setRouteDuration] = useState(null);
  const [instructions, setInstructions] = useState('');
  const [showSendGuidanceModal, setShowSendGuidanceModal] = useState(false);
  const [patientLocationsListener, setPatientLocationsListener] = useState(null);
  const [showPatientSelectModal, setShowPatientSelectModal] = useState(false);
  const [patients, setPatients] = useState([]);
  const [selectedPatient, setSelectedPatient] = useState(patient || null);

  // Load initial data
  useEffect(() => {
    loadInitialData();
    loadLinkedPatients();

    // Clean up function
    return () => {
      if (patientLocationsListener) {
        patientLocationsListener();
      }
    };
  }, []);

  // Effect to listen to patient location when selected patient changes
  useEffect(() => {
    if (selectedPatient && selectedPatient.uid) {
      listenToPatientLocation(selectedPatient.uid);
    }
  }, [selectedPatient]);

  const loadInitialData = async () => {
    try {
      setLoading(true);

      // Get current location using browser geolocation API
      if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
          (position) => {
            const coords = {
              latitude: position.coords.latitude,
              longitude: position.coords.longitude,
            };
            setCurrentLocation(coords);
            setMapCenter([coords.latitude, coords.longitude]);
          },
          (error) => {
            console.error('Error getting location:', error);
            setErrorMessage('Permission to access location was denied');
          }
        );
      } else {
        setErrorMessage('Geolocation is not supported by this browser');
      }

      // If no patient is selected and we came directly to this screen,
      // show the patient selection modal
      if (!selectedPatient && !patient) {
        setShowPatientSelectModal(true);
      }
    } catch (error) {
      console.error('Error loading initial data:', error);
      setErrorMessage('Failed to load navigation data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Function to load patients linked to this caregiver
  const loadLinkedPatients = async () => {
    try {
      const currentUser = auth.currentUser;
      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      // Get the current user's document to find linked patients
      const userDocRef = doc(db, 'users', currentUser.uid);
      const userDocSnap = await getDoc(userDocRef);

      if (!userDocSnap.exists()) {
        throw new Error('User document not found');
      }

      const userData = userDocSnap.data();
      const linkedPatientIds = userData.linkedPatients || [];

      if (linkedPatientIds.length === 0) {
        // No linked patients
        setPatients([]);
        return;
      }

      // Get user documents for all linked patients
      const usersCollection = collection(db, 'users');
      const patientDocs = [];

      for (const patientId of linkedPatientIds) {
        const patientDocRef = doc(usersCollection, patientId);
        const patientDocSnap = await getDoc(patientDocRef);

        if (patientDocSnap.exists()) {
          patientDocs.push({
            uid: patientDocSnap.id,
            ...patientDocSnap.data()
          });
        }
      }

      setPatients(patientDocs);
    } catch (error) {
      console.error('Error loading linked patients:', error);
      alert('Failed to load patients. Please try again.');
    }
  };

  // Listen to patient's location updates
  const listenToPatientLocation = (patientId) => {
    try {
      const locationsCollection = collection(db, 'patientLocations');
      const q = query(
        locationsCollection,
        where('userId', '==', patientId),
        orderBy('createdAt', 'desc'),
        limit(1)
      );

      // Set up real-time listener
      const unsubscribe = onSnapshot(q, (snapshot) => {
        if (!snapshot.empty) {
          const locationData = snapshot.docs[0].data();
          setPatientLocation({
            latitude: locationData.latitude,
            longitude: locationData.longitude,
          });

          // If this is the first location update and we don't have a map center yet,
          // center the map on the patient's location
          if (!currentLocation) {
            setMapCenter([locationData.latitude, locationData.longitude]);
          }
        }
      }, (error) => {
        console.error('Error listening to patient location:', error);
        alert('Failed to get real-time location updates.');
      });

      setPatientLocationsListener(unsubscribe);
    } catch (error) {
      console.error('Error setting up location listener:', error);
    }
  };

  // Handle map click to set destination
  const handleMapClick = async (event) => {
    const { lat, lng } = event.latlng;
    const coordinate = { latitude: lat, longitude: lng };

    // Set the clicked location as destination
    setDestinationCoords(coordinate);

    try {
      // Use reverse geocoding to get address (you might need to implement this)
      // For now, we'll just use coordinates
      setDestination(`${lat.toFixed(6)}, ${lng.toFixed(6)}`);

      // Calculate route
      if (patientLocation) {
        calculateRoute(patientLocation, coordinate, routeType);
      }
    } catch (error) {
      console.error('Error handling map click:', error);
    }
  };

  // Calculate route between two points
  const calculateRoute = async (origin, destination, mode) => {
    try {
      // Use our navigation service to calculate the route
      const routeInfo = await firebaseNavigationService.calculateRoute(origin, destination, mode);

      // Update state with route information
      setRouteCoordinates(routeInfo.routeCoordinates);
      setRouteDistance(routeInfo.distance);
      setRouteDuration(routeInfo.duration);
    } catch (error) {
      console.error('Error calculating route:', error);
      alert('Failed to calculate route. Please try again.');
    }
  };

  // Handle patient selection
  const handlePatientSelect = (patient) => {
    setSelectedPatient(patient);
    setShowPatientSelectModal(false);

    // If we have patient location, update the map center
    if (patientLocation) {
      setMapCenter([patientLocation.latitude, patientLocation.longitude]);
    }
  };

  // Send guidance to patient
  const handleSendGuidance = async () => {
    if (!selectedPatient || !selectedPatient.uid) {
      alert('No patient selected');
      return;
    }

    if (!destinationCoords) {
      alert('Please select a destination on the map');
      return;
    }

    if (!instructions.trim()) {
      alert('Please provide instructions for the patient');
      return;
    }

    try {
      setLoading(true);

      // Create guidance data
      const guidanceData = {
        patientId: selectedPatient.uid,
        destination,
        destinationCoords: {
          latitude: destinationCoords.latitude,
          longitude: destinationCoords.longitude,
        },
        routeType,
        routeCoordinates,
        routeDistance,
        routeDuration,
        instructions,
      };

      // Use our navigation service to send the guidance
      await firebaseNavigationService.sendPatientGuidance(guidanceData);

      alert(`Guidance sent to ${selectedPatient.firstName}`);
      setShowSendGuidanceModal(false);
    } catch (error) {
      console.error('Error sending guidance:', error);
      alert('Failed to send guidance. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Render loading state
  if (loading) {
    return (
      <div className="loading-container">
        <div className="spinner"></div>
        <p className="loading-text">Loading navigation...</p>
      </div>
    );
  }

  return (
    <div className="container">
      <div className="header">
        <h1 className="header-title">
          {selectedPatient ? `Navigate ${selectedPatient.firstName}` : 'Patient Navigation'}
        </h1>
        <div className="header-buttons">
          <button
            className="header-button"
            onClick={() => setShowPatientSelectModal(true)}
          >
            <i className="fas fa-users"></i>
          </button>
          <button
            className="header-button"
            onClick={loadInitialData}
          >
            <i className="fas fa-sync-alt"></i>
          </button>
        </div>
      </div>

      {/* Map Section */}
      <div className="map-container">
        <MapContainer
          center={mapCenter}
          zoom={13}
          style={{ height: '100%', width: '100%' }}
          ref={mapRef}
          onClick={handleMapClick}
        >
          <TileLayer
            url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
            attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
          />

          {/* Caregiver Location Marker */}
          {currentLocation && (
            <Marker
              position={[currentLocation.latitude, currentLocation.longitude]}
              icon={createCustomIcon(caregiverColors.primary, 'user')}
            >
              <Popup>Your Location</Popup>
            </Marker>
          )}

          {/* Patient Location Marker */}
          {patientLocation && (
            <Marker
              position={[patientLocation.latitude, patientLocation.longitude]}
              icon={createCustomIcon(ROLE_COLORS.patient.primary, 'user-injured')}
            >
              <Popup>
                {selectedPatient ? `${selectedPatient.firstName}'s Location` : 'Patient Location'}
              </Popup>
            </Marker>
          )}

          {/* Destination Marker */}
          {destinationCoords && (
            <Marker
              position={[destinationCoords.latitude, destinationCoords.longitude]}
              icon={createCustomIcon('#DB4437', 'map-marker-alt')}
            >
              <Popup>{destination || "Selected destination"}</Popup>
            </Marker>
          )}

          {/* Route Polyline */}
          {routeCoordinates.length > 0 && (
            <Polyline
              positions={routeCoordinates.map(coord => [coord.latitude, coord.longitude])}
              color="#4285F4"
              weight={4}
              opacity={0.8}
            />
          )}
        </MapContainer>
      </div>

      {/* Route Type Selection */}
      <div className="route-type-container">
        <button
          className={`route-type-button ${routeType === 'walking' ? 'active' : ''}`}
          onClick={() => {
            setRouteType('walking');
            if (patientLocation && destinationCoords) {
              calculateRoute(patientLocation, destinationCoords, 'walking');
            }
          }}
        >
          <i className="fas fa-walking"></i>
          <span>Walking</span>
        </button>

        <button
          className={`route-type-button ${routeType === 'driving' ? 'active' : ''}`}
          onClick={() => {
            setRouteType('driving');
            if (patientLocation && destinationCoords) {
              calculateRoute(patientLocation, destinationCoords, 'driving');
            }
          }}
        >
          <i className="fas fa-car"></i>
          <span>Driving</span>
        </button>

        <button
          className={`route-type-button ${routeType === 'transit' ? 'active' : ''}`}
          onClick={() => {
            setRouteType('transit');
            if (patientLocation && destinationCoords) {
              calculateRoute(patientLocation, destinationCoords, 'transit');
            }
          }}
        >
          <i className="fas fa-bus"></i>
          <span>Transit</span>
        </button>
      </div>

      {/* Route Information */}
      {destinationCoords && (
        <div className="route-info-section">
          <h3 className="route-info-title">Route Information</h3>

          <div className="route-info-row">
            <span className="route-info-label">Destination:</span>
            <span className="route-info-value">{destination || 'Selected location'}</span>
          </div>

          {routeDistance && (
            <div className="route-info-row">
              <span className="route-info-label">Distance:</span>
              <span className="route-info-value">{routeDistance.toFixed(2)} km</span>
            </div>
          )}

          {routeDuration && (
            <div className="route-info-row">
              <span className="route-info-label">Duration:</span>
              <span className="route-info-value">
                {routeDuration > 60
                  ? `${Math.floor(routeDuration / 60)}h ${routeDuration % 60}min`
                  : `${routeDuration} min`}
              </span>
            </div>
          )}

          <button
            className="send-guidance-button"
            onClick={() => setShowSendGuidanceModal(true)}
          >
            <i className="fas fa-location-arrow"></i>
            <span>Send Guidance to Patient</span>
          </button>
        </div>
      )}

      {/* Send Guidance Modal */}
      {showSendGuidanceModal && (
        <div className="modal-overlay" onClick={() => setShowSendGuidanceModal(false)}>
          <div className="modal-content" onClick={(e) => e.stopPropagation()}>
            <h2 className="modal-title">Send Navigation Guidance</h2>

            <div className="modal-body">
              <div className="modal-field">
                <label className="modal-label">Destination:</label>
                <p className="modal-value">{destination || 'Selected location'}</p>
              </div>

              <div className="modal-field">
                <label className="modal-label">Instructions for Patient:</label>
                <textarea
                  className="instructions-input"
                  placeholder="Enter instructions for the patient..."
                  value={instructions}
                  onChange={(e) => setInstructions(e.target.value)}
                  rows={4}
                />
              </div>
            </div>

            <div className="modal-buttons">
              <button
                className="modal-button cancel-button"
                onClick={() => setShowSendGuidanceModal(false)}
              >
                Cancel
              </button>

              <button
                className="modal-button send-button"
                onClick={handleSendGuidance}
              >
                Send
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Patient Selection Modal */}
      {showPatientSelectModal && (
        <div className="modal-overlay" onClick={() => {
          if (selectedPatient) {
            setShowPatientSelectModal(false);
          } else {
            alert('You need to select a patient to navigate.');
          }
        }}>
          <div className="modal-content patient-select-modal" onClick={(e) => e.stopPropagation()}>
            <h2 className="modal-title">Select Patient to Navigate</h2>

            {patients.length === 0 ? (
              <div className="empty-patients-container">
                <i className="fas fa-users empty-patients-icon"></i>
                <p className="empty-patients-text">No patients found</p>
                <p className="empty-patients-subtext">
                  You need to have patients assigned to you before you can navigate them
                </p>
              </div>
            ) : (
              <div className="patients-list">
                {patients.map((item) => {
                  const initials = `${item.firstName?.charAt(0) || ''}${item.lastName?.charAt(0) || ''}`;
                  const displayName = `${item.firstName || ''} ${item.lastName || ''}`.trim() || 'Unknown Patient';

                  return (
                    <div
                      key={item.uid}
                      className="patient-item"
                      onClick={() => handlePatientSelect(item)}
                    >
                      <div className="patient-avatar">
                        <span className="patient-initials">{initials}</span>
                      </div>
                      <div className="patient-info">
                        <p className="patient-name">{displayName}</p>
                        <p className="patient-email">{item.email || 'No email'}</p>
                      </div>
                      <i className="fas fa-chevron-right"></i>
                    </div>
                  );
                })}
              </div>
            )}

            {selectedPatient && (
              <div className="modal-footer">
                <button
                  className="modal-button cancel-button"
                  onClick={() => setShowPatientSelectModal(false)}
                >
                  Close
                </button>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default CaregiverPatientNavigationScreen;
