import React, { useState } from 'react';
import { Ionicons } from '@expo/vector-icons';
import { ROLE_COLORS } from '../../../config/theme';
import '../../../../styles/dashboards/doctor/doctor_patient_detail/patientDetailCard.css';

const PatientDetailCard = ({ patient }) => {
  const [expandedSections, setExpandedSections] = useState({
    personalInfo: true,
    contactInfo: false,
    medicalInfo: false,
    emergencyContacts: false
  });

  const toggleSection = (section) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  // Format date string to a readable format
  const formatDate = (dateString) => {
    if (!dateString) return 'Not specified';

    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } catch (error) {
      return dateString || 'Not specified';
    }
  };

  // Render a section header with toggle functionality
  const renderSectionHeader = (title, section, icon) => (
    <div
      className="sectionHeader"
      onClick={() => toggleSection(section)}
    >
      <div className="sectionHeaderLeft">
        <Ionicons name={icon} size={20} color={ROLE_COLORS.doctor.primary} />
        <span className="sectionTitle">{title}</span>
      </div>
      <Ionicons
        name={expandedSections[section] ? 'chevron-up' : 'chevron-down'}
        size={20}
        color={ROLE_COLORS.doctor.primary}
      />
    </div>
  );

  // Render a field with label and value
  const renderField = (label, value, icon) => (
    <div className="fieldContainer">
      {icon && <Ionicons name={icon} size={16} color="#666" className="fieldIcon" />}
      <span className="fieldLabel">{label}:</span>
      <span className="fieldValue">{value || 'Not specified'}</span>
    </div>
  );

  // Render emergency contacts
  const renderEmergencyContacts = () => {
    if (!patient.emergencyContacts || patient.emergencyContacts.length === 0) {
      return <p className="emptyText">No emergency contacts specified</p>;
    }

    return patient.emergencyContacts.map((contact, index) => (
      <div key={index} className="contactCard">
        <div className="contactName">{contact.name}</div>
        <div className="contactRelationship">{contact.relationship}</div>
        <div className="contactPhone">{contact.phoneNumber}</div>
      </div>
    ));
  };

  // Render medical conditions
  const renderMedicalConditions = () => {
    const conditions = patient.medicalInfo?.conditions || [];

    if (conditions.length === 0) {
      return <p className="emptyText">No medical conditions specified</p>;
    }

    return (
      <div className="conditionsContainer">
        {conditions.map((condition, index) => (
          <div key={index} className="conditionTag">
            <span className="conditionText">{condition}</span>
          </div>
        ))}
      </div>
    );
  };

  // Render allergies
  const renderAllergies = () => {
    const allergies = patient.medicalInfo?.allergies || patient.allergies || [];

    if (allergies.length === 0) {
      return <p className="emptyText">No allergies specified</p>;
    }

    return (
      <div className="allergiesContainer">
        {allergies.map((allergy, index) => (
          <div key={index} className="allergyTag">
            <span className="allergyText">{allergy}</span>
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className="container">
      {/* Personal Information Section */}
      {renderSectionHeader('Personal Information', 'personalInfo', 'person')}
      {expandedSections.personalInfo && (
        <div className="sectionContent">
          {renderField('Full Name', `${patient.firstName} ${patient.lastName}`, 'person-outline')}
          {renderField('Gender', patient.gender, 'male-female-outline')}
          {renderField('Date of Birth', formatDate(patient.dateOfBirth), 'calendar-outline')}
          {renderField('Age', patient.age, 'time-outline')}
          {renderField('Blood Type', patient.bloodType || patient.medicalInfo?.bloodType, 'water-outline')}
          {renderField('Height', patient.height || patient.medicalInfo?.height, 'resize-outline')}
          {renderField('Weight', patient.weight || patient.medicalInfo?.weight, 'scale-outline')}
        </div>
      )}

      {/* Contact Information Section */}
      {renderSectionHeader('Contact Information', 'contactInfo', 'call')}
      {expandedSections.contactInfo && (
        <div className="sectionContent">
          {renderField('Email', patient.email, 'mail-outline')}
          {renderField('Phone', patient.phone, 'call-outline')}
          {renderField('Address', patient.address, 'home-outline')}
          {renderField('City', patient.city, 'location-outline')}
          {renderField('Country', patient.country, 'globe-outline')}
          {renderField('Postal Code', patient.postalCode, 'map-outline')}
        </div>
      )}

      {/* Medical Information Section */}
      {renderSectionHeader('Medical Information', 'medicalInfo', 'medical')}
      {expandedSections.medicalInfo && (
        <div className="sectionContent">
          {renderField('Primary Diagnosis', patient.medicalInfo?.primaryDiagnosis, 'fitness-outline')}

          <div className="subSectionTitle">Medical Conditions</div>
          {renderMedicalConditions()}

          <div className="subSectionTitle">Allergies</div>
          {renderAllergies()}

          {renderField('Medical History', patient.medicalHistory, 'document-text-outline')}
          {renderField('Insurance Provider', patient.insuranceProvider, 'card-outline')}
          {renderField('Insurance Number', patient.insuranceNumber, 'key-outline')}
        </div>
      )}

      {/* Emergency Contacts Section */}
      {renderSectionHeader('Emergency Contacts', 'emergencyContacts', 'alert-circle')}
      {expandedSections.emergencyContacts && (
        <div className="sectionContent">
          {renderEmergencyContacts()}
        </div>
      )}
    </div>
  );
};

export default PatientDetailCard;
