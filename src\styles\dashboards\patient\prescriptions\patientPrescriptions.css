/* Patient Prescriptions CSS */
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background-color: #fff;
  border-bottom: 1px solid #e0e0e0;
  box-shadow: 0 2px 2px rgba(0, 0, 0, 0.1);
}

.backButton {
  padding: 8px;
  cursor: pointer;
}

.headerTitle {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.loadingContainer {
  display: flex;
  flex: 1;
  justify-content: center;
  align-items: center;
}

.content {
  flex: 1;
}

.emptyContainer {
  display: flex;
  flex-direction: column;
  flex: 1;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.emptyText {
  font-size: 18px;
  font-weight: bold;
  color: #757575;
  margin-top: 16px;
}

.emptySubText {
  font-size: 14px;
  color: #9E9E9E;
  text-align: center;
  margin-top: 8px;
  padding: 0 32px;
}

.listContainer {
  padding: 16px;
}

.prescriptionItem {
  background-color: #fff;
  border-radius: 8px;
  margin-bottom: 12px;
  padding: 16px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  cursor: pointer;
}

.prescriptionHeader {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.prescriptionIcon {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 12px;
}

.prescriptionInfo {
  flex: 1;
  padding-right: 8px;
}

.prescriptionTitle {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.prescriptionMedication {
  font-size: 14px;
  color: #757575;
  margin-top: 2px;
}

.prescriptionDate {
  font-size: 12px;
  color: #9E9E9E;
  margin-top: 4px;
}

.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modalContent {
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modalHeader {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
}

.modalTitle {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.modalScrollView {
  padding: 16px;
  overflow-y: auto;
  max-height: 60vh;
}

.detailSection {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 12px;
}

.detailLabel {
  font-size: 14px;
  font-weight: bold;
  color: #757575;
  width: 80px;
}

.detailValue {
  font-size: 14px;
  color: #333;
  flex: 1;
}

.medicationsSection {
  margin-top: 16px;
  margin-bottom: 16px;
}

.sectionTitle {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
}

.medicationItem {
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 8px;
}

.medicationName {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
}

.medicationDetailRow {
  display: flex;
  flex-direction: row;
  margin-bottom: 6px;
  padding-left: 4px;
}

.medicationDetailLabel {
  font-size: 14px;
  font-weight: bold;
  color: #555;
  width: 90px;
}

.medicationDetailValue {
  font-size: 14px;
  color: #333;
  flex: 1;
}

.notesSection {
  margin-bottom: 16px;
}

.notesText {
  font-size: 14px;
  color: #333;
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 12px;
}

.imageSection {
  margin-bottom: 16px;
}

.prescriptionImage {
  width: 100%;
  height: 200px;
  border-radius: 8px;
  background-color: #f5f5f5;
  object-fit: contain;
}

.modalFooter {
  padding: 16px;
  border-top: 1px solid #e0e0e0;
  display: flex;
  justify-content: center;
}

.closeButton {
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
}

.closeButtonText {
  font-size: 14px;
  font-weight: bold;
  color: #fff;
}
