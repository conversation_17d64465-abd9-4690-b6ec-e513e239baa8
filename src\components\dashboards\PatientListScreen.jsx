import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../../../contexts/AuthContext';
import { usersAPI } from '../../../config/api';
import DoctorAddPatientButton from '../doctor/AddPatientButton';
import SupervisorAddPatientButton from '../supervisor/AddPatientButton';
import '../../styles/dashboards/patientListScreen.css';

const PatientListScreen = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { action } = location.state || {};
  const { user } = useAuth();
  const [patients, setPatients] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    fetchPatients();
  }, []);

  const fetchPatients = async () => {
    try {
      setLoading(true);
      const response = await usersAPI.getLinkedUsers('patients');
      setPatients(response || []);
    } catch (error) {
      console.error('Error fetching patients:', error);
      // Replace react-native-flash-message with browser alert or a toast library
      alert('Failed to load patients. Please try again.');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleRefresh = () => {
    setRefreshing(true);
    fetchPatients();
  };

  const handlePatientPress = (patient) => {
    // If action is assignCaregiver, navigate to the assign caregiver screen
    if (action === 'assignCaregiver' && user.role === 'supervisor') {
      navigate('/supervisor/assign-caregiver', { state: { patient } });
      return;
    }

    // Default behavior - just show a message
    alert(`You selected ${patient.displayName}`);
  };

  const renderPatientItem = (item) => (
    <div 
      className="patientItem"
      onClick={() => handlePatientPress(item)}
      key={item.uid}
    >
      <div className="avatarContainer">
        <span className="avatarText">
          {item.displayName.charAt(0).toUpperCase()}
        </span>
      </div>
      <div className="patientInfo">
        <div className="patientName">{item.displayName}</div>
        <div className="patientRole">{item.role.charAt(0).toUpperCase() + item.role.slice(1)}</div>
      </div>
      <Ionicons name="chevron-forward" size={24} color="#ccc" />
    </div>
  );

  // Render the appropriate add patient button based on user role
  const renderAddPatientButton = (onPatientAdded) => {
    if (user.role === 'supervisor') {
      return <SupervisorAddPatientButton onPatientAdded={onPatientAdded} />;
    } else {
      return <DoctorAddPatientButton onPatientAdded={onPatientAdded} />;
    }
  };

  const renderEmptyList = () => (
    <div className="emptyContainer">
      <Ionicons name="people" size={60} color="#ccc" className="emptyIcon" />
      <div className="emptyText">No patients connected yet</div>
      <div className="emptySubtext">
        Add patients by scanning their QR code or entering their unique code
      </div>
      <div className="addButtonContainer">
        {renderAddPatientButton(() => fetchPatients())}
      </div>
    </div>
  );

  return (
    <div className="container">
      {refreshing && <div>Refreshing...</div>}
      
      <button className="refreshButton" onClick={handleRefresh} disabled={refreshing}>
        Refresh
      </button>
      
      {patients.length > 0 ? (
        <div className="patientList">
          {patients.map(patient => renderPatientItem(patient))}
        </div>
      ) : !loading && renderEmptyList()}

      {loading && (
        <div className="loadingContainer">
          <div className="spinner"></div>
          <div className="loadingText">Loading patients...</div>
        </div>
      )}

      {patients.length > 0 && (
        <div className="floatingButtonContainer">
          {renderAddPatientButton((patient) => {
            setPatients(prev => [...prev, patient]);
            alert(`${patient.displayName} has been added to your patients`);
          })}
        </div>
      )}
    </div>
  );
};

export default PatientListScreen;
