import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { ROLE_COLORS } from '../config/theme';
import { firebaseCaregiverService } from '../services/firebaseCaregiverService';
import '../styles/screenscaregiverRecordVitalsScreen.css';

const CaregiverRecordVitalsScreen = () => {
  const location = useLocation();
  const { patient } = location.state || {};
  const navigate = useNavigate();
  const caregiverColors = ROLE_COLORS.caregiver;
  
  const [vitalType, setVitalType] = useState('heartRate');
  const [vitalValues, setVitalValues] = useState({
    heartRate: '',
    systolic: '',
    diastolic: '',
    bloodGlucose: '',
    weight: '',
    temperature: '',
    oxygenSaturation: '',
  });
  const [notes, setNotes] = useState('');
  const [loading, setLoading] = useState(false);

  const handleVitalTypeChange = (type) => {
    setVitalType(type);
  };

  const handleValueChange = (field, value) => {
    setVitalValues({
      ...vitalValues,
      [field]: value
    });
  };

  const getVitalValues = () => {
    switch (vitalType) {
      case 'heartRate':
        return { bpm: vitalValues.heartRate };
      case 'bloodPressure':
        return { 
          systolic: vitalValues.systolic,
          diastolic: vitalValues.diastolic
        };
      case 'bloodGlucose':
        return { mgdl: vitalValues.bloodGlucose };
      case 'weight':
        return { kg: vitalValues.weight };
      case 'temperature':
        return { celsius: vitalValues.temperature };
      case 'oxygenSaturation':
        return { percentage: vitalValues.oxygenSaturation };
      default:
        return {};
    }
  };

  const validateForm = () => {
    switch (vitalType) {
      case 'heartRate':
        return vitalValues.heartRate.trim() !== '';
      case 'bloodPressure':
        return vitalValues.systolic.trim() !== '' && vitalValues.diastolic.trim() !== '';
      case 'bloodGlucose':
        return vitalValues.bloodGlucose.trim() !== '';
      case 'weight':
        return vitalValues.weight.trim() !== '';
      case 'temperature':
        return vitalValues.temperature.trim() !== '';
      case 'oxygenSaturation':
        return vitalValues.oxygenSaturation.trim() !== '';
      default:
        return false;
    }
  };

  const showAlert = (title, message) => {
    alert(`${title}: ${message}`);
  };

  const showMessage = ({ message, description, type }) => {
    // Simple notification for web - could be replaced with a toast library
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `<strong>${message}</strong><br/>${description}`;
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 15px;
      border-radius: 5px;
      color: white;
      background-color: ${type === 'success' ? '#4CAF50' : '#f44336'};
      z-index: 1000;
      max-width: 300px;
    `;
    document.body.appendChild(notification);
    setTimeout(() => {
      document.body.removeChild(notification);
    }, 3000);
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      showAlert('Validation Error', 'Please fill in all required fields');
      return;
    }

    setLoading(true);
    try {
      const values = getVitalValues();
      await firebaseCaregiverService.recordPatientVital(
        patient.uid,
        vitalType,
        values,
        notes
      );

      showMessage({
        message: 'Success',
        description: 'Vital signs recorded successfully',
        type: 'success',
      });

      // Navigate back to patient detail
      navigate(-1);
    } catch (error) {
      console.error('Error recording vital signs:', error);
      showMessage({
        message: 'Error',
        description: 'Failed to record vital signs. Please try again.',
        type: 'danger',
      });
    } finally {
      setLoading(false);
    }
  };

  const renderVitalTypeButton = (type, label, icon) => (
    <button
      key={type}
      className={`vital-type-button ${vitalType === type ? 'active' : ''}`}
      onClick={() => handleVitalTypeChange(type)}
      style={vitalType === type ? { borderColor: caregiverColors.primary, color: caregiverColors.primary } : {}}
    >
      <i className={`icon-${icon}`}></i>
      <span>{label}</span>
    </button>
  );

  const renderVitalInputs = () => {
    switch (vitalType) {
      case 'heartRate':
        return (
          <div className="input-container">
            <label className="input-label">Heart Rate (BPM)</label>
            <input
              type="number"
              className="input"
              value={vitalValues.heartRate}
              onChange={(e) => handleValueChange('heartRate', e.target.value)}
              placeholder="Enter heart rate"
            />
          </div>
        );
      case 'bloodPressure':
        return (
          <>
            <div className="input-container">
              <label className="input-label">Systolic (mmHg)</label>
              <input
                type="number"
                className="input"
                value={vitalValues.systolic}
                onChange={(e) => handleValueChange('systolic', e.target.value)}
                placeholder="Enter systolic pressure"
              />
            </div>
            <div className="input-container">
              <label className="input-label">Diastolic (mmHg)</label>
              <input
                type="number"
                className="input"
                value={vitalValues.diastolic}
                onChange={(e) => handleValueChange('diastolic', e.target.value)}
                placeholder="Enter diastolic pressure"
              />
            </div>
          </>
        );
      case 'bloodGlucose':
        return (
          <div className="input-container">
            <label className="input-label">Blood Glucose (mg/dL)</label>
            <input
              type="number"
              className="input"
              value={vitalValues.bloodGlucose}
              onChange={(e) => handleValueChange('bloodGlucose', e.target.value)}
              placeholder="Enter blood glucose"
            />
          </div>
        );
      case 'weight':
        return (
          <div className="input-container">
            <label className="input-label">Weight (kg)</label>
            <input
              type="number"
              className="input"
              value={vitalValues.weight}
              onChange={(e) => handleValueChange('weight', e.target.value)}
              placeholder="Enter weight"
            />
          </div>
        );
      case 'temperature':
        return (
          <div className="input-container">
            <label className="input-label">Temperature (°C)</label>
            <input
              type="number"
              step="0.1"
              className="input"
              value={vitalValues.temperature}
              onChange={(e) => handleValueChange('temperature', e.target.value)}
              placeholder="Enter temperature"
            />
          </div>
        );
      case 'oxygenSaturation':
        return (
          <div className="input-container">
            <label className="input-label">Oxygen Saturation (%)</label>
            <input
              type="number"
              className="input"
              value={vitalValues.oxygenSaturation}
              onChange={(e) => handleValueChange('oxygenSaturation', e.target.value)}
              placeholder="Enter oxygen saturation"
            />
          </div>
        );
      default:
        return null;
    }
  };

  if (!patient) {
    return <div className="error">Patient information not found</div>;
  }

  return (
    <div className="container">
      <div className="header">
        <h1 className="header-title">Record Vital Signs</h1>
        <p className="patient-name">
          Patient: {patient.firstName} {patient.lastName}
        </p>
      </div>

      <div className="vital-type-container">
        <h2 className="section-title">Select Vital Type</h2>
        <div className="vital-type-scroll">
          {renderVitalTypeButton('heartRate', 'Heart Rate', 'heart')}
          {renderVitalTypeButton('bloodPressure', 'Blood Pressure', 'fitness')}
          {renderVitalTypeButton('bloodGlucose', 'Blood Glucose', 'water')}
          {renderVitalTypeButton('weight', 'Weight', 'scale')}
          {renderVitalTypeButton('temperature', 'Temperature', 'thermometer')}
          {renderVitalTypeButton('oxygenSaturation', 'Oxygen', 'pulse')}
        </div>
      </div>

      <div className="form-container">
        <h2 className="section-title">Enter Values</h2>
        {renderVitalInputs()}

        <div className="input-container">
          <label className="input-label">Notes (Optional)</label>
          <textarea
            className="input notes-input"
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
            placeholder="Enter any additional notes"
          />
        </div>

        <button
          className="submit-button"
          onClick={handleSubmit}
          disabled={loading}
          style={{ backgroundColor: caregiverColors.primary }}
        >
          {loading ? (
            <div className="loading-spinner"></div>
          ) : (
            <>
              <i className="icon-save"></i>
              <span>Save Vital Signs</span>
            </>
          )}
        </button>
      </div>
    </div>
  );
};

export default CaregiverRecordVitalsScreen;
